/* ===========================
   增强样式 - 集成 Tailwind CSS & Font Awesome
   性能优化版本 v2.0
   ========================== */

/* 基础 Tailwind CSS 扩展和自定义 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 性能优化 - 关键CSS内联 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    font-feature-settings: "kern" 1, "liga" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 自定义 CSS 变量覆盖 */
:root {
  /* 使用 Tailwind 色彩系统扩展 */
  --primary-color: theme('colors.blue.600');
  --primary-dark: theme('colors.blue.700');
  --primary-light: theme('colors.blue.500');
  --primary-pale: theme('colors.blue.50');
  
  --success-color: theme('colors.green.500');
  --success-bg: theme('colors.green.50');
  
  --warning-color: theme('colors.yellow.500');
  --warning-bg: theme('colors.yellow.50');
  
  --error-color: theme('colors.red.500');
  --error-bg: theme('colors.red.50');
  
  --info-color: theme('colors.blue.500');
  --info-bg: theme('colors.blue.50');
  
  /* 背景和表面 */
  --background-color: theme('colors.gray.50');
  --surface-color: theme('colors.white');
  --surface-hover: theme('colors.gray.50');
  
  /* 文本颜色 */
  --text-primary: theme('colors.gray.900');
  --text-secondary: theme('colors.gray.600');
  --text-tertiary: theme('colors.gray.400');
  
  /* 边框和阴影 */
  --border-color: theme('colors.gray.200');
  --shadow-color: theme('colors.gray.900' / 0.1);
  
  /* 动画 */
  --animation-duration: 200ms;
  --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===========================
   增强的组件样式
   ========================== */

/* 欢迎卡片增强样式 */
.welcome-message {
  @apply max-w-4xl mx-auto;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #f1f5f9 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(10px);
}

.welcome-header {
  position: relative;
  overflow: hidden;
}

.welcome-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.5), transparent);
}

.welcome-icon-container {
  position: relative;
  display: inline-block;
}

.feature-item {
  position: relative;
  overflow: hidden;
}

.feature-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.feature-item:hover::before {
  left: 100%;
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #1f2937, #4b5563);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.welcome-icon-container {
  animation: float 3s ease-in-out infinite;
}

/* 响应式调整 - 仅在移动端应用，不影响桌面端居中布局 */
@media (max-width: 768px) {
  .new-product-container {
    /* 移动端保持垂直布局，但不强制覆盖桌面端的居中设置 */
    padding: 1rem !important;
  }

  .welcome-message {
    @apply mx-2;
    max-width: 100% !important;
  }

  .central-input-container {
    margin: 0 1rem !important;
  }

  .welcome-header .relative {
    @apply px-4 pt-6 pb-4;
  }

  .feature-item {
    @apply p-3;
  }
}

/* 新建产品容器样式 - 保持主CSS中的居中布局 */
.new-product-container {
  /* 不覆盖主CSS中的flex布局设置，只添加额外样式 */
  min-height: calc(100vh - 300px); /* 减少最小高度，给顶部和底部留更多空间 */
  padding: 2rem 1rem; /* 添加适当的内边距 */
}

.central-input-container {
  /* 移除可能影响布局的背景色设置，保持主CSS样式 */
}

/* 按钮增强 */
.btn-enhanced {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ease-in-out;
}

.btn-primary-enhanced {
  @apply btn-enhanced bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 active:bg-blue-800;
}

.btn-secondary-enhanced {
  @apply btn-enhanced bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-outline-enhanced {
  @apply btn-enhanced bg-transparent border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
}

.btn-success-enhanced {
  @apply btn-enhanced bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-warning-enhanced {
  @apply btn-enhanced bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-400;
}

.btn-danger-enhanced {
  @apply btn-enhanced bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

/* 卡片增强 */
.card-enhanced {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header-enhanced {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body-enhanced {
  @apply px-6 py-4;
}

.card-footer-enhanced {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* 表单增强 */
.form-input-enhanced {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
}

.form-label-enhanced {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.form-error-enhanced {
  @apply mt-1 text-sm text-red-600;
}

.form-group-enhanced {
  @apply mb-4;
}

/* 通知和徽章增强 */
.badge-enhanced {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary-enhanced {
  @apply badge-enhanced bg-blue-100 text-blue-800;
}

.badge-success-enhanced {
  @apply badge-enhanced bg-green-100 text-green-800;
}

.badge-warning-enhanced {
  @apply badge-enhanced bg-yellow-100 text-yellow-800;
}

.badge-danger-enhanced {
  @apply badge-enhanced bg-red-100 text-red-800;
}

.badge-secondary-enhanced {
  @apply badge-enhanced bg-gray-100 text-gray-800;
}

/* 下拉菜单增强 */
.dropdown-enhanced {
  @apply relative inline-block text-left;
}

.dropdown-menu-enhanced {
  @apply absolute right-0 z-50 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none;
}

.dropdown-item-enhanced {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer;
}

/* 模态框增强 */
.modal-enhanced {
  @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal-backdrop-enhanced {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity;
}

.modal-container-enhanced {
  @apply flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0;
}

.modal-panel-enhanced {
  @apply relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg;
}

/* 导航增强 */
.nav-enhanced {
  @apply flex space-x-8;
}

.nav-item-enhanced {
  @apply text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium cursor-pointer transition-colors duration-200;
}

.nav-item-active-enhanced {
  @apply nav-item-enhanced text-blue-600 bg-blue-50;
}

/* 表格增强 */
.table-enhanced {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header-enhanced {
  @apply bg-gray-50;
}

.table-header-cell-enhanced {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table-body-enhanced {
  @apply bg-white divide-y divide-gray-200;
}

.table-cell-enhanced {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

/* 侧边栏增强 */
.sidebar-enhanced {
  @apply flex flex-col w-64 bg-white shadow-lg border-r border-gray-200;
}

.sidebar-header-enhanced {
  @apply flex items-center justify-between p-4 border-b border-gray-200;
}

.sidebar-nav-enhanced {
  @apply flex-1 px-4 py-6 space-y-1;
}

.sidebar-nav-item-enhanced {
  @apply flex items-center px-2 py-2 text-sm font-medium rounded-md text-gray-600 hover:bg-gray-50 hover:text-gray-900 cursor-pointer transition-colors duration-200;
}

.sidebar-nav-item-active-enhanced {
  @apply sidebar-nav-item-enhanced bg-blue-50 text-blue-700 border-r-2 border-blue-700;
}

/* 头部导航增强 */
.header-enhanced {
  @apply bg-white shadow-sm border-b border-gray-200;
}

.header-container-enhanced {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.header-content-enhanced {
  @apply flex justify-between items-center py-4;
}

/* 加载状态增强 */
.loading-spinner-enhanced {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

.loading-overlay-enhanced {
  @apply fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50;
}

/* 消息提示增强 */
.alert-enhanced {
  @apply p-4 rounded-md;
}

.alert-success-enhanced {
  @apply alert-enhanced bg-green-50 border border-green-200 text-green-800;
}

.alert-warning-enhanced {
  @apply alert-enhanced bg-yellow-50 border border-yellow-200 text-yellow-800;
}

.alert-danger-enhanced {
  @apply alert-enhanced bg-red-50 border border-red-200 text-red-800;
}

.alert-info-enhanced {
  @apply alert-enhanced bg-blue-50 border border-blue-200 text-blue-800;
}

/* 工具提示增强 */
.tooltip-enhanced {
  @apply absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg;
}

/* 分页增强 */
.pagination-enhanced {
  @apply flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6;
}

.pagination-info-enhanced {
  @apply text-sm text-gray-700;
}

.pagination-nav-enhanced {
  @apply flex space-x-1;
}

.pagination-item-enhanced {
  @apply px-3 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 cursor-pointer;
}

.pagination-item-active-enhanced {
  @apply pagination-item-enhanced text-blue-600 bg-blue-50;
}

/* 搜索框增强 */
.search-enhanced {
  @apply relative;
}

.search-input-enhanced {
  @apply block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
}

.search-icon-enhanced {
  @apply absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none;
}

/* 标签增强 */
.tag-enhanced {
  @apply inline-flex items-center px-2 py-1 rounded text-xs font-medium;
}

.tag-primary-enhanced {
  @apply tag-enhanced bg-blue-100 text-blue-800;
}

.tag-success-enhanced {
  @apply tag-enhanced bg-green-100 text-green-800;
}

.tag-warning-enhanced {
  @apply tag-enhanced bg-yellow-100 text-yellow-800;
}

.tag-danger-enhanced {
  @apply tag-enhanced bg-red-100 text-red-800;
}

/* 进度条增强 */
.progress-enhanced {
  @apply w-full bg-gray-200 rounded-full h-2;
}

.progress-bar-enhanced {
  @apply bg-blue-600 h-2 rounded-full transition-all duration-300;
}

/* 头像增强 */
.avatar-enhanced {
  @apply inline-block rounded-full;
}

.avatar-sm-enhanced {
  @apply avatar-enhanced h-8 w-8;
}

.avatar-md-enhanced {
  @apply avatar-enhanced h-10 w-10;
}

.avatar-lg-enhanced {
  @apply avatar-enhanced h-12 w-12;
}

.avatar-xl-enhanced {
  @apply avatar-enhanced h-16 w-16;
}

/* 图标增强 */
.icon-enhanced {
  @apply inline-flex items-center justify-center;
}

.icon-sm-enhanced {
  @apply icon-enhanced w-4 h-4;
}

.icon-md-enhanced {
  @apply icon-enhanced w-5 h-5;
}

.icon-lg-enhanced {
  @apply icon-enhanced w-6 h-6;
}

.icon-xl-enhanced {
  @apply icon-enhanced w-8 h-8;
}

/* 响应式工具类 */
.hide-mobile {
  @apply hidden sm:block;
}

.hide-desktop {
  @apply block sm:hidden;
}

.mobile-only {
  @apply sm:hidden;
}

.desktop-only {
  @apply hidden sm:block;
}

/* 动画增强 */
.fade-in-enhanced {
  @apply animate-fade-in;
}

.slide-up-enhanced {
  @apply animate-slide-up;
}

.bounce-enhanced {
  @apply animate-bounce;
}

/* 自定义动画 */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 可访问性增强 */
.sr-only-enhanced {
  @apply sr-only;
}

.focus-visible-enhanced {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500;
}

/* 打印样式 */
@media print {
  .no-print {
    @apply hidden;
  }
  
  .print-only {
    @apply block;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .dark-mode-auto {
    @apply bg-gray-900 text-white;
  }
}

/* ===========================
   Font Awesome 图标优化
   ========================== */

/* 统一图标大小和间距 - 性能优化 */
.fa-icon {
  @apply inline-flex items-center justify-center;
  will-change: transform;
  backface-visibility: hidden;
}

.fa-icon-sm {
  @apply fa-icon w-4 h-4 text-sm;
}

.fa-icon-md {
  @apply fa-icon w-5 h-5 text-base;
}

.fa-icon-lg {
  @apply fa-icon w-6 h-6 text-lg;
}

.fa-icon-xl {
  @apply fa-icon w-8 h-8 text-xl;
}

/* 图标动画效果 */
.fa-icon-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-icon-pulse {
  animation: fa-pulse 1s infinite steps(8);
}

.fa-icon-bounce {
  animation: fa-bounce 1s infinite;
}

.fa-icon-shake {
  animation: fa-shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes fa-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fa-pulse {
  0% { opacity: 1; }
  50% { opacity: 0.4; }
  100% { opacity: 1; }
}

@keyframes fa-bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.610, 0.355, 1.000);
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -30px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.050, 0.855, 0.060);
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0,-4px,0);
  }
}

@keyframes fa-shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

/* 图标与文本组合 */
.icon-text-enhanced {
  @apply inline-flex items-center space-x-2;
}

.icon-text-sm-enhanced {
  @apply icon-text-enhanced text-sm;
}

.icon-text-md-enhanced {
  @apply icon-text-enhanced text-base;
}

.icon-text-lg-enhanced {
  @apply icon-text-enhanced text-lg;
}

/* ===========================
   主题系统兼容性
   ========================== */

/* 确保与现有主题系统兼容 */
[data-theme="modern"] {
  /* 保持现有主题变量 */
}

[data-theme="professional"] {
  /* 保持现有主题变量 */
}

[data-theme="alien"] {
  /* 保持现有主题变量 */
}

/* ===========================
   工具类扩展
   ========================== */

/* 自定义工具类 */
.glass-effect {
  @apply bg-white bg-opacity-20 backdrop-blur-sm border border-white border-opacity-30;
}

.gradient-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700;
}

.gradient-secondary {
  @apply bg-gradient-to-r from-gray-600 to-gray-700;
}

.gradient-success {
  @apply bg-gradient-to-r from-green-600 to-green-700;
}

.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

/* 阴影工具类 */
.shadow-elevated {
  @apply shadow-lg;
}

.shadow-floating {
  @apply shadow-xl;
}

.shadow-deep {
  @apply shadow-2xl;
}

/* 边框工具类 */
.border-primary {
  @apply border-blue-600;
}

.border-success {
  @apply border-green-600;
}

.border-warning {
  @apply border-yellow-600;
}

.border-danger {
  @apply border-red-600;
}

/* 文本工具类 */
.text-primary-enhanced {
  @apply text-blue-600;
}

.text-success-enhanced {
  @apply text-green-600;
}

.text-warning-enhanced {
  @apply text-yellow-600;
}

.text-danger-enhanced {
  @apply text-red-600;
}

/* 背景工具类 */
.bg-primary-enhanced {
  @apply bg-blue-600;
}

.bg-success-enhanced {
  @apply bg-green-600;
}

.bg-warning-enhanced {
  @apply bg-yellow-600;
}

.bg-danger-enhanced {
  @apply bg-red-600;
}

/* ===========================
   兼容性样式
   ========================== */

/* 确保与现有组件的兼容性 */
.product-card {
  @apply card-enhanced hover:shadow-elevated transition-shadow duration-200;
}

.menu-item {
  @apply sidebar-nav-item-enhanced;
}

.menu-item.active {
  @apply sidebar-nav-item-active-enhanced;
}

.btn-primary {
  @apply btn-primary-enhanced;
}

.btn-secondary {
  @apply btn-secondary-enhanced;
}

.btn-outline {
  @apply btn-outline-enhanced;
}

/* 通知相关 */
.notification-item {
  @apply card-enhanced p-4 hover:shadow-elevated cursor-pointer transition-all duration-200;
}

.status-tag {
  @apply badge-enhanced;
}

.status-tag.status-in-progress {
  @apply badge-warning-enhanced;
}

.status-tag.status-confirmed {
  @apply badge-success-enhanced;
}

.status-tag.status-promoting {
  @apply badge-primary-enhanced;
}

.status-tag.status-completed {
  @apply badge-secondary-enhanced;
}

/* ===========================
   优化后的响应式设计
   ========================== */

/* 移动端优化 */
@media (max-width: 640px) {
  .card-enhanced {
    @apply mx-2 rounded-lg;
  }
  
  .btn-enhanced {
    @apply w-full justify-center;
  }
  
  .modal-panel-enhanced {
    @apply mx-4;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  .sidebar-enhanced {
    @apply w-56;
  }
}

/* 桌面端优化 */
@media (min-width: 1025px) {
  .sidebar-enhanced {
    @apply w-64;
  }
  
  .header-container-enhanced {
    @apply max-w-full;
  }
}

/* ===========================
   性能优化增强版
   ========================== */

/* 减少重绘和回流 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.will-change-scroll {
  will-change: scroll-position;
}

/* GPU 加速增强 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 字体渲染优化 */
.text-optimized {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: "kern" 1, "liga" 1;
}

/* 图片懒加载优化 */
.lazy-image {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

.lazy-image.loaded {
  opacity: 1;
}

/* 滚动性能优化 */
.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 减少重绘的过渡效果 */
.optimized-transition {
  transition: transform 0.2s ease-out, opacity 0.2s ease-out;
  will-change: transform, opacity;
}

/* 内容可见性优化 */
.content-visibility-auto {
  content-visibility: auto;
  contain-intrinsic-size: 200px;
}

/* 关键渲染路径优化 */
.critical-above-fold {
  contain: layout style paint;
}

/* 交互优化 */
.touch-optimized {
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

/* 动画性能优化 */
.performance-animation {
  animation-fill-mode: both;
  animation-play-state: paused;
}

.performance-animation.animate {
  animation-play-state: running;
}

/* 分步骤分析卡片动画 */
.step-animate-in {
    opacity: 0;
    transform: translateY(20px);
    animation: stepSlideIn var(--animation-duration) var(--animation-easing) forwards;
}

.step-animate-in[data-step="1"] {
    animation-delay: 0s;
}

.step-animate-in[data-step="2"] {
    animation-delay: 0.3s;
}

@keyframes stepSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 分析步骤卡片增强样式 */
.analysis-steps {
    margin: var(--spacing-base) 0;
}

.analysis-steps .step-item {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-base);
    margin-bottom: var(--spacing-base);
    box-shadow: 0 2px 8px var(--shadow-color);
    transition: all var(--animation-duration) var(--animation-easing);
}

.analysis-steps .step-item:hover {
    box-shadow: 0 4px 16px var(--shadow-hover);
    transform: translateY(-2px);
}

.analysis-steps .step-label {
    background: var(--primary-alpha-10);
    color: var(--primary-color);
    padding: var(--spacing-sm) var(--spacing-base);
    border-bottom: 1px solid var(--border-light);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.analysis-steps .step-label i {
    color: var(--primary-color);
}

.analysis-steps .step-content {
    padding: var(--spacing-base);
}

/* 特征标签动画效果 */
.core-features .feature-tag {
    opacity: 0;
    transform: translateX(-10px);
    animation: featureTagIn 0.4s var(--animation-easing) forwards;
}

.core-features .feature-tag:nth-child(1) { animation-delay: 0.1s; }
.core-features .feature-tag:nth-child(2) { animation-delay: 0.2s; }
.core-features .feature-tag:nth-child(3) { animation-delay: 0.3s; }
.core-features .feature-tag:nth-child(4) { animation-delay: 0.4s; }
.core-features .feature-tag:nth-child(5) { animation-delay: 0.5s; }

@keyframes featureTagIn {
    from {
        opacity: 0;
        transform: translateX(-10px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 受众标签动画效果 */
.audience-tags .audience-tag {
    opacity: 0;
    transform: scale(0.8);
    animation: audienceTagIn 0.3s var(--animation-easing) forwards;
}

.audience-tags .audience-tag:nth-child(1) { animation-delay: 0.6s; }
.audience-tags .audience-tag:nth-child(2) { animation-delay: 0.7s; }
.audience-tags .audience-tag:nth-child(3) { animation-delay: 0.8s; }

@keyframes audienceTagIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 产品信息卡片增强 - 使用CSS变量覆盖基础样式 */
.product-info-card {
    background: var(--surface-elevated) !important;
}

.product-image-container {
    width: 150px !important;
    height: 150px !important;
}

.product-category-tag {
    background: var(--info-alpha-10) !important;
    color: var(--info-color) !important;
} 