// 邮箱配置切换功能
class EmailConfig {
    constructor() {
        this.currentProvider = 'qq';
        this.providerConfigs = {
            qq: {
                title: 'QQ邮箱配置指导',
                steps: [
                    '登录网页端 → 右上角 设置 → 账户',
                    '找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP',
                    '系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处'
                ],
                link: 'https://mail.qq.com',
                linkText: '点击获取授权码',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '授权码',
                authPlaceholder: '请输入QQ邮箱授权码',
                smtp: { server: 'smtp.qq.com', port: '465 (SSL)' },
                imap: { server: 'imap.qq.com', port: '993 (SSL)' }
            },
            163: {
                title: '163邮箱配置指导',
                steps: [
                    '登录网页端 → 右上角 设置 → POP3/SMTP/IMAP',
                    '开启 IMAP/SMTP 服务',
                    '设置客户端授权密码（不是登录密码）'
                ],
                link: 'https://mail.163.com',
                linkText: '点击获取授权码',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '授权码',
                authPlaceholder: '请输入163邮箱授权码',
                smtp: { server: 'smtp.163.com', port: '465 (SSL)' },
                imap: { server: 'imap.163.com', port: '993 (SSL)' }
            },
            other: {
                title: '其他邮箱配置指导',
                steps: [
                    '联系您的邮箱服务商获取SMTP/IMAP配置信息',
                    '确保已开启IMAP/SMTP服务',
                    '在下方手动填写服务器地址、端口号和加密方式'
                ],
                link: '#',
                linkText: '查看常见邮箱配置',
                emailPlaceholder: '例如: <EMAIL>',
                authLabel: '密码/授权码',
                authPlaceholder: '请输入邮箱密码或授权码',
                smtp: { server: '请手动配置', port: '请手动配置' },
                imap: { server: '请手动配置', port: '请手动配置' },
                isManual: true
            }
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateConfig(this.currentProvider);
    }

    bindEvents() {
        // 邮箱提供商下拉菜单切换
        const providerSelect = document.getElementById('email-provider');
        providerSelect.addEventListener('change', (e) => {
            this.switchProvider(e.target.value);
        });

        // 测试连接按钮
        const testButton = document.getElementById('test-btn');
        testButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.testConnection();
        });

        // 保存配置按钮
        const saveButton = document.getElementById('save-btn');
        saveButton.addEventListener('click', (e) => {
            e.preventDefault();
            this.saveConfig();
        });
    }

    switchProvider(providerType) {
        this.currentProvider = providerType;
        this.updateConfig(providerType);

        // 清空表单
        document.getElementById('email-address').value = '';
        document.getElementById('auth-code').value = '';

        // 清空手动配置字段
        if (providerType === 'other') {
            this.clearManualConfig();
        }
    }

    updateConfig(providerType) {
        const config = this.providerConfigs[providerType];
        if (!config) return;

        // 更新指导内容
        document.getElementById('guidance-title').textContent = config.title;

        const stepsList = document.getElementById('guidance-steps');
        stepsList.innerHTML = '';
        config.steps.forEach(step => {
            const li = document.createElement('li');
            li.textContent = step;
            stepsList.appendChild(li);
        });

        const guidanceLink = document.getElementById('guidance-link');
        guidanceLink.href = config.link;
        guidanceLink.textContent = config.linkText;

        // 更新表单
        document.getElementById('email-address').placeholder = config.emailPlaceholder;
        document.getElementById('auth-code').placeholder = config.authPlaceholder;

        // 切换配置区域显示
        const autoConfigSection = document.getElementById('auto-config-section');
        const manualConfigSection = document.getElementById('manual-config-section');

        if (config.isManual) {
            // 显示手动配置，隐藏自动配置
            autoConfigSection.style.display = 'none';
            manualConfigSection.style.display = 'block';
        } else {
            // 显示自动配置，隐藏手动配置
            autoConfigSection.style.display = 'block';
            manualConfigSection.style.display = 'none';

            // 更新自动配置服务器信息
            document.getElementById('smtp-server').textContent = config.smtp.server;
            document.getElementById('smtp-port').textContent = config.smtp.port;
            document.getElementById('imap-server').textContent = config.imap.server;
            document.getElementById('imap-port').textContent = config.imap.port;
        }
    }

    validateForm() {
        const emailInput = document.getElementById('email-address');
        const authInput = document.getElementById('auth-code');

        let isValid = true;
        let errorMessage = '';

        // 验证邮箱
        if (!emailInput.value.trim()) {
            errorMessage = '请输入邮箱地址';
            isValid = false;
        } else if (!this.isValidEmail(emailInput.value)) {
            errorMessage = '请输入有效的邮箱地址';
            isValid = false;
        }

        // 验证密码/授权码
        if (!authInput.value.trim()) {
            errorMessage = '请输入密码或授权码';
            isValid = false;
        }

        // 如果是手动配置，验证手动配置字段
        if (this.currentProvider === 'other') {
            const manualValidation = this.validateManualConfig();
            if (!manualValidation.isValid) {
                errorMessage = manualValidation.errorMessage;
                isValid = false;
            }
        }

        if (!isValid) {
            this.showMessage(errorMessage, 'error');
        }

        return isValid;
    }

    validateManualConfig() {
        const smtpServer = document.getElementById('manual-smtp-server').value.trim();
        const smtpPort = document.getElementById('manual-smtp-port').value.trim();
        const imapServer = document.getElementById('manual-imap-server').value.trim();
        const imapPort = document.getElementById('manual-imap-port').value.trim();

        if (!smtpServer) {
            return { isValid: false, errorMessage: '请输入SMTP服务器地址' };
        }
        if (!smtpPort) {
            return { isValid: false, errorMessage: '请输入SMTP端口号' };
        }
        if (!imapServer) {
            return { isValid: false, errorMessage: '请输入IMAP服务器地址' };
        }
        if (!imapPort) {
            return { isValid: false, errorMessage: '请输入IMAP端口号' };
        }

        // 验证端口号格式
        if (!/^\d+$/.test(smtpPort) || parseInt(smtpPort) < 1 || parseInt(smtpPort) > 65535) {
            return { isValid: false, errorMessage: 'SMTP端口号格式不正确（1-65535）' };
        }
        if (!/^\d+$/.test(imapPort) || parseInt(imapPort) < 1 || parseInt(imapPort) > 65535) {
            return { isValid: false, errorMessage: 'IMAP端口号格式不正确（1-65535）' };
        }

        return { isValid: true };
    }

    clearManualConfig() {
        document.getElementById('manual-smtp-server').value = '';
        document.getElementById('manual-smtp-port').value = '';
        document.getElementById('manual-smtp-encryption').value = 'ssl';
        document.getElementById('manual-imap-server').value = '';
        document.getElementById('manual-imap-port').value = '';
        document.getElementById('manual-imap-encryption').value = 'ssl';
    }

    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    testConnection() {
        if (!this.validateForm()) return;

        const testButton = document.getElementById('test-btn');
        const originalText = testButton.textContent;

        // 显示加载状态
        testButton.textContent = '测试中...';
        testButton.disabled = true;

        // 模拟测试连接
        setTimeout(() => {
            testButton.textContent = originalText;
            testButton.disabled = false;

            // 模拟测试结果
            const success = Math.random() > 0.3; // 70%成功率
            if (success) {
                this.showMessage('连接测试成功！', 'success');
            } else {
                this.showMessage('连接测试失败，请检查配置信息', 'error');
            }
        }, 2000);
    }

    saveConfig() {
        if (!this.validateForm()) return;

        const saveButton = document.getElementById('save-btn');
        const originalText = saveButton.textContent;

        // 显示加载状态
        saveButton.textContent = '保存中...';
        saveButton.disabled = true;

        // 获取表单数据
        const formData = this.getFormData();

        // 模拟保存配置
        setTimeout(() => {
            saveButton.textContent = originalText;
            saveButton.disabled = false;

            // 保存到本地存储
            localStorage.setItem('emailConfig', JSON.stringify({
                provider: this.currentProvider,
                data: formData,
                timestamp: new Date().toISOString()
            }));

            this.showMessage('配置保存成功！', 'success');
        }, 1500);
    }

    getFormData() {
        const baseData = {
            emailAddress: document.getElementById('email-address').value,
            authCode: document.getElementById('auth-code').value,
            provider: this.currentProvider
        };

        if (this.currentProvider === 'other') {
            // 手动配置数据
            return {
                ...baseData,
                smtpServer: document.getElementById('manual-smtp-server').value,
                smtpPort: document.getElementById('manual-smtp-port').value,
                smtpEncryption: document.getElementById('manual-smtp-encryption').value,
                imapServer: document.getElementById('manual-imap-server').value,
                imapPort: document.getElementById('manual-imap-port').value,
                imapEncryption: document.getElementById('manual-imap-encryption').value,
                isManualConfig: true
            };
        } else {
            // 自动配置数据
            return {
                ...baseData,
                smtpServer: document.getElementById('smtp-server').textContent,
                smtpPort: document.getElementById('smtp-port').textContent,
                imapServer: document.getElementById('imap-server').textContent,
                imapPort: document.getElementById('imap-port').textContent,
                isManualConfig: false
            };
        }
    }

    showMessage(message, type = 'info') {
        // 移除现有消息
        const existingMessage = document.querySelector('.email-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `email-message email-message-${type}`;
        messageEl.textContent = message;

        // 插入到消息容器中
        const messageContainer = document.getElementById('email-message-container');
        if (messageContainer) {
            messageContainer.appendChild(messageEl);
        } else {
            // 如果没有消息容器，插入到卡片顶部
            const card = document.querySelector('.email-config-card');
            card.insertBefore(messageEl, card.firstChild);
        }

        // 自动移除消息
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 3000);
    }

    // 加载保存的配置
    loadSavedConfig() {
        const saved = localStorage.getItem('emailConfig');
        if (saved) {
            try {
                const config = JSON.parse(saved);

                // 设置下拉菜单值
                document.getElementById('email-provider').value = config.provider;
                this.switchProvider(config.provider);

                // 填充表单数据
                if (config.data) {
                    document.getElementById('email-address').value = config.data.emailAddress || '';
                    document.getElementById('auth-code').value = config.data.authCode || '';

                    // 如果是手动配置，填充手动配置字段
                    if (config.data.isManualConfig && config.provider === 'other') {
                        document.getElementById('manual-smtp-server').value = config.data.smtpServer || '';
                        document.getElementById('manual-smtp-port').value = config.data.smtpPort || '';
                        document.getElementById('manual-smtp-encryption').value = config.data.smtpEncryption || 'ssl';
                        document.getElementById('manual-imap-server').value = config.data.imapServer || '';
                        document.getElementById('manual-imap-port').value = config.data.imapPort || '';
                        document.getElementById('manual-imap-encryption').value = config.data.imapEncryption || 'ssl';
                    }
                }
            } catch (e) {
                console.error('加载配置失败:', e);
            }
        }
    }
}

// 账号设置管理类
class AccountManager {
    constructor() {
        this.currentState = 'phone-only'; // 默认状态
        this.countdownTimer = null; // 验证码倒计时定时器
        this.userStates = {
            'phone-only': {
                showPhone: true,
                showUsername: false,
                showGoogle: false,
                passwordSet: false,
                showBindPhone: false,
                showBindGoogle: true,
                googleBound: false,
                phoneBound: true
            },
            'phone-password': {
                showPhone: true,
                showUsername: true,
                showGoogle: false,
                passwordSet: true,
                showBindPhone: false,
                showBindGoogle: true,
                googleBound: false,
                phoneBound: true
            },
            'google-only': {
                showPhone: false,
                showUsername: false,
                showGoogle: true,
                passwordSet: false,
                showBindPhone: true,
                showBindGoogle: false,
                googleBound: true,
                phoneBound: false
            },
            'full-account': {
                showPhone: true,
                showUsername: true,
                showGoogle: true,
                passwordSet: true,
                showBindPhone: false,
                showBindGoogle: false,
                googleBound: true,
                phoneBound: true
            }
        };
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateAccountDisplay();
        this.setupRealTimeValidation();
        this.setupFormDisableLogic();
    }

    bindEvents() {
        // 页签切换
        const tabButtons = document.querySelectorAll('.tab-btn');
        tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.closest('.tab-btn').dataset.tab);
            });
        });

        // 状态切换按钮
        const stateButtons = document.querySelectorAll('.state-btn');
        stateButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchState(e.target.dataset.state);
            });
        });

        // 账号操作按钮
        this.bindAccountActions();

        // 弹窗相关事件
        this.bindModalEvents();
    }

    switchTab(tabName) {
        // 更新页签按钮状态
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新页签内容显示
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`${tabName}-tab`).classList.add('active');

        // 控制外部状态切换器的显示/隐藏
        const externalSwitcher = document.getElementById('external-state-switcher');
        if (externalSwitcher) {
            if (tabName === 'account') {
                // 显示状态切换器
                externalSwitcher.classList.add('visible');
            } else {
                // 隐藏状态切换器
                externalSwitcher.classList.remove('visible');
            }
        }
    }

    switchState(stateName) {
        this.currentState = stateName;

        // 更新状态按钮
        document.querySelectorAll('.state-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-state="${stateName}"]`).classList.add('active');

        // 更新界面显示
        this.updateAccountDisplay();
    }

    updateAccountDisplay() {
        const state = this.userStates[this.currentState];

        // 更新账号信息显示
        this.toggleElement('.phone-info', state.showPhone);
        this.toggleElement('.username-info', state.showUsername);
        this.toggleElement('.google-info', state.showGoogle);

        // 更新密码状态
        this.toggleElement('.password-status.not-set', !state.passwordSet);
        this.toggleElement('.password-status.set', state.passwordSet);

        // 更新绑定按钮和状态显示
        this.toggleElement('.bind-phone-btn', state.showBindPhone);
        this.toggleElement('.bind-google-btn', state.showBindGoogle);

        // 更新绑定状态显示
        this.toggleElement('.phone-binding-status', state.phoneBound);
        this.toggleElement('.google-binding-status', state.googleBound);

        // 根据是否有绑定状态或按钮来决定是否显示对应区域
        const hasBindingStatus = state.phoneBound || state.googleBound;
        const hasBindingActions = state.showBindPhone || state.showBindGoogle;

        this.toggleElement('.binding-status', hasBindingStatus);
        this.toggleElement('.binding-actions', hasBindingActions);

        // 如果既没有绑定状态也没有绑定按钮，隐藏整个绑定区域
        const bindingSection = document.querySelector('.binding-section');
        if (bindingSection) {
            if (!hasBindingStatus && !hasBindingActions) {
                bindingSection.style.display = 'none';
            } else {
                bindingSection.style.display = 'block';
            }
        }
    }

    toggleElement(selector, show) {
        const element = document.querySelector(selector);
        if (element) {
            if (show) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        }
    }

    bindAccountActions() {
        // 设置密码
        const setPasswordBtn = document.querySelector('.set-password-btn');
        if (setPasswordBtn) {
            setPasswordBtn.addEventListener('click', () => {
                this.handleSetPasswordClick();
            });
        }

        // 修改密码
        const changePasswordBtn = document.querySelector('.change-password-btn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => {
                this.openModal('change-password-modal');
            });
        }

        // 绑定手机号
        const bindPhoneBtn = document.querySelector('.bind-phone-btn');
        if (bindPhoneBtn) {
            bindPhoneBtn.addEventListener('click', () => {
                this.openModal('bind-phone-modal');
            });
        }

        // 绑定Google账号
        const bindGoogleBtn = document.querySelector('.bind-google-btn');
        if (bindGoogleBtn) {
            bindGoogleBtn.addEventListener('click', () => {
                this.openModal('bind-google-modal');
            });
        }
    }

    bindModalEvents() {
        // 弹窗遮罩层点击关闭
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.addEventListener('click', (e) => {
                if (e.target === modalOverlay) {
                    this.closeModal();
                }
            });
        }

        // 关闭按钮
        const closeButtons = document.querySelectorAll('.modal-close-btn, [data-modal]');
        closeButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                if (btn.classList.contains('email-btn-secondary') || btn.classList.contains('modal-close-btn')) {
                    this.closeModal();
                }
            });
        });

        // 密码可见性切换
        const passwordToggles = document.querySelectorAll('.password-toggle');
        passwordToggles.forEach(toggle => {
            toggle.addEventListener('click', (e) => {
                this.togglePasswordVisibility(e.target.closest('.password-toggle').dataset.target);
            });
        });

        // 发送验证码按钮
        const sendCodeBtn = document.getElementById('send-bind-code');
        if (sendCodeBtn) {
            sendCodeBtn.addEventListener('click', () => {
                this.sendVerificationCode();
            });
        }

        // 确认操作按钮
        this.bindConfirmActions();
    }

    bindConfirmActions() {
        // 确认设置密码
        const confirmSetPassword = document.getElementById('confirm-set-password');
        if (confirmSetPassword) {
            confirmSetPassword.addEventListener('click', () => {
                this.handleSetPassword();
            });
        }

        // 确认修改密码
        const confirmChangePassword = document.getElementById('confirm-change-password');
        if (confirmChangePassword) {
            confirmChangePassword.addEventListener('click', () => {
                this.handleChangePassword();
            });
        }

        // 确认绑定手机号
        const confirmBindPhone = document.getElementById('confirm-bind-phone');
        if (confirmBindPhone) {
            confirmBindPhone.addEventListener('click', () => {
                this.handleBindPhone();
            });
        }

        // 确认绑定Google账号
        const confirmBindGoogle = document.getElementById('confirm-bind-google');
        if (confirmBindGoogle) {
            confirmBindGoogle.addEventListener('click', () => {
                this.handleBindGoogle();
            });
        }
    }

    openModal(modalId) {
        const modalOverlay = document.getElementById('modal-overlay');
        const modal = document.getElementById(modalId);

        if (modalOverlay && modal) {
            // 隐藏所有弹窗
            document.querySelectorAll('.modal').forEach(m => {
                m.style.display = 'none';
            });

            // 显示目标弹窗
            modal.style.display = 'block';
            modalOverlay.classList.add('active');

            // 聚焦到第一个输入框
            const firstInput = modal.querySelector('input');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 300);
            }
        }
    }

    closeModal() {
        const modalOverlay = document.getElementById('modal-overlay');
        if (modalOverlay) {
            modalOverlay.classList.remove('active');

            // 隐藏密码设置提示
            this.hidePasswordSetupTip();

            // 清空所有表单
            setTimeout(() => {
                document.querySelectorAll('.modal input').forEach(input => {
                    input.value = '';
                    input.type = input.dataset.originalType || input.type;
                });

                // 重置密码切换按钮
                document.querySelectorAll('.password-toggle i').forEach(icon => {
                    icon.className = 'fas fa-eye';
                });
            }, 300);
        }
    }

    togglePasswordVisibility(targetId) {
        const input = document.getElementById(targetId);
        const toggle = document.querySelector(`[data-target="${targetId}"] i`);

        if (input && toggle) {
            if (input.type === 'password') {
                input.type = 'text';
                toggle.className = 'fas fa-eye-slash';
            } else {
                input.type = 'password';
                toggle.className = 'fas fa-eye';
            }
        }
    }

    sendVerificationCode() {
        const phoneInput = document.getElementById('bind-phone-number');
        const sendBtn = document.getElementById('send-bind-code');

        if (!phoneInput.value.trim()) {
            this.showMessage('请输入手机号', 'error');
            return;
        }

        if (!this.isValidPhone(phoneInput.value)) {
            this.showMessage('请输入正确的手机号格式', 'error');
            return;
        }

        // 3.1 详细错误提示 - 检查手机号是否已被绑定
        const boundPhones = ['13888888888', '13999999999'];
        if (boundPhones.includes(phoneInput.value)) {
            this.showMessage('该手机号已被其他账号绑定，请更换', 'error');
            return;
        }

        // 2.2 验证码处理机制 - 使用新的倒计时方法
        this.startVerificationCountdown(sendBtn, 60);
        
        // 模拟5分钟有效期
        setTimeout(() => {
            this.showMessage('验证码已过期，请重新获取（验证码有效期为5分钟）', 'warning');
        }, 5 * 60 * 1000);

        this.showMessage('验证码已发送至您的手机，请查收', 'success');
    }

    handleSetPassword() {
        // 1.3 密码设置前置检查
        if (!this.checkPasswordSetupPreconditions()) {
            return;
        }

        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        if (!this.validatePassword(newPassword, confirmPassword)) {
            return;
        }

        // 模拟设置密码
        const previousState = this.currentState;
        setTimeout(() => {
            this.showMessage('密码设置成功！', 'success');
            this.closeModal();

            // 更新状态（如果当前是仅手机号用户，切换到手机号+密码用户）
            let newState = this.currentState;
            if (this.currentState === 'phone-only') {
                newState = 'phone-password';
                this.switchState(newState);
            }

            // 4.2 动态界面更新
            this.updateUIAfterStateChange(previousState, newState);
        }, 1000);
    }

    handleChangePassword() {
        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password-change').value;
        const confirmPassword = document.getElementById('confirm-password-change').value;

        if (!currentPassword.trim()) {
            this.showMessage('请输入当前密码', 'error');
            return;
        }

        if (!this.validatePassword(newPassword, confirmPassword)) {
            return;
        }

        // 模拟修改密码
        setTimeout(() => {
            this.showMessage('密码修改成功！', 'success');
            this.closeModal();
        }, 1000);
    }

    handleBindPhone() {
        const phoneNumber = document.getElementById('bind-phone-number').value;
        const verificationCode = document.getElementById('bind-phone-code').value;

        // 3.1 详细错误提示
        if (!phoneNumber.trim()) {
            this.showMessage('请输入手机号', 'error');
            return;
        }

        if (!this.isValidPhone(phoneNumber)) {
            this.showMessage('请输入正确的手机号格式', 'error');
            return;
        }

        // 检查手机号是否已被绑定
        const boundPhones = ['13888888888', '13999999999'];
        if (boundPhones.includes(phoneNumber)) {
            this.showMessage('该手机号已被其他账号绑定，请更换', 'error');
            return;
        }

        if (!verificationCode.trim()) {
            this.showMessage('请输入验证码', 'error');
            return;
        }

        // 6.2 数据格式验证 - 验证码格式检查
        if (!this.validateVerificationCode(verificationCode)) {
            this.showMessage('验证码格式错误，应为6位数字', 'error');
            return;
        }

        // 模拟验证码验证
        if (verificationCode !== '123456') {
            this.showMessage('验证码输入错误，请重新输入', 'error');
            return;
        }

        // 模拟绑定手机号
        const previousState = this.currentState;
        setTimeout(() => {
            this.showMessage('手机号绑定成功！', 'success');
            this.closeModal();

            // 更新状态（如果当前是仅Google用户，切换到完整账号用户）
            let newState = this.currentState;
            if (this.currentState === 'google-only') {
                newState = 'full-account';
                this.switchState(newState);
            }

            // 4.2 动态界面更新
            this.updateUIAfterStateChange(previousState, newState);
        }, 1000);
    }

    handleBindGoogle() {
        // 模拟Google授权流程
        this.showMessage('正在跳转到Google授权页面...', 'info');

        // 模拟Google授权可能的错误情况
        const random = Math.random();
        
        setTimeout(() => {
            // 3.1 详细错误提示 - Google绑定相关错误
            if (random < 0.1) {
                this.showMessage('Google账号授权失败，请重试', 'error');
                return;
            }
            
            if (random < 0.2) {
                this.showMessage('该Google账号已被其他用户绑定', 'error');
                return;
            }
            
            if (random < 0.3) {
                this.showMessage('网络连接异常，请检查网络后重试', 'error');
                return;
            }

            // 成功绑定
            const previousState = this.currentState;
            this.showMessage('Google账号绑定成功！', 'success');
            this.closeModal();

            // 更新状态
            let newState = this.currentState;
            if (this.currentState === 'phone-only') {
                newState = 'phone-password';
                this.switchState(newState);
            } else if (this.currentState === 'phone-password') {
                newState = 'full-account';
                this.switchState(newState);
            }

            // 4.2 动态界面更新
            this.updateUIAfterStateChange(previousState, newState);
        }, 2000);
    }

    validatePassword(password, confirmPassword) {
        if (!password.trim()) {
            this.showMessage('请输入密码', 'error');
            return false;
        }

        // 5.2 密码强度验证 - 简化为只检查长度（8-20位）
        if (password.length < 8 || password.length > 20) {
            this.showMessage('密码长度必须为8-20位', 'error');
            return false;
        }

        if (!confirmPassword.trim()) {
            this.showMessage('请确认密码', 'error');
            return false;
        }

        if (password !== confirmPassword) {
            this.showMessage('两次输入的密码不一致，请检查', 'error');
            return false;
        }

        return true;
    }

    isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }

    showMessage(message, type = 'info') {
        // 复用邮箱配置的消息显示方法
        const existingMessage = document.querySelector('.email-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        const messageEl = document.createElement('div');
        messageEl.className = `email-message email-message-${type}`;
        messageEl.textContent = message;

        const messageContainer = document.getElementById('email-message-container');
        if (messageContainer) {
            messageContainer.appendChild(messageEl);
        }

        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.remove();
            }
        }, 3000);
    }

    // 1.3 密码设置前置检查
    checkPasswordSetupPreconditions() {
        const state = this.userStates[this.currentState];
        
        // Google用户必须先绑定手机号才能设置密码
        if (this.currentState === 'google-only') {
            if (!state.phoneBound) {
                this.showMessage('设置密码前，请先绑定手机号', 'warning');
                return false;
            }
        }
        
        return true;
    }

    // 2.1 实时验证反馈 - 密码长度检测
    setupPasswordRealTimeValidation(passwordInputId, confirmPasswordId = null) {
        const passwordInput = document.getElementById(passwordInputId);
        if (!passwordInput) return;

        // 创建提示元素
        let feedbackEl = passwordInput.parentNode.querySelector('.password-feedback');
        if (!feedbackEl) {
            feedbackEl = document.createElement('div');
            feedbackEl.className = 'password-feedback';
            passwordInput.parentNode.appendChild(feedbackEl);
        }

        passwordInput.addEventListener('input', (e) => {
            const password = e.target.value;
            this.validatePasswordRealTime(password, feedbackEl);
            
            // 同时验证确认密码
            if (confirmPasswordId) {
                const confirmInput = document.getElementById(confirmPasswordId);
                if (confirmInput && confirmInput.value) {
                    this.validateConfirmPasswordRealTime(password, confirmInput.value, confirmInput);
                }
            }
        });

        // 确认密码实时验证
        if (confirmPasswordId) {
            const confirmInput = document.getElementById(confirmPasswordId);
            if (confirmInput) {
                let confirmFeedbackEl = confirmInput.parentNode.querySelector('.password-feedback');
                if (!confirmFeedbackEl) {
                    confirmFeedbackEl = document.createElement('div');
                    confirmFeedbackEl.className = 'password-feedback';
                    confirmInput.parentNode.appendChild(confirmFeedbackEl);
                }

                confirmInput.addEventListener('input', (e) => {
                    this.validateConfirmPasswordRealTime(passwordInput.value, e.target.value, confirmFeedbackEl);
                });
            }
        }
    }

    // 实时密码长度验证
    validatePasswordRealTime(password, feedbackElement) {
        if (!password) {
            feedbackElement.textContent = '';
            feedbackElement.className = 'password-feedback';
            return;
        }

        if (password.length < 8) {
            feedbackElement.textContent = '密码长度不能少于8位';
            feedbackElement.className = 'password-feedback error';
        } else if (password.length > 20) {
            feedbackElement.textContent = '密码长度不能超过20位';
            feedbackElement.className = 'password-feedback error';
        } else {
            feedbackElement.textContent = '✓ 密码长度符合要求';
            feedbackElement.className = 'password-feedback success';
        }
    }

    // 实时确认密码验证
    validateConfirmPasswordRealTime(password, confirmPassword, feedbackElement) {
        if (!confirmPassword) {
            feedbackElement.textContent = '';
            feedbackElement.className = 'password-feedback';
            return;
        }

        if (password !== confirmPassword) {
            feedbackElement.textContent = '两次输入的密码不一致';
            feedbackElement.className = 'password-feedback error';
        } else {
            feedbackElement.textContent = '✓ 密码一致';
            feedbackElement.className = 'password-feedback success';
        }
    }

    // 2.1 实时验证反馈 - 手机号格式检测
    setupPhoneRealTimeValidation(phoneInputId) {
        const phoneInput = document.getElementById(phoneInputId);
        if (!phoneInput) return;

        let feedbackEl = phoneInput.parentNode.querySelector('.phone-feedback');
        if (!feedbackEl) {
            feedbackEl = document.createElement('div');
            feedbackEl.className = 'phone-feedback';
            phoneInput.parentNode.appendChild(feedbackEl);
        }

        phoneInput.addEventListener('blur', (e) => {
            const phone = e.target.value.trim();
            this.validatePhoneRealTime(phone, feedbackEl);
        });

        phoneInput.addEventListener('input', (e) => {
            // 清空之前的反馈
            if (feedbackEl.textContent) {
                feedbackEl.textContent = '';
                feedbackEl.className = 'phone-feedback';
            }
        });
    }

    // 实时手机号验证
    validatePhoneRealTime(phone, feedbackElement) {
        if (!phone) {
            feedbackElement.textContent = '';
            feedbackElement.className = 'phone-feedback';
            return;
        }

        if (!this.isValidPhone(phone)) {
            feedbackElement.textContent = '请输入正确的手机号格式';
            feedbackElement.className = 'phone-feedback error';
        } else {
            // 模拟检查手机号是否已被绑定
            const boundPhones = ['13888888888', '13999999999']; // 模拟已绑定的手机号
            if (boundPhones.includes(phone)) {
                feedbackElement.textContent = '该手机号已被其他账号绑定，请更换';
                feedbackElement.className = 'phone-feedback error';
            } else {
                feedbackElement.textContent = '✓ 手机号格式正确';
                feedbackElement.className = 'phone-feedback success';
            }
        }
    }

    // 2.2 验证码处理机制 - 倒计时和有效期
    startVerificationCountdown(buttonElement, seconds = 60) {
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
        }

        buttonElement.disabled = true;
        let countdown = seconds;
        buttonElement.textContent = `${countdown}s后重发`;

        this.countdownTimer = setInterval(() => {
            countdown--;
            if (countdown > 0) {
                buttonElement.textContent = `${countdown}s后重发`;
            } else {
                clearInterval(this.countdownTimer);
                this.countdownTimer = null;
                buttonElement.disabled = false;
                buttonElement.textContent = '发送验证码';
            }
        }, 1000);
    }

    // 验证码格式验证（6位数字）
    validateVerificationCode(code) {
        const codeRegex = /^\d{6}$/;
        return codeRegex.test(code);
    }

    // 3.2 表单禁用逻辑
    setupFormDisableLogic() {
        // 设置密码表单禁用逻辑
        this.setupPasswordFormDisable();
        // 手机号绑定表单禁用逻辑
        this.setupPhoneBindFormDisable();
    }

    setupPasswordFormDisable() {
        const newPasswordInput = document.getElementById('new-password');
        const confirmPasswordInput = document.getElementById('confirm-password');
        const setPasswordBtn = document.getElementById('confirm-set-password');

        if (newPasswordInput && confirmPasswordInput && setPasswordBtn) {
            const validateForm = () => {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                const isValid = newPassword.length >= 8 && 
                               newPassword.length <= 20 && 
                               confirmPassword && 
                               newPassword === confirmPassword;
                
                setPasswordBtn.disabled = !isValid;
                setPasswordBtn.title = isValid ? '' : '请完善密码信息';
            };

            newPasswordInput.addEventListener('input', validateForm);
            confirmPasswordInput.addEventListener('input', validateForm);
        }
    }

    setupPhoneBindFormDisable() {
        const phoneInput = document.getElementById('bind-phone-number');
        const codeInput = document.getElementById('bind-phone-code');
        const bindBtn = document.getElementById('confirm-bind-phone');

        if (phoneInput && codeInput && bindBtn) {
            const validateForm = () => {
                const phone = phoneInput.value;
                const code = codeInput.value;
                
                const isValid = this.isValidPhone(phone) && 
                               this.validateVerificationCode(code);
                
                bindBtn.disabled = !isValid;
                bindBtn.title = isValid ? '' : '请完善手机号和验证码信息';
            };

            phoneInput.addEventListener('input', validateForm);
            codeInput.addEventListener('input', validateForm);
        }
    }

    // 4.1 状态提示和引导
    showAccountTips() {
        const state = this.userStates[this.currentState];
        
        // 显示当前账号信息提示
        if (this.currentState === 'phone-only' && !state.passwordSet) {
            this.showMessage('设置密码后，您可以使用手机号作为账号进行密码登录', 'info');
        }
        
        if (this.currentState === 'google-only' && !state.phoneBound) {
            this.showMessage('绑定手机号后可设置登录密码，提供更多登录方式', 'info');
        }
    }

    // 4.2 动态界面更新
    updateUIAfterStateChange(fromState, toState) {
        // 绑定手机号成功后的界面更新
        if (fromState === 'google-only' && toState === 'full-account') {
            this.showMessage('手机号绑定成功！现在可以设置登录密码了', 'success');
            setTimeout(() => {
                this.showAccountTips();
            }, 2000);
        }
        
        // 设置密码成功后的界面更新
        if (!this.userStates[fromState].passwordSet && this.userStates[toState].passwordSet) {
            const phoneNumber = this.getCurrentPhoneNumber();
            this.showMessage(`设置密码成功！您的登录账号是：${phoneNumber}`, 'success');
        }
    }

    // 获取当前手机号（模拟）
    getCurrentPhoneNumber() {
        // 根据用户状态返回相应的手机号
        const phoneNumbers = {
            'phone-only': '138****5678',      // 手机号登录用户
            'phone-password': '138****5678',  // 手机号+密码用户  
            'google-only': '139****1234',     // Google用户绑定的手机号
            'full-account': '139****1234'     // 完整账号用户
        };
        
        return phoneNumbers[this.currentState] || '138****5678';
    }

    // 初始化实时验证
    setupRealTimeValidation() {
        // 2.1 实时验证反馈 - 为所有密码输入框设置实时验证
        this.setupPasswordRealTimeValidation('new-password', 'confirm-password');
        this.setupPasswordRealTimeValidation('new-password-change', 'confirm-password-change');
        this.setupPasswordRealTimeValidation('current-password');
        
        // 为手机号输入框设置实时验证
        this.setupPhoneRealTimeValidation('bind-phone-number');
    }

    // 处理设置密码按钮点击
    handleSetPasswordClick() {
        const state = this.userStates[this.currentState];
        
        // 1. Google用户必须先绑定手机号
        if (this.currentState === 'google-only') {
            if (!state.phoneBound) {
                this.showMessage('设置密码前，请先绑定手机号', 'warning');
                return;
            }
        }
        
        // 打开设置密码弹窗
        this.openModal('set-password-modal');
        
        // 弹窗打开后显示提示
        setTimeout(() => {
            // 2. 手机号登录用户设置密码时的提示
            if (this.currentState === 'phone-only') {
                const phoneNumber = this.getCurrentPhoneNumber();
                this.showPasswordSetupTip(phoneNumber);
            }
            
            // 3. Google用户已绑定手机号后设置密码的提示
            if (this.currentState === 'google-only' && state.phoneBound) {
                const phoneNumber = this.getCurrentPhoneNumber();
                this.showPasswordSetupTip(phoneNumber);
            }
        }, 100);
    }

    // 显示密码设置提示
    showPasswordSetupTip(phoneNumber) {
        // 在弹窗内显示账号提示
        const tipSection = document.getElementById('password-setup-tip');
        const tipText = document.getElementById('password-setup-tip-text');
        
        if (tipSection && tipText) {
            tipText.textContent = `设置密码后，您的登录账号将是：${phoneNumber}，可使用该账号进行密码登录`;
            tipSection.style.display = 'block';
        }
    }

    // 隐藏密码设置提示
    hidePasswordSetupTip() {
        const tipSection = document.getElementById('password-setup-tip');
        if (tipSection) {
            tipSection.style.display = 'none';
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    const emailConfig = new EmailConfig();
    emailConfig.loadSavedConfig();

    const accountManager = new AccountManager();
});