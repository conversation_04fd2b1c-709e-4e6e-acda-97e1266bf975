<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置邮箱</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- 邮箱配置页面 -->
    <div class="email-config-container">
        <!-- 背景装饰元素 -->
        <div class="email-background-blur"></div>
        <div class="email-background-shapes">
            <div class="bg-shape bg-shape-1"></div>
            <div class="bg-shape bg-shape-2"></div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content-wrapper">
            <!-- 个人账户中心卡片 -->
        <div class="email-config-card">
            <div class="email-header">
                <!-- 关闭按钮 -->
                <button class="close-btn" onclick="window.close()">×</button>

                <!-- Logo 区域 -->
                <div class="email-logo">
                    <div class="logo-icon-container">
                        <i class="fas fa-user-cog logo-icon-main"></i>
                    </div>
                </div>
            </div>

            <div class="email-content">
                <div class="email-title-section">
                    <h1 class="email-main-title">个人账户中心</h1>
                    <p class="email-subtitle">管理您的邮箱配置和账号设置</p>
                </div>

                <!-- 页签导航 -->
                <div class="account-tabs">
                    <button class="tab-btn active" data-tab="email">
                        <i class="fas fa-envelope"></i>
                        <span>邮箱设置</span>
                    </button>
                    <button class="tab-btn" data-tab="account">
                        <i class="fas fa-user"></i>
                        <span>账号设置</span>
                    </button>
                </div>

                <!-- 邮箱设置页签内容 -->
                <div class="tab-content active" id="email-tab">
                    <div class="email-form">
                    <!-- 邮箱类型下拉选择 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-server"></i>
                        </div>
                        <div class="input-divider"></div>
                        <select id="email-provider" class="email-input-select">
                            <option value="qq">QQ邮箱</option>
                            <option value="163">163邮箱</option>
                            <option value="other">其他邮箱</option>
                        </select>
                    </div>

                    <!-- 配置指导区域 -->
                    <div class="guidance-wrapper" id="provider-guidance">
                        <div class="guidance-content">
                            <div class="guidance-header">
                                <i class="fas fa-info-circle"></i>
                                <span id="guidance-title">QQ邮箱配置指导</span>
                            </div>
                            <ol id="guidance-steps" class="guidance-steps">
                                <li>登录网页端 → 右上角 设置 → 账户</li>
                                <li>找到「POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV 服务」，点击 开启 IMAP/SMTP</li>
                                <li>系统会弹出短信二次验证，验证后生成一次性 16 位授权码，复制保存，填到下方授权码处</li>
                            </ol>
                            <a href="https://mail.qq.com" target="_blank" class="auth-code-link" id="guidance-link">
                                <i class="fas fa-external-link-alt"></i>
                                点击获取授权码
                            </a>
                        </div>
                    </div>

                    <!-- 邮箱地址输入 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="email" id="email-address" placeholder="例如: <EMAIL>" class="email-input">
                    </div>

                    <!-- 授权码输入 -->
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="auth-code" placeholder="请输入QQ邮箱授权码" class="email-input">
                    </div>

                    <!-- 自动配置参数 -->
                    <div class="auto-config-section" id="auto-config-section">
                        <div class="config-title">
                            <i class="fas fa-cog"></i>
                            <span>自动配置参数</span>
                        </div>
                        <div class="config-grid">
                            <div class="config-item">
                                <div class="config-label">SMTP服务器</div>
                                <div class="config-value" id="smtp-server">smtp.qq.com</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">SMTP端口</div>
                                <div class="config-value" id="smtp-port">465 (SSL)</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">IMAP服务器</div>
                                <div class="config-value" id="imap-server">imap.qq.com</div>
                            </div>
                            <div class="config-item">
                                <div class="config-label">IMAP端口</div>
                                <div class="config-value" id="imap-port">993 (SSL)</div>
                            </div>
                        </div>
                    </div>

                    <!-- 手动配置参数 -->
                    <div class="manual-config-section" id="manual-config-section" style="display: none;">
                        <div class="config-title">
                            <i class="fas fa-tools"></i>
                            <span>手动配置参数</span>
                        </div>

                        <!-- SMTP 配置 -->
                        <div class="config-group-manual">
                            <div class="config-group-title">
                                <i class="fas fa-paper-plane"></i>
                                <span>SMTP 发送服务器配置</span>
                            </div>
                            <div class="manual-config-grid">
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <input type="text" id="manual-smtp-server" placeholder="smtp.example.com" class="email-input">
                                </div>
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-plug"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <input type="text" id="manual-smtp-port" placeholder="465" class="email-input">
                                </div>
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <select id="manual-smtp-encryption" class="email-input-select">
                                        <option value="ssl">SSL/TLS (推荐)</option>
                                        <option value="starttls">STARTTLS</option>
                                        <option value="none">无加密</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- IMAP 配置 -->
                        <div class="config-group-manual">
                            <div class="config-group-title">
                                <i class="fas fa-inbox"></i>
                                <span>IMAP 接收服务器配置</span>
                            </div>
                            <div class="manual-config-grid">
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-server"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <input type="text" id="manual-imap-server" placeholder="imap.example.com" class="email-input">
                                </div>
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-plug"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <input type="text" id="manual-imap-port" placeholder="993" class="email-input">
                                </div>
                                <div class="email-input-wrapper manual-input">
                                    <div class="input-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="input-divider"></div>
                                    <select id="manual-imap-encryption" class="email-input-select">
                                        <option value="ssl">SSL/TLS (推荐)</option>
                                        <option value="starttls">STARTTLS</option>
                                        <option value="none">无加密</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- 操作按钮 -->
                        <div class="email-form-actions">
                            <button type="button" class="email-btn-secondary" id="test-btn">测试连接</button>
                            <button type="submit" class="email-btn-primary" id="save-btn">保存配置</button>
                        </div>
                    </div>
                </div>

                <!-- 账号设置页签内容 -->
                <div class="tab-content" id="account-tab">

                    <!-- 账号信息展示区域 -->
                    <div class="account-info-section">
                        <div class="section-header">
                            <i class="fas fa-user"></i>
                            <span>账号信息</span>
                        </div>

                        <div class="info-items">
                            <div class="info-item phone-info">
                                <span class="info-label">手机号</span>
                                <span class="info-value">138****5678</span>
                            </div>

                            <div class="info-item username-info">
                                <span class="info-label">账号名</span>
                                <span class="info-value">username123</span>
                            </div>

                            <div class="info-item google-info">
                                <span class="info-label">Google账号</span>
                                <span class="info-value"><EMAIL></span>
                            </div>
                        </div>
                    </div>

                    <!-- 密码管理区域 -->
                    <div class="account-section password-section">
                        <div class="section-header">
                            <i class="fas fa-lock"></i>
                            <span>登录密码</span>
                        </div>

                        <!-- 未设置密码状态 -->
                        <div class="password-status not-set">
                            <span class="status-text">未设置登录密码</span>
                            <button class="account-btn set-password-btn">设置密码</button>
                        </div>

                        <!-- 已设置密码状态 -->
                        <div class="password-status set">
                            <span class="status-text">● 已设置登录密码</span>
                            <button class="account-btn change-password-btn">修改密码</button>
                        </div>
                    </div>

                    <!-- 绑定操作区域 -->
                    <div class="account-section binding-section">
                        <div class="section-header">
                            <i class="fas fa-link"></i>
                            <span>账号绑定</span>
                        </div>

                        <!-- 绑定状态显示 -->
                        <div class="binding-status hidden">
                            <div class="binding-status-item phone-binding-status">
                                <span class="binding-label">手机号</span>
                                <span class="binding-value">已绑定</span>
                            </div>
                            <div class="binding-status-item google-binding-status">
                                <span class="binding-label">Google账号</span>
                                <span class="binding-value">已绑定</span>
                            </div>
                        </div>

                        <!-- 绑定操作按钮 -->
                        <div class="binding-actions">
                            <button class="account-btn bind-phone-btn">绑定手机号</button>
                            <button class="account-btn bind-google-btn">绑定Google账号</button>
                        </div>
                    </div>
                </div>

                <!-- 消息提示容器 -->
                <div id="email-message-container"></div>
            </div>
        </div>

        <!-- 状态切换器（仅原型演示用，位于卡片外部） -->
        <div class="prototype-state-switcher external" id="external-state-switcher">
            <div class="state-switcher-label">原型状态切换：</div>
            <div class="state-buttons">
                <button class="state-btn active" data-state="phone-only">仅手机号用户</button>
                <button class="state-btn" data-state="phone-password">手机号+密码用户</button>
                <button class="state-btn" data-state="google-only">仅Google用户</button>
                <button class="state-btn" data-state="full-account">完整账号用户</button>
            </div>
        </div>
    </div>

    <!-- 弹窗遮罩层 -->
    <div class="modal-overlay" id="modal-overlay">
        <!-- 设置密码弹窗 -->
        <div class="modal set-password-modal" id="set-password-modal">
            <div class="modal-header">
                <h3>设置登录密码</h3>
                <button class="modal-close-btn" data-modal="set-password-modal">×</button>
            </div>
            <div class="modal-body">
                <!-- 账号提示区域 -->
                <div class="account-tip-section" id="password-setup-tip" style="display: none;">
                    <div class="tip-content">
                        <i class="fas fa-info-circle"></i>
                        <span id="password-setup-tip-text"></span>
                    </div>
                </div>
                
                <div class="modal-form">
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="new-password" placeholder="请输入新密码（8-20位）" class="email-input">
                        <button type="button" class="password-toggle" data-target="new-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="confirm-password" placeholder="请确认新密码" class="email-input">
                        <button type="button" class="password-toggle" data-target="confirm-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="email-btn-secondary" data-modal="set-password-modal">取消</button>
                <button class="email-btn-primary" id="confirm-set-password">确认设置</button>
            </div>
        </div>

        <!-- 修改密码弹窗 -->
        <div class="modal change-password-modal" id="change-password-modal">
            <div class="modal-header">
                <h3>修改登录密码</h3>
                <button class="modal-close-btn" data-modal="change-password-modal">×</button>
            </div>
            <div class="modal-body">
                <div class="modal-form">
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="current-password" placeholder="请输入当前密码" class="email-input">
                        <button type="button" class="password-toggle" data-target="current-password">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="new-password-change" placeholder="请输入新密码（8-20位）" class="email-input">
                        <button type="button" class="password-toggle" data-target="new-password-change">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="password" id="confirm-password-change" placeholder="请确认新密码" class="email-input">
                        <button type="button" class="password-toggle" data-target="confirm-password-change">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="email-btn-secondary" data-modal="change-password-modal">取消</button>
                <button class="email-btn-primary" id="confirm-change-password">确认修改</button>
            </div>
        </div>

        <!-- 绑定手机号弹窗 -->
        <div class="modal bind-phone-modal" id="bind-phone-modal">
            <div class="modal-header">
                <h3>绑定手机号</h3>
                <button class="modal-close-btn" data-modal="bind-phone-modal">×</button>
            </div>
            <div class="modal-body">
                <div class="modal-form">
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="tel" id="bind-phone-number" placeholder="请输入手机号" class="email-input">
                    </div>
                    <div class="email-input-wrapper">
                        <div class="input-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="input-divider"></div>
                        <input type="text" id="bind-phone-code" placeholder="请输入验证码" class="email-input">
                        <button type="button" class="sms-code-btn" id="send-bind-code">获取验证码</button>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="email-btn-secondary" data-modal="bind-phone-modal">取消</button>
                <button class="email-btn-primary" id="confirm-bind-phone">确认绑定</button>
            </div>
        </div>

        <!-- 绑定Google账号弹窗 -->
        <div class="modal bind-google-modal" id="bind-google-modal">
            <div class="modal-header">
                <h3>绑定Google账号</h3>
                <button class="modal-close-btn" data-modal="bind-google-modal">×</button>
            </div>
            <div class="modal-body">
                <div class="google-auth-info">
                    <div class="google-icon">
                        <i class="fab fa-google"></i>
                    </div>
                    <p class="auth-description">点击下方按钮将跳转到Google授权页面，授权成功后将自动绑定您的Google账号。</p>
                    <div class="auth-benefits">
                        <div class="benefit-item">
                            <i class="fas fa-check"></i>
                            <span>支持Google账号快速登录</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-check"></i>
                            <span>提供更多登录方式选择</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="email-btn-secondary" data-modal="bind-google-modal">取消</button>
                <button class="email-btn-primary google-auth-btn" id="confirm-bind-google">
                    <i class="fab fa-google"></i>
                    <span>授权绑定</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="script.js"></script>
</body>
</html>