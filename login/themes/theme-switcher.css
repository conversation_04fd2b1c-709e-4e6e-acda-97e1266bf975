/* 主题切换器样式 - 升级版 */
.theme-switcher {
    position: fixed;
    bottom: var(--spacing-lg, 24px);
    right: var(--spacing-lg, 24px);
    z-index: 1000;
    user-select: none;
}

.theme-toggle-btn {
    width: 56px;
    height: 56px;
    border-radius: var(--radius-full, 50%);
    background: var(--primary-color, #1976d2);
    border: none;
    color: var(--text-inverse, #ffffff);
    font-size: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 12px var(--shadow-color, rgba(0, 0, 0, 0.15));
    transition: all var(--animation-duration, 0.3s) var(--animation-easing, ease);
    position: relative;
    overflow: hidden;
}

.theme-toggle-btn:hover {
    background: var(--primary-hover, #1565c0);
    transform: scale(1.05);
    box-shadow: 0 6px 20px var(--shadow-hover, rgba(0, 0, 0, 0.2));
}

.theme-toggle-btn:active {
    transform: scale(0.95);
}

.theme-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, var(--primary-light, #42a5f5) 50%, transparent 70%);
    opacity: 0;
    transition: opacity var(--animation-duration-fast, 0.15s);
}

.theme-toggle-btn:hover::before {
    opacity: 0.1;
}

.theme-panel {
    position: absolute;
    bottom: calc(100% + var(--spacing-sm, 8px));
    right: 0;
    min-width: 280px;
    background: var(--surface-color, #ffffff);
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: var(--radius-lg, 8px);
    box-shadow: 0 8px 32px var(--shadow-color, rgba(0, 0, 0, 0.15));
    opacity: 0;
    visibility: hidden;
    transform: translateY(var(--spacing-sm, 8px)) scale(0.95);
    transition: all var(--animation-duration, 0.3s) var(--animation-easing, ease);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.theme-panel.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.theme-panel-header {
    padding: var(--spacing-base, 16px);
    background: var(--surface-color, #ffffff);
    border-right: 1px solid var(--border-color, #e0e0e0);
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    border-radius: var(--radius-lg, 8px) var(--radius-lg, 8px) 0 0;
    position: relative;
}

.theme-panel-header h3 {
    margin: 0;
    font-size: var(--font-size-lg, 18px);
    font-weight: var(--font-weight-semibold, 600);
    color: var(--text-primary, #212121);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 8px);
}

.theme-panel-header .panel-icon {
    color: var(--primary-color, #1976d2);
}

/* 自动模式区域样式 */
.auto-mode-section {
    padding: var(--spacing-base, 16px);
    border-bottom: 1px solid var(--border-color, #e0e0e0);
    background: var(--surface-color, #ffffff);
}

.auto-mode-section h4 {
    margin: 0 0 var(--spacing-sm, 8px) 0;
    font-size: var(--font-size-sm, 14px);
    font-weight: var(--font-weight-medium, 500);
    color: var(--text-secondary, #757575);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.auto-mode-options {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-xs, 4px);
}

.auto-mode-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs, 4px);
    padding: var(--spacing-sm, 8px) var(--spacing-xs, 4px);
    border: 1px solid var(--border-color, #e0e0e0);
    border-radius: var(--radius-base, 6px);
    background: var(--surface-color, #ffffff);
    color: var(--text-secondary, #757575);
    font-size: var(--font-size-xs, 12px);
    font-weight: var(--font-weight-medium, 500);
    cursor: pointer;
    transition: all var(--animation-duration-fast, 0.15s) var(--animation-easing, ease);
    position: relative;
    overflow: hidden;
}

.auto-mode-btn:hover {
    background: var(--surface-hover, #f5f5f5);
    border-color: var(--border-hover, #d0d0d0);
    color: var(--text-primary, #212121);
    transform: translateY(-1px);
}

.auto-mode-btn.active {
    background: var(--primary-color, #1976d2);
    border-color: var(--primary-color, #1976d2);
    color: var(--text-inverse, #ffffff);
    box-shadow: 0 2px 4px var(--primary-alpha-20, rgba(25, 118, 210, 0.2));
}

.auto-mode-btn.active:hover {
    background: var(--primary-hover, #1565c0);
    border-color: var(--primary-hover, #1565c0);
}

.auto-mode-btn i {
    font-size: var(--font-size-sm, 14px);
    margin-right: var(--spacing-xs, 4px);
}

.auto-mode-btn span {
    white-space: nowrap;
}



/* 深色主题特殊样式 */
[data-theme="professional"] .theme-panel {
    border-color: var(--border-color, #334155);
    background: var(--surface-color, #1e293b);
}

[data-theme="professional"] .theme-panel-header {
    background: var(--surface-color, #1e293b);
    border-color: var(--border-color, #334155);
}

[data-theme="professional"] .theme-option {
    background: var(--surface-color, #1e293b);
}

[data-theme="professional"] .theme-option:hover {
    background: var(--surface-hover, #334155);
}

/* 深色主题自动模式样式 */
[data-theme="professional"] .auto-mode-section {
    background: var(--surface-color, #1e293b);
    border-color: var(--border-color, #334155);
}

[data-theme="professional"] .auto-mode-btn {
    background: var(--surface-color, #1e293b);
    border-color: var(--border-color, #334155);
    color: var(--text-secondary, #cbd5e1);
}

[data-theme="professional"] .auto-mode-btn:hover {
    background: var(--surface-hover, #334155);
    border-color: var(--border-hover, #475569);
    color: var(--text-primary, #f8fafc);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .theme-switcher {
        bottom: var(--spacing-base, 16px);
        right: var(--spacing-base, 16px);
    }
    
    .theme-toggle-btn {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }
    
    .theme-panel {
        min-width: 240px;
        max-width: calc(100vw - var(--spacing-xl, 32px));
    }
    
    .theme-option {
        padding: var(--spacing-sm, 8px) var(--spacing-base, 16px);
    }
    
    .theme-preview {
        width: 24px;
        height: 24px;
        margin-right: var(--spacing-sm, 8px);
    }
}

/* 加载动画 */
@keyframes themeSwitch {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.theme-switching {
    animation: themeSwitch var(--animation-duration, 0.3s) ease-in-out;
}

/* 主题预览色彩 */
.theme-preview-color:nth-child(1) { background: var(--chart-primary, #1976d2); }
.theme-preview-color:nth-child(2) { background: var(--chart-secondary, #ff9800); }
.theme-preview-color:nth-child(3) { background: var(--chart-tertiary, #4caf50); }
.theme-preview-color:nth-child(4) { background: var(--chart-quaternary, #9c27b0); }

/* 工具提示样式 */
.theme-tooltip {
    position: absolute;
    bottom: calc(100% + var(--spacing-xs, 4px));
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-900, #212121);
    color: var(--text-inverse, #ffffff);
    padding: var(--spacing-xs, 4px) var(--spacing-sm, 8px);
    border-radius: var(--radius-sm, 4px);
    font-size: var(--font-size-xs, 12px);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--animation-duration-fast, 0.15s);
    pointer-events: none;
    z-index: 1001;
}

.theme-toggle-btn:hover .theme-tooltip {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-var(--spacing-xs, 4px));
}

.theme-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: var(--spacing-xs, 4px) solid transparent;
    border-top-color: var(--gray-900, #212121);
} 