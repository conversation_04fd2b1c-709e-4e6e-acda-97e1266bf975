{"manifest_version": 3, "name": "TikTok商家建联助手", "version": "2.0.0", "description": "专为TikTok商家后台设计的智能建联工具，支持批量消息发送、回复监控和自动化营销", "permissions": ["storage", "alarms", "scripting", "tabs", "webNavigation", "notifications", "contextMenus", "webRequest", "debugger", "cookies", "activeTab"], "host_permissions": ["https://seller-us.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-sg.tiktok.com/*", "https://seller-my.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://seller-vn.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-id.tiktok.com/*", "https://*.tiktokglobalshop.com/*", "https://seller.tiktokglobalshop.com/*", "https://*.tiktokshop.com/*", "https://affiliate.tiktok.com/*", "https://partner.tiktokshop.com/*", "https://agent.turingmarket.ai/*", "https://*.turingmarket.ai/*", "http://localhost:*", "https://localhost:*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://seller-us.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-sg.tiktok.com/*", "https://seller-my.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://seller-vn.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-id.tiktok.com/*", "https://*.tiktokglobalshop.com/*", "https://affiliate.tiktok.com/*", "https://*.tiktokshop.com/*"], "js": ["content.js"], "css": ["content.css"], "run_at": "document_start"}, {"matches": ["https://seller-us.tiktok.com/seller/im*", "https://seller-uk.tiktok.com/seller/im*", "https://seller-sg.tiktok.com/seller/im*", "https://seller-my.tiktok.com/seller/im*", "https://seller-th.tiktok.com/seller/im*", "https://seller-vn.tiktok.com/seller/im*", "https://seller-ph.tiktok.com/seller/im*", "https://seller-id.tiktok.com/seller/im*", "https://*.tiktokglobalshop.com/seller/im*", "https://*.tokopedia.com/seller/im*"], "js": ["seller-im.js"], "css": ["content.css"], "run_at": "document_start", "all_frames": true}], "action": {"default_popup": "popup.html", "default_title": "TikTok商家建联助手", "default_icon": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}}, "icons": {"16": "icons/icon-16.png", "32": "icons/icon-32.png", "48": "icons/icon-48.png", "128": "icons/icon-128.png"}, "web_accessible_resources": [{"resources": ["injected.js", "assets/*"], "matches": ["https://seller-us.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-sg.tiktok.com/*", "https://seller-my.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://seller-vn.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-id.tiktok.com/*", "https://*.tiktokglobalshop.com/*", "https://affiliate.tiktok.com/*", "https://*.tiktokshop.com/*"]}]}