/**
 * TikTok商家建联插件 - seller/im私信页面专用脚本
 * 专门处理商家后台私信界面，参考达秘插件的实现
 */

// 防止重复注入
if (window.tikTokSellerImLoaded) {
    console.log('TikTok seller/im专用脚本已加载，跳过重复注入');
} else {
    window.tikTokSellerImLoaded = true;

    // seller/im专用配置
    const SELLER_IM_CONFIG = {
        // 私信页面特有选择器 - 基于达秘插件的发现
        selectors: {
            // 消息输入框
            messageInput: [
                '#im_sdk_chat_input textarea',
                'textarea[placeholder*="输入消息"]',
                'textarea[placeholder*="Type a message"]',
                '.message-input textarea',
                '.chat-input textarea'
            ],
            
            // 发送按钮
            sendButton: [
                'button[data-e2e="send-message"]',
                '.send-button',
                '.btn-send',
                'button[type="submit"]',
                '.message-send-btn'
            ],
            
            // 消息列表容器
            messageContainer: [
                '.chat-container',
                '.message-list',
                '.conversation-container',
                '#im_sdk_chat_messages',
                '.im-message-list'
            ],
            
            // 新消息指示器
            newMessage: [
                '.new-message',
                '.unread-message',
                '.message-item:not(.own-message)',
                '[data-e2e="message-item"]:not([data-direction="outgoing"])'
            ],
            
            // 对话列表
            conversationList: [
                '.conversation-list',
                '.chat-list',
                '.im-conversation-list',
                '.seller-im-conversations'
            ]
        },
        
        // 监控间隔
        intervals: {
            messageCheck: 2000,  // 2秒检查一次新消息
            heartbeat: 30000     // 30秒发送一次心跳
        }
    };

    let messageObserver = null;
    let heartbeatInterval = null;
    let lastMessageCount = 0;

    /**
     * 初始化seller/im专用功能
     */
    function initSellerIM() {
        console.log('🎯 TikTok seller/im私信页面专用脚本已激活');
        
        // 创建专用状态指示器
        createSellerIMIndicator();
        
        // 启动消息监控
        startSellerIMMessageMonitoring();
        
        // 启动心跳保持连接
        startHeartbeat();
        
        // 通知background script
        chrome.runtime.sendMessage({
            type: 'SELLER_IM_READY',
            url: window.location.href,
            timestamp: Date.now()
        }).catch(() => {
            console.log('无法连接到background script');
        });
    }

    /**
     * 创建seller/im专用状态指示器
     */
    function createSellerIMIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'seller-im-plugin-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 16px;
            height: 16px;
            background: linear-gradient(45deg, #ff0050, #ff6b9d);
            border-radius: 50%;
            z-index: 10001;
            box-shadow: 0 0 15px rgba(255, 0, 80, 0.6);
            animation: sellerImPulse 2s infinite;
            cursor: pointer;
        `;
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sellerImPulse {
                0%, 100% { 
                    opacity: 1; 
                    transform: scale(1);
                    box-shadow: 0 0 15px rgba(255, 0, 80, 0.6);
                }
                50% { 
                    opacity: 0.8; 
                    transform: scale(1.2);
                    box-shadow: 0 0 25px rgba(255, 0, 80, 0.8);
                }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(indicator);
        
        // 点击显示状态
        indicator.addEventListener('click', () => {
            showSellerIMStatus();
        });
    }

    /**
     * 显示seller/im状态面板
     */
    function showSellerIMStatus() {
        const existingPanel = document.getElementById('seller-im-status-panel');
        if (existingPanel) {
            existingPanel.remove();
            return;
        }

        const panel = document.createElement('div');
        panel.id = 'seller-im-status-panel';
        panel.innerHTML = `
            <div style="
                position: fixed;
                top: 40px;
                right: 10px;
                width: 300px;
                background: white;
                border: 2px solid #ff0050;
                border-radius: 10px;
                padding: 15px;
                z-index: 10002;
                box-shadow: 0 5px 20px rgba(0,0,0,0.3);
                font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            ">
                <h3 style="margin: 0 0 10px 0; color: #ff0050; font-size: 14px;">
                    🎯 TikTok seller/im监控状态
                </h3>
                <div style="font-size: 12px; line-height: 1.5;">
                    <div>📍 页面类型: seller/im私信页面</div>
                    <div>💬 消息监控: <span style="color: #10b981;">已激活</span></div>
                    <div>❤️ 心跳保持: <span style="color: #10b981;">运行中</span></div>
                    <div>📊 检测到消息: ${lastMessageCount}条</div>
                    <div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee;">
                        <small style="color: #666;">点击指示器可关闭此面板</small>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(panel);

        // 5秒后自动关闭
        setTimeout(() => {
            panel.remove();
        }, 5000);
    }

    /**
     * 启动seller/im消息监控
     */
    function startSellerIMMessageMonitoring() {
        // 寻找消息容器
        const messageContainer = findElement(SELLER_IM_CONFIG.selectors.messageContainer);
        
        if (!messageContainer) {
            console.log('seller/im: 未找到消息容器，稍后重试');
            setTimeout(startSellerIMMessageMonitoring, 3000);
            return;
        }

        console.log('seller/im: 开始监控消息容器:', messageContainer);

        // 创建消息观察器
        messageObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            handleNewMessageInSellerIM(node);
                        }
                    });
                }
            });
        });

        // 开始观察
        messageObserver.observe(messageContainer, {
            childList: true,
            subtree: true
        });

        // 初始扫描现有消息
        scanExistingMessages(messageContainer);
    }

    /**
     * 扫描现有消息
     */
    function scanExistingMessages(container) {
        const messageElements = container.querySelectorAll('.message-item, .chat-message, .im-message');
        lastMessageCount = messageElements.length;
        console.log(`seller/im: 检测到 ${lastMessageCount} 条现有消息`);
    }

    /**
     * 处理seller/im中的新消息
     */
    function handleNewMessageInSellerIM(element) {
        // 检查是否是新消息
        const isMessage = SELLER_IM_CONFIG.selectors.newMessage.some(selector => {
            try {
                return element.matches && element.matches(selector) ||
                       element.querySelector && element.querySelector(selector);
            } catch(e) {
                return false;
            }
        });

        if (!isMessage) return;

        // 检查是否是自己发送的消息（跳过）
        const ownMessageSelectors = [
            '.own-message', 
            '.sent-message',
            '.message-out',
            '[data-direction="outgoing"]'
        ];
        
        const isOwnMessage = ownMessageSelectors.some(selector => {
            try {
                return element.matches(selector) || element.querySelector(selector);
            } catch(e) {
                return false;
            }
        });

        if (isOwnMessage) return;

        // 提取消息信息
        const messageInfo = extractSellerIMMessageInfo(element);
        if (!messageInfo) return;

        lastMessageCount++;
        console.log('🆕 seller/im新消息:', messageInfo);

        // 通知background script
        chrome.runtime.sendMessage({
            type: 'NEW_SELLER_IM_MESSAGE',
            message: {
                ...messageInfo,
                timestamp: Date.now(),
                url: window.location.href,
                source: 'seller_im'
            }
        }).catch(error => {
            console.log('发送消息到background失败:', error);
        });
    }

    /**
     * 提取seller/im消息信息
     */
    function extractSellerIMMessageInfo(element) {
        const content = element.textContent?.trim();
        if (!content) return null;

        // 尝试提取发送者信息
        let senderName = 'Unknown';
        let senderAvatar = null;

        // 常见的发送者选择器
        const senderSelectors = [
            '.sender-name',
            '.username',
            '.user-name',
            '.message-sender',
            '[data-e2e="sender"]'
        ];

        for (const selector of senderSelectors) {
            const senderElement = element.querySelector(selector);
            if (senderElement) {
                senderName = senderElement.textContent.trim();
                break;
            }
        }

        // 尝试提取头像
        const avatarElement = element.querySelector('.avatar img, .user-avatar img, .sender-avatar img');
        if (avatarElement) {
            senderAvatar = avatarElement.src;
        }

        return {
            from: senderName,
            content: content,
            avatar: senderAvatar,
            element: element.outerHTML.substring(0, 200) + '...'
        };
    }

    /**
     * 启动心跳保持连接
     */
    function startHeartbeat() {
        heartbeatInterval = setInterval(() => {
            // 发送心跳信号
            chrome.runtime.sendMessage({
                type: 'SELLER_IM_HEARTBEAT',
                url: window.location.href,
                timestamp: Date.now(),
                messageCount: lastMessageCount
            }).catch(() => {
                // 静默处理连接错误
            });
        }, SELLER_IM_CONFIG.intervals.heartbeat);
    }

    /**
     * 查找元素（支持选择器数组）
     */
    function findElement(selectors) {
        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
        }
        return null;
    }

    /**
     * 延迟函数
     */
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 监听来自background的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        switch (message.type) {
            case 'SELLER_IM_SEND_MESSAGE':
                // 在seller/im页面发送消息
                sendMessageInSellerIM(message.data).then(result => {
                    sendResponse(result);
                });
                return true;

            case 'SELLER_IM_GET_CONVERSATIONS':
                // 获取对话列表
                getSellerIMConversations().then(result => {
                    sendResponse(result);
                });
                return true;

            case 'SELLER_IM_STATUS':
                sendResponse({
                    active: true,
                    messageCount: lastMessageCount,
                    url: window.location.href
                });
                break;
        }
    });

    /**
     * 在seller/im页面发送消息
     */
    async function sendMessageInSellerIM(data) {
        try {
            const { message } = data;
            
            // 找到输入框
            const messageInput = findElement(SELLER_IM_CONFIG.selectors.messageInput);
            if (!messageInput) {
                throw new Error('未找到消息输入框');
            }

            // 清空并输入消息
            messageInput.focus();
            messageInput.value = '';
            
            // 模拟逐字输入
            for (const char of message) {
                messageInput.value += char;
                messageInput.dispatchEvent(new Event('input', { bubbles: true }));
                await delay(50);
            }

            // 触发change事件
            messageInput.dispatchEvent(new Event('change', { bubbles: true }));
            await delay(200);

            // 找到发送按钮并点击
            const sendButton = findElement(SELLER_IM_CONFIG.selectors.sendButton);
            if (!sendButton) {
                throw new Error('未找到发送按钮');
            }

            sendButton.click();
            await delay(500);

            return {
                success: true,
                message: '消息发送成功',
                sentMessage: message
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取seller/im对话列表
     */
    async function getSellerIMConversations() {
        try {
            const conversationContainer = findElement(SELLER_IM_CONFIG.selectors.conversationList);
            if (!conversationContainer) {
                throw new Error('未找到对话列表');
            }

            const conversations = [];
            const conversationElements = conversationContainer.querySelectorAll('.conversation-item, .chat-item, .im-conversation');

            conversationElements.forEach(element => {
                const nameElement = element.querySelector('.conversation-name, .chat-name, .user-name');
                const lastMessageElement = element.querySelector('.last-message, .conversation-preview');
                const unreadElement = element.querySelector('.unread-count, .new-message-count');

                if (nameElement) {
                    conversations.push({
                        name: nameElement.textContent.trim(),
                        lastMessage: lastMessageElement ? lastMessageElement.textContent.trim() : '',
                        unreadCount: unreadElement ? parseInt(unreadElement.textContent) || 0 : 0,
                        element: element.outerHTML.substring(0, 200) + '...'
                    });
                }
            });

            return {
                success: true,
                conversations: conversations,
                total: conversations.length
            };

        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', () => {
        if (messageObserver) {
            messageObserver.disconnect();
        }
        if (heartbeatInterval) {
            clearInterval(heartbeatInterval);
        }
    });

    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initSellerIM);
    } else {
        initSellerIM();
    }

    console.log('🎯 TikTok seller/im专用脚本加载完成');
}