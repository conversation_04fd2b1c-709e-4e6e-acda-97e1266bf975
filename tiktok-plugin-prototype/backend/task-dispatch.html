<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok建联助手 - 任务分发中心</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <style>
        :root {
            --primary-color: #ff0050;
            --primary-dark: #e6004a;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --surface-color: #ffffff;
            --background-color: #f8fafc;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
        }

        .status-bar {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            text-align: center;
            flex: 1;
        }

        .status-value {
            font-size: 24px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .status-label {
            font-size: 12px;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        .panel {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .panel-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px 30px;
            font-size: 18px;
            font-weight: 600;
        }

        .panel-content {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
            font-size: 14px;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.2s ease;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(255, 0, 80, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 0, 80, 0.3);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            color: white;
        }

        .task-queue {
            max-height: 400px;
            overflow-y: auto;
        }

        .task-item {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }

        .task-item:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }

        .task-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .task-type {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .task-priority {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-high {
            background: #fee2e2;
            color: #dc2626;
        }

        .priority-medium {
            background: #fef3c7;
            color: #d97706;
        }

        .priority-low {
            background: #f0f9ff;
            color: #0369a1;
        }

        .task-details {
            font-size: 13px;
            color: var(--text-secondary);
            margin-top: 8px;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
            z-index: 1000;
        }

        .connection-status.connected {
            background: #dcfce7;
            color: #16a34a;
        }

        .connection-status.disconnected {
            background: #fee2e2;
            color: #dc2626;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
        }

        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 16px 20px;
            max-width: 350px;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .notification-message {
            font-size: 14px;
            color: var(--text-secondary);
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- 连接状态指示器 -->
    <div class="connection-status connected" id="connectionStatus">
        <div class="status-indicator"></div>
        <span>已连接到插件</span>
    </div>

    <!-- 通知 -->
    <div class="notification" id="notification">
        <div class="notification-header">
            <i class="fas fa-info-circle text-blue-500"></i>
            <span class="notification-title">通知</span>
        </div>
        <div class="notification-message" id="notificationMessage"></div>
    </div>

    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1><i class="fab fa-tiktok mr-3"></i>TikTok建联助手 - 任务分发中心</h1>
            <p>智能管理和分发TikTok网红建联任务，实时监控执行状态</p>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value" id="totalTasks">0</div>
                <div class="status-label">总任务数</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="pendingTasks">0</div>
                <div class="status-label">待执行</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="completedTasks">0</div>
                <div class="status-label">已完成</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="successRate">0%</div>
                <div class="status-label">成功率</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 任务创建面板 -->
            <div class="panel">
                <div class="panel-header">
                    <i class="fas fa-plus-circle mr-2"></i>创建新任务
                </div>
                <div class="panel-content">
                    <form id="taskForm">
                        <div class="form-group">
                            <label class="form-label">任务类型</label>
                            <select class="form-select" id="taskType" required>
                                <option value="">请选择任务类型</option>
                                <option value="search_influencer">搜索网红</option>
                                <option value="send_message">发送消息</option>
                                <option value="check_reply">检查回复</option>
                                <option value="solve_captcha">处理验证码</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">优先级</label>
                            <select class="form-select" id="taskPriority" required>
                                <option value="high">高优先级</option>
                                <option value="medium" selected>中优先级</option>
                                <option value="low">低优先级</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">目标用户名</label>
                            <input type="text" class="form-input" id="targetUsername" placeholder="@用户名" required>
                        </div>

                        <div class="form-group" id="messageGroup" style="display: none;">
                            <label class="form-label">消息内容</label>
                            <textarea class="form-textarea" id="messageContent" placeholder="输入要发送的消息内容..."></textarea>
                        </div>

                        <div class="form-group" id="followerRange" style="display: none;">
                            <label class="form-label">粉丝数范围</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="number" class="form-input" id="minFollowers" placeholder="最小粉丝数">
                                <input type="number" class="form-input" id="maxFollowers" placeholder="最大粉丝数">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">任务描述</label>
                            <input type="text" class="form-input" id="taskDescription" placeholder="简短描述此任务...">
                        </div>

                        <div style="display: flex; gap: 10px;">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                发送任务
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="generateRandomTask()">
                                <i class="fas fa-random"></i>
                                随机生成
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 任务队列面板 -->
            <div class="panel">
                <div class="panel-header">
                    <i class="fas fa-list mr-2"></i>任务队列
                    <div style="margin-left: auto; display: flex; gap: 10px;">
                        <button class="btn btn-success btn-sm" onclick="sendAllTasks()">
                            <i class="fas fa-play"></i>
                            全部发送
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="clearAllTasks()">
                            <i class="fas fa-trash"></i>
                            清空队列
                        </button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="task-queue" id="taskQueue">
                        <div style="text-align: center; padding: 40px 20px; color: var(--text-secondary);">
                            <i class="fas fa-inbox text-4xl mb-4"></i>
                            <p>暂无任务，点击左侧创建新任务</p>
                        </div>
                    </div>

                    <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid var(--border-color);">
                        <h3 style="margin-bottom: 15px; color: var(--text-primary);">快速操作</h3>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn btn-primary btn-sm" onclick="createQuickTask('search_influencer')">
                                快速搜索任务
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="createQuickTask('send_message')">
                                快速消息任务
                            </button>
                            <button class="btn btn-primary btn-sm" onclick="createQuickTask('check_reply')">
                                快速检查任务
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let taskQueue = [];
        let taskIdCounter = 1;
        let statistics = {
            totalTasks: 0,
            pendingTasks: 0,
            completedTasks: 0,
            successRate: 0
        };

        // 模拟服务器
        class MockServer {
            constructor() {
                this.port = 8080;
                this.tasks = [];
                this.isRunning = false;
                this.init();
            }

            init() {
                console.log('模拟后端服务器已启动，端口:', this.port);
                this.startServer();
            }

            startServer() {
                this.isRunning = true;
                // 模拟API端点
                this.setupRoutes();
            }

            setupRoutes() {
                // 模拟处理任务获取请求
                window.addEventListener('message', (event) => {
                    if (event.data.type === 'API_REQUEST') {
                        this.handleAPIRequest(event.data);
                    }
                });
            }

            handleAPIRequest(data) {
                const { endpoint, method, payload } = data;
                
                switch (endpoint) {
                    case '/api/tasks/pending':
                        this.sendPendingTasks();
                        break;
                    case '/api/tasks':
                        if (method === 'POST') {
                            this.addTask(payload);
                        }
                        break;
                    default:
                        console.log('未知API端点:', endpoint);
                }
            }

            sendPendingTasks() {
                const pendingTasks = this.tasks.filter(task => task.status === 'pending');
                console.log('发送待处理任务:', pendingTasks.length);
                
                // 模拟插件请求
                if (pendingTasks.length > 0) {
                    showNotification(`发送了 ${pendingTasks.length} 个任务到插件`);
                    
                    // 标记为已发送
                    pendingTasks.forEach(task => {
                        task.status = 'sent';
                        task.sentAt = Date.now();
                    });
                    
                    updateStatistics();
                }
            }

            addTask(task) {
                this.tasks.push({
                    ...task,
                    status: 'pending',
                    createdAt: Date.now()
                });
                console.log('添加任务到服务器:', task);
            }

            getAllTasks() {
                return this.tasks;
            }
        }

        // 初始化模拟服务器
        const mockServer = new MockServer();

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            initializeForm();
            updateStatistics();
            
            // 定期检查连接状态
            setInterval(checkConnectionStatus, 5000);
        });

        // 初始化表单
        function initializeForm() {
            const taskTypeSelect = document.getElementById('taskType');
            const messageGroup = document.getElementById('messageGroup');
            const followerRange = document.getElementById('followerRange');

            taskTypeSelect.addEventListener('change', function() {
                const selectedType = this.value;
                
                // 显示/隐藏相关字段
                messageGroup.style.display = selectedType === 'send_message' ? 'block' : 'none';
                followerRange.style.display = selectedType === 'search_influencer' ? 'block' : 'none';
            });

            // 表单提交
            document.getElementById('taskForm').addEventListener('submit', function(e) {
                e.preventDefault();
                createTask();
            });
        }

        // 创建任务
        function createTask() {
            const form = document.getElementById('taskForm');
            const formData = new FormData(form);
            
            const task = {
                id: `task_${Date.now()}_${taskIdCounter++}`,
                type: document.getElementById('taskType').value,
                priority: document.getElementById('taskPriority').value,
                targetUsername: document.getElementById('targetUsername').value,
                description: document.getElementById('taskDescription').value,
                createdAt: Date.now(),
                status: 'pending'
            };

            // 根据任务类型添加特定数据
            switch (task.type) {
                case 'search_influencer':
                    task.data = {
                        username: task.targetUsername.replace('@', ''),
                        minFollowers: parseInt(document.getElementById('minFollowers').value) || 1000,
                        maxFollowers: parseInt(document.getElementById('maxFollowers').value) || 1000000,
                        category: 'general'
                    };
                    break;
                
                case 'send_message':
                    task.data = {
                        username: task.targetUsername.replace('@', ''),
                        message: document.getElementById('messageContent').value,
                        template: 'custom'
                    };
                    break;
                
                case 'check_reply':
                    task.data = {
                        username: task.targetUsername.replace('@', ''),
                        lastContact: Date.now() - 24 * 60 * 60 * 1000 // 24小时前
                    };
                    break;
                
                case 'solve_captcha':
                    task.data = {
                        captchaType: 'auto_detect',
                        pageUrl: 'https://www.tiktok.com'
                    };
                    break;
            }

            // 添加到队列
            addTaskToQueue(task);
            
            // 清空表单
            form.reset();
            document.getElementById('messageGroup').style.display = 'none';
            document.getElementById('followerRange').style.display = 'none';
            
            showNotification('任务已创建并添加到队列');
        }

        // 添加任务到队列
        function addTaskToQueue(task) {
            taskQueue.push(task);
            updateTaskQueueUI();
            updateStatistics();
        }

        // 更新任务队列UI
        function updateTaskQueueUI() {
            const queueContainer = document.getElementById('taskQueue');
            
            if (taskQueue.length === 0) {
                queueContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px 20px; color: var(--text-secondary);">
                        <i class="fas fa-inbox text-4xl mb-4"></i>
                        <p>暂无任务，点击左侧创建新任务</p>
                    </div>
                `;
                return;
            }

            queueContainer.innerHTML = taskQueue.map(task => `
                <div class="task-item" data-task-id="${task.id}">
                    <div class="task-header">
                        <div>
                            <div class="task-title">${getTaskTitle(task)}</div>
                            <div class="task-type">${getTaskTypeText(task.type)}</div>
                        </div>
                        <div class="task-priority priority-${task.priority}">
                            ${task.priority === 'high' ? '高' : task.priority === 'medium' ? '中' : '低'}
                        </div>
                    </div>
                    <div class="task-details">
                        目标: ${task.targetUsername} | 创建时间: ${new Date(task.createdAt).toLocaleString()}
                        ${task.description ? `<br>描述: ${task.description}` : ''}
                    </div>
                    <div style="margin-top: 12px; display: flex; gap: 8px;">
                        <button onclick="sendSingleTask('${task.id}')" class="btn btn-primary btn-sm">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                        <button onclick="removeTask('${task.id}')" class="btn btn-secondary btn-sm">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 获取任务标题
        function getTaskTitle(task) {
            switch (task.type) {
                case 'search_influencer':
                    return `搜索网红 ${task.targetUsername}`;
                case 'send_message':
                    return `发送消息给 ${task.targetUsername}`;
                case 'check_reply':
                    return `检查 ${task.targetUsername} 的回复`;
                case 'solve_captcha':
                    return '处理验证码挑战';
                default:
                    return '未知任务';
            }
        }

        // 获取任务类型文本
        function getTaskTypeText(type) {
            const types = {
                'search_influencer': '搜索网红',
                'send_message': '发送消息',
                'check_reply': '检查回复',
                'solve_captcha': '处理验证码'
            };
            return types[type] || '未知类型';
        }

        // 发送单个任务
        function sendSingleTask(taskId) {
            const task = taskQueue.find(t => t.id === taskId);
            if (!task) return;

            // 添加到模拟服务器
            mockServer.addTask(task);
            
            // 从队列中移除
            taskQueue = taskQueue.splice(taskQueue.findIndex(t => t.id === taskId), 1);
            
            // 更新UI
            updateTaskQueueUI();
            updateStatistics();
            
            showNotification(`任务已发送: ${getTaskTitle(task)}`);
            
            // 模拟发送到插件
            setTimeout(() => {
                mockServer.sendPendingTasks();
            }, 1000);
        }

        // 发送所有任务
        function sendAllTasks() {
            if (taskQueue.length === 0) {
                showNotification('队列中没有任务', 'warning');
                return;
            }

            const taskCount = taskQueue.length;
            
            // 添加所有任务到服务器
            taskQueue.forEach(task => {
                mockServer.addTask(task);
            });
            
            // 清空队列
            taskQueue = [];
            
            // 更新UI
            updateTaskQueueUI();
            updateStatistics();
            
            showNotification(`已发送 ${taskCount} 个任务到插件`);
            
            // 模拟发送到插件
            setTimeout(() => {
                mockServer.sendPendingTasks();
            }, 1000);
        }

        // 清空所有任务
        function clearAllTasks() {
            if (taskQueue.length === 0) return;
            
            if (confirm('确定要清空所有任务吗？')) {
                taskQueue = [];
                updateTaskQueueUI();
                updateStatistics();
                showNotification('任务队列已清空');
            }
        }

        // 删除任务
        function removeTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                taskQueue = taskQueue.filter(t => t.id !== taskId);
                updateTaskQueueUI();
                updateStatistics();
                showNotification('任务已删除');
            }
        }

        // 生成随机任务
        function generateRandomTask() {
            const taskTypes = ['search_influencer', 'send_message', 'check_reply'];
            const priorities = ['high', 'medium', 'low'];
            const usernames = ['beauty_queen', 'fashion_lover', 'style_guru', 'makeup_artist', 'lifestyle_blogger'];
            const messages = [
                '您好！我们对您的内容很感兴趣，希望能与您合作。',
                'Hi，我们是一个新兴品牌，想邀请您试用我们的产品。',
                '您的视频很棒！我们想和您讨论合作的可能性。'
            ];

            const randomType = taskTypes[Math.floor(Math.random() * taskTypes.length)];
            const randomPriority = priorities[Math.floor(Math.random() * priorities.length)];
            const randomUsername = '@' + usernames[Math.floor(Math.random() * usernames.length)] + Math.floor(Math.random() * 1000);

            // 填充表单
            document.getElementById('taskType').value = randomType;
            document.getElementById('taskPriority').value = randomPriority;
            document.getElementById('targetUsername').value = randomUsername;
            document.getElementById('taskDescription').value = `随机生成的${getTaskTypeText(randomType)}任务`;

            // 触发类型变化事件
            document.getElementById('taskType').dispatchEvent(new Event('change'));

            // 根据类型填充特定字段
            if (randomType === 'send_message') {
                document.getElementById('messageContent').value = messages[Math.floor(Math.random() * messages.length)];
            } else if (randomType === 'search_influencer') {
                document.getElementById('minFollowers').value = Math.floor(Math.random() * 50000) + 10000;
                document.getElementById('maxFollowers').value = Math.floor(Math.random() * 500000) + 100000;
            }

            showNotification('已生成随机任务，请检查并提交');
        }

        // 创建快速任务
        function createQuickTask(type) {
            const quickTasks = {
                'search_influencer': {
                    type: 'search_influencer',
                    priority: 'medium',
                    targetUsername: '@beauty_guru_' + Math.floor(Math.random() * 1000),
                    description: '快速搜索美妆博主',
                    data: {
                        username: 'beauty_guru_' + Math.floor(Math.random() * 1000),
                        minFollowers: 50000,
                        maxFollowers: 200000,
                        category: 'beauty'
                    }
                },
                'send_message': {
                    type: 'send_message',
                    priority: 'high',
                    targetUsername: '@fashion_star_' + Math.floor(Math.random() * 1000),
                    description: '快速发送合作邀请',
                    data: {
                        username: 'fashion_star_' + Math.floor(Math.random() * 1000),
                        message: '您好！我们对您的时尚内容很感兴趣，希望能与您合作推广我们的新品。期待您的回复！',
                        template: 'collaboration_invite'
                    }
                },
                'check_reply': {
                    type: 'check_reply',
                    priority: 'low',
                    targetUsername: '@lifestyle_vlog_' + Math.floor(Math.random() * 1000),
                    description: '快速检查回复状态',
                    data: {
                        username: 'lifestyle_vlog_' + Math.floor(Math.random() * 1000),
                        lastContact: Date.now() - 2 * 60 * 60 * 1000
                    }
                }
            };

            const task = {
                id: `task_${Date.now()}_${taskIdCounter++}`,
                ...quickTasks[type],
                createdAt: Date.now(),
                status: 'pending'
            };

            addTaskToQueue(task);
            showNotification(`快速创建了一个${getTaskTypeText(type)}任务`);
        }

        // 更新统计数据
        function updateStatistics() {
            statistics.totalTasks = taskQueue.length + mockServer.getAllTasks().length;
            statistics.pendingTasks = taskQueue.length;
            statistics.completedTasks = mockServer.getAllTasks().filter(t => t.status === 'completed').length;
            statistics.successRate = statistics.totalTasks > 0 
                ? Math.round((statistics.completedTasks / statistics.totalTasks) * 100) 
                : 0;

            document.getElementById('totalTasks').textContent = statistics.totalTasks;
            document.getElementById('pendingTasks').textContent = statistics.pendingTasks;
            document.getElementById('completedTasks').textContent = statistics.completedTasks;
            document.getElementById('successRate').textContent = statistics.successRate + '%';
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageElement = document.getElementById('notificationMessage');
            
            messageElement.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 检查连接状态
        function checkConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span');
            
            // 模拟连接检查
            const isConnected = Math.random() > 0.1; // 90%的时间显示已连接
            
            if (isConnected) {
                statusElement.className = 'connection-status connected';
                text.textContent = '已连接到插件';
            } else {
                statusElement.className = 'connection-status disconnected';
                text.textContent = '插件连接中断';
            }
        }

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'Enter':
                        e.preventDefault();
                        document.getElementById('taskForm').dispatchEvent(new Event('submit'));
                        break;
                    case 'r':
                        e.preventDefault();
                        generateRandomTask();
                        break;
                    case 'a':
                        e.preventDefault();
                        sendAllTasks();
                        break;
                }
            }
        });

        console.log('TikTok建联助手任务分发中心已加载');
    </script>
</body>
</html>