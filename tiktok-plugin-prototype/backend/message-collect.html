<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok建联助手 - 消息收集中心</title>
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    <style>
        :root {
            --primary-color: #ff0050;
            --primary-dark: #e6004a;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --surface-color: #ffffff;
            --background-color: #f8fafc;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .header p {
            color: var(--text-secondary);
            font-size: 16px;
        }

        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px auto;
            font-size: 20px;
            color: white;
        }

        .stat-icon.messages {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-icon.replies {
            background: linear-gradient(135deg, #10b981, #059669);
        }

        .stat-icon.pending {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .stat-value {
            font-size: 28px;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
        }

        .messages-panel {
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .panel-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: white;
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-controls {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn-white {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-white:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .messages-container {
            max-height: 600px;
            overflow-y: auto;
            padding: 20px;
        }

        .message-item {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            transition: all 0.2s ease;
            position: relative;
        }

        .message-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .message-item.new {
            border-color: var(--success-color);
            background: #f0fdf4;
        }

        .message-item.replied {
            border-color: var(--primary-color);
            background: #fef2f2;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .sender-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sender-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .sender-details h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .sender-details p {
            margin: 2px 0 0 0;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .message-meta {
            text-align: right;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .message-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            text-transform: uppercase;
            margin-top: 4px;
        }

        .status-new {
            background: #dcfce7;
            color: #16a34a;
        }

        .status-replied {
            background: #fef2f2;
            color: #dc2626;
        }

        .status-pending {
            background: #fef3c7;
            color: #d97706;
        }

        .message-content {
            background: #f8fafc;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            font-size: 14px;
            line-height: 1.6;
            color: var(--text-primary);
        }

        .message-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .filter-panel, .analytics-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 20px;
        }

        .filter-panel h3, .analytics-panel h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .filter-group {
            margin-bottom: 16px;
        }

        .filter-label {
            display: block;
            font-size: 12px;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        }

        .filter-buttons {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 6px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: white;
            color: var(--text-secondary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .filter-btn.active {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .filter-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .analytics-chart {
            height: 200px;
            background: #f8fafc;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            font-size: 14px;
            margin: 16px 0;
        }

        .connection-status {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            z-index: 1000;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .connection-status.connected {
            background: #dcfce7;
            color: #16a34a;
        }

        .connection-status.collecting {
            background: #fef3c7;
            color: #d97706;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-secondary);
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            padding: 16px 20px;
            max-width: 350px;
            z-index: 1001;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .notification-title {
            font-weight: 600;
            color: var(--text-primary);
        }

        .notification-message {
            font-size: 14px;
            color: var(--text-secondary);
        }

        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                grid-row: 1;
            }
        }
    </style>
</head>
<body>
    <!-- 连接状态指示器 -->
    <div class="connection-status connected" id="connectionStatus">
        <div class="status-indicator"></div>
        <span>实时监控中</span>
    </div>

    <!-- 通知 -->
    <div class="notification" id="notification">
        <div class="notification-header">
            <i class="fas fa-bell text-blue-500"></i>
            <span class="notification-title">新消息</span>
        </div>
        <div class="notification-message" id="notificationMessage"></div>
    </div>

    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1><i class="fas fa-comments mr-3"></i>TikTok建联助手 - 消息收集中心</h1>
            <p>实时收集和管理来自TikTok网红的回复消息，智能分析建联效果</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-bar">
            <div class="stat-card">
                <div class="stat-icon messages">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stat-value" id="totalMessages">0</div>
                <div class="stat-label">总消息数</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon replies">
                    <i class="fas fa-reply"></i>
                </div>
                <div class="stat-value" id="newReplies">0</div>
                <div class="stat-label">新回复</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon pending">
                    <i class="fas fa-hourglass-half"></i>
                </div>
                <div class="stat-value" id="pendingReplies">0</div>
                <div class="stat-label">待处理</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-value" id="successRate">0%</div>
                <div class="stat-label">回复率</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 消息面板 -->
            <div class="messages-panel">
                <div class="panel-header">
                    <div class="panel-title">
                        <i class="fas fa-inbox"></i>
                        收到的消息
                    </div>
                    <div class="panel-controls">
                        <button class="btn btn-white" onclick="refreshMessages()">
                            <i class="fas fa-sync-alt"></i>
                            刷新
                        </button>
                        <button class="btn btn-white" onclick="exportMessages()">
                            <i class="fas fa-download"></i>
                            导出
                        </button>
                        <button class="btn btn-white" onclick="clearMessages()">
                            <i class="fas fa-trash"></i>
                            清空
                        </button>
                    </div>
                </div>
                <div class="messages-container" id="messagesContainer">
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>暂无消息</h3>
                        <p>等待插件收集网红回复消息...</p>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 筛选面板 -->
                <div class="filter-panel">
                    <h3><i class="fas fa-filter mr-2"></i>筛选条件</h3>
                    
                    <div class="filter-group">
                        <label class="filter-label">消息状态</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-filter="all" onclick="filterMessages('all')">全部</button>
                            <button class="filter-btn" data-filter="new" onclick="filterMessages('new')">新消息</button>
                            <button class="filter-btn" data-filter="replied" onclick="filterMessages('replied')">已回复</button>
                            <button class="filter-btn" data-filter="pending" onclick="filterMessages('pending')">待处理</button>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">时间范围</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-timefilter="all" onclick="filterByTime('all')">全部</button>
                            <button class="filter-btn" data-timefilter="today" onclick="filterByTime('today')">今天</button>
                            <button class="filter-btn" data-timefilter="week" onclick="filterByTime('week')">本周</button>
                            <button class="filter-btn" data-timefilter="month" onclick="filterByTime('month')">本月</button>
                        </div>
                    </div>

                    <div class="filter-group">
                        <label class="filter-label">网红类型</label>
                        <div class="filter-buttons">
                            <button class="filter-btn active" data-category="all" onclick="filterByCategory('all')">全部</button>
                            <button class="filter-btn" data-category="beauty" onclick="filterByCategory('beauty')">美妆</button>
                            <button class="filter-btn" data-category="fashion" onclick="filterByCategory('fashion')">时尚</button>
                            <button class="filter-btn" data-category="lifestyle" onclick="filterByCategory('lifestyle')">生活</button>
                        </div>
                    </div>
                </div>

                <!-- 分析面板 -->
                <div class="analytics-panel">
                    <h3><i class="fas fa-chart-line mr-2"></i>数据分析</h3>
                    
                    <div class="analytics-chart">
                        <div style="text-align: center;">
                            <i class="fas fa-chart-bar text-3xl mb-2 block"></i>
                            <p>消息统计图表</p>
                            <small>实时更新数据分析</small>
                        </div>
                    </div>

                    <div style="margin-top: 16px;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-size: 12px; color: var(--text-secondary);">平均回复时间</span>
                            <span style="font-size: 12px; font-weight: 600;" id="avgReplyTime">2.3小时</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span style="font-size: 12px; color: var(--text-secondary);">最活跃时段</span>
                            <span style="font-size: 12px; font-weight: 600;" id="activeHours">14:00-18:00</span>
                        </div>
                        <div style="display: flex; justify-content: space-between;">
                            <span style="font-size: 12px; color: var(--text-secondary);">今日新增</span>
                            <span style="font-size: 12px; font-weight: 600; color: var(--success-color);" id="todayCount">+3</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局状态
        let allMessages = [];
        let filteredMessages = [];
        let currentFilter = { status: 'all', time: 'all', category: 'all' };
        let statistics = {
            totalMessages: 0,
            newReplies: 0,
            pendingReplies: 0,
            successRate: 0
        };

        // 模拟消息收集服务器
        class MessageCollector {
            constructor() {
                this.messages = [];
                this.isCollecting = false;
                this.init();
            }

            init() {
                console.log('消息收集服务器已启动');
                this.startCollecting();
                this.generateMockMessages();
            }

            startCollecting() {
                this.isCollecting = true;
                
                // 模拟定期收到新消息
                setInterval(() => {
                    if (Math.random() > 0.7) { // 30%概率收到新消息
                        this.generateNewMessage();
                    }
                }, 5000);
            }

            generateMockMessages() {
                // 生成一些初始消息
                const mockMessages = [
                    {
                        id: 'msg_1',
                        from: '@beauty_queen_2024',
                        fromName: '美妆女王',
                        content: '谢谢您的邀请！我很感兴趣，能详细了解一下合作内容吗？',
                        timestamp: Date.now() - 2 * 60 * 60 * 1000,
                        status: 'new',
                        category: 'beauty',
                        url: 'https://www.tiktok.com/messages'
                    },
                    {
                        id: 'msg_2',
                        from: '@fashion_lover_style',
                        fromName: '时尚达人',
                        content: '您好！我看了您的产品，很符合我的风格。我们可以进一步讨论合作细节。',
                        timestamp: Date.now() - 4 * 60 * 60 * 1000,
                        status: 'replied',
                        category: 'fashion',
                        url: 'https://www.tiktok.com/messages'
                    },
                    {
                        id: 'msg_3',
                        from: '@lifestyle_blogger',
                        fromName: '生活博主',
                        content: '感谢联系！目前我的档期比较满，可能需要排到下个月，您看可以吗？',
                        timestamp: Date.now() - 6 * 60 * 60 * 1000,
                        status: 'pending',
                        category: 'lifestyle',
                        url: 'https://www.tiktok.com/messages'
                    }
                ];

                mockMessages.forEach(msg => this.addMessage(msg));
            }

            generateNewMessage() {
                const usernames = ['makeup_guru', 'style_icon', 'beauty_tips', 'fashion_trends', 'lifestyle_vibes'];
                const names = ['化妆师小王', '穿搭达人', '美容专家', '时尚顾问', '生活家'];
                const contents = [
                    '您好！我对您的产品很感兴趣，能了解更多详情吗？',
                    '谢谢您的邀请，我想知道具体的合作方式。',
                    '您的品牌理念很棒，我很愿意合作！',
                    '这个产品正好适合我的受众群体，我们聊聊？',
                    '感谢关注我的内容，期待与您的合作！'
                ];
                const categories = ['beauty', 'fashion', 'lifestyle'];
                const statuses = ['new', 'pending'];

                const randomIndex = Math.floor(Math.random() * usernames.length);
                const newMessage = {
                    id: 'msg_' + Date.now(),
                    from: '@' + usernames[randomIndex] + '_' + Math.floor(Math.random() * 1000),
                    fromName: names[randomIndex] + Math.floor(Math.random() * 100),
                    content: contents[Math.floor(Math.random() * contents.length)],
                    timestamp: Date.now(),
                    status: statuses[Math.floor(Math.random() * statuses.length)],
                    category: categories[Math.floor(Math.random() * categories.length)],
                    url: 'https://www.tiktok.com/messages'
                };

                this.addMessage(newMessage);
                showNotification(`收到来自 ${newMessage.fromName} 的新消息`);
                updateConnectionStatus('collecting');
                
                setTimeout(() => updateConnectionStatus('connected'), 2000);
            }

            addMessage(message) {
                this.messages.unshift(message); // 新消息在前
                allMessages = [...this.messages];
                updateMessagesDisplay();
                updateStatistics();
            }

            getAllMessages() {
                return this.messages;
            }

            clearAllMessages() {
                this.messages = [];
                allMessages = [];
                updateMessagesDisplay();
                updateStatistics();
            }
        }

        // 初始化消息收集器
        const messageCollector = new MessageCollector();

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateMessagesDisplay();
            updateStatistics();
        });

        // 更新消息显示
        function updateMessagesDisplay() {
            const container = document.getElementById('messagesContainer');
            
            // 应用筛选
            applyFilters();
            
            if (filteredMessages.length === 0) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <h3>暂无消息</h3>
                        <p>${allMessages.length === 0 ? '等待插件收集网红回复消息...' : '没有符合筛选条件的消息'}</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = filteredMessages.map(message => `
                <div class="message-item ${message.status}" data-message-id="${message.id}">
                    <div class="message-header">
                        <div class="sender-info">
                            <div class="sender-avatar">
                                ${message.fromName.charAt(0)}
                            </div>
                            <div class="sender-details">
                                <h3>${message.fromName}</h3>
                                <p>${message.from}</p>
                            </div>
                        </div>
                        <div class="message-meta">
                            <div>${formatTime(message.timestamp)}</div>
                            <div class="message-status status-${message.status}">
                                ${getStatusText(message.status)}
                            </div>
                        </div>
                    </div>
                    <div class="message-content">
                        ${message.content}
                    </div>
                    <div class="message-actions">
                        <button class="btn btn-primary" onclick="replyToMessage('${message.id}')">
                            <i class="fas fa-reply"></i> 回复
                        </button>
                        <button class="btn btn-success" onclick="markAsProcessed('${message.id}')">
                            <i class="fas fa-check"></i> 标记处理
                        </button>
                        <button class="btn btn-warning" onclick="viewProfile('${message.from}')">
                            <i class="fas fa-user"></i> 查看资料
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 应用筛选条件
        function applyFilters() {
            filteredMessages = allMessages.filter(message => {
                // 状态筛选
                if (currentFilter.status !== 'all' && message.status !== currentFilter.status) {
                    return false;
                }
                
                // 时间筛选
                if (currentFilter.time !== 'all') {
                    const now = Date.now();
                    const messageTime = message.timestamp;
                    
                    switch (currentFilter.time) {
                        case 'today':
                            if (now - messageTime > 24 * 60 * 60 * 1000) return false;
                            break;
                        case 'week':
                            if (now - messageTime > 7 * 24 * 60 * 60 * 1000) return false;
                            break;
                        case 'month':
                            if (now - messageTime > 30 * 24 * 60 * 60 * 1000) return false;
                            break;
                    }
                }
                
                // 类别筛选
                if (currentFilter.category !== 'all' && message.category !== currentFilter.category) {
                    return false;
                }
                
                return true;
            });
        }

        // 筛选消息
        function filterMessages(status) {
            currentFilter.status = status;
            updateFilterButtons('data-filter', status);
            updateMessagesDisplay();
        }

        function filterByTime(time) {
            currentFilter.time = time;
            updateFilterButtons('data-timefilter', time);
            updateMessagesDisplay();
        }

        function filterByCategory(category) {
            currentFilter.category = category;
            updateFilterButtons('data-category', category);
            updateMessagesDisplay();
        }

        // 更新筛选按钮状态
        function updateFilterButtons(attribute, value) {
            document.querySelectorAll(`[${attribute}]`).forEach(btn => {
                btn.classList.remove('active');
                if (btn.getAttribute(attribute) === value) {
                    btn.classList.add('active');
                }
            });
        }

        // 更新统计数据
        function updateStatistics() {
            statistics.totalMessages = allMessages.length;
            statistics.newReplies = allMessages.filter(m => m.status === 'new').length;
            statistics.pendingReplies = allMessages.filter(m => m.status === 'pending').length;
            statistics.successRate = statistics.totalMessages > 0 
                ? Math.round(((statistics.newReplies + statistics.pendingReplies) / statistics.totalMessages) * 100)
                : 0;

            document.getElementById('totalMessages').textContent = statistics.totalMessages;
            document.getElementById('newReplies').textContent = statistics.newReplies;
            document.getElementById('pendingReplies').textContent = statistics.pendingReplies;
            document.getElementById('successRate').textContent = statistics.successRate + '%';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const now = Date.now();
            const diff = now - timestamp;
            
            if (diff < 60 * 1000) {
                return '刚刚';
            } else if (diff < 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 1000)) + '分钟前';
            } else if (diff < 24 * 60 * 60 * 1000) {
                return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
            } else {
                return new Date(timestamp).toLocaleDateString();
            }
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'new': '新消息',
                'replied': '已回复',
                'pending': '待处理'
            };
            return statusMap[status] || status;
        }

        // 消息操作函数
        function replyToMessage(messageId) {
            const message = allMessages.find(m => m.id === messageId);
            showNotification(`正在回复 ${message.fromName} 的消息`);
            
            // 标记为已回复
            message.status = 'replied';
            updateMessagesDisplay();
            updateStatistics();
        }

        function markAsProcessed(messageId) {
            const message = allMessages.find(m => m.id === messageId);
            showNotification(`已标记 ${message.fromName} 的消息为已处理`);
            
            // 移除消息或标记为已处理
            const index = allMessages.findIndex(m => m.id === messageId);
            if (index > -1) {
                allMessages.splice(index, 1);
                updateMessagesDisplay();
                updateStatistics();
            }
        }

        function viewProfile(username) {
            showNotification(`正在查看 ${username} 的资料`);
            // 这里可以打开新窗口或显示用户资料
            window.open(`https://www.tiktok.com/${username}`, '_blank');
        }

        // 工具函数
        function refreshMessages() {
            showNotification('正在刷新消息列表...');
            
            // 模拟刷新延迟
            setTimeout(() => {
                updateMessagesDisplay();
                showNotification('消息列表已刷新');
            }, 1000);
        }

        function exportMessages() {
            if (allMessages.length === 0) {
                showNotification('没有消息可导出', 'warning');
                return;
            }

            const csvContent = [
                ['时间', '发送者', '用户名', '内容', '状态', '类别'].join(','),
                ...allMessages.map(msg => [
                    new Date(msg.timestamp).toLocaleString(),
                    msg.fromName,
                    msg.from,
                    `"${msg.content}"`,
                    getStatusText(msg.status),
                    msg.category
                ].join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = `tiktok_messages_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();

            showNotification('消息已导出为CSV文件');
        }

        function clearMessages() {
            if (allMessages.length === 0) return;
            
            if (confirm('确定要清空所有消息吗？此操作不可恢复。')) {
                messageCollector.clearAllMessages();
                showNotification('所有消息已清空');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const messageElement = document.getElementById('notificationMessage');
            
            messageElement.textContent = message;
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            const statusElement = document.getElementById('connectionStatus');
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span');
            
            switch (status) {
                case 'connected':
                    statusElement.className = 'connection-status connected';
                    text.textContent = '实时监控中';
                    break;
                case 'collecting':
                    statusElement.className = 'connection-status collecting';
                    text.textContent = '收集消息中';
                    break;
                default:
                    statusElement.className = 'connection-status connected';
                    text.textContent = '实时监控中';
            }
        }

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'r':
                        e.preventDefault();
                        refreshMessages();
                        break;
                    case 'e':
                        e.preventDefault();
                        exportMessages();
                        break;
                }
            }
        });

        console.log('TikTok建联助手消息收集中心已加载');
    </script>
</body>
</html>