<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok商家建联插件</title>
    
    <!-- 资源预加载 -->
    <link rel="preconnect" href="https://cdn.staticfile.net">
    <link rel="dns-prefetch" href="https://cdn.staticfile.net">
    
    <!-- 核心CSS框架 -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.net/tailwindcss/2.2.9/tailwind.min.css">
    
    <!-- 插件样式 -->
    <link rel="stylesheet" href="plugin-styles.css">
    
    <style>
    /* CSS变量定义 */
    :root {
        --primary-color: #ff0050;
        --primary-dark: #e6004a;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --surface-color: #ffffff;
        --surface-dark: #1f2937;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --border-color: #e5e7eb;
        --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
        --shadow-heavy: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    /* 全局样式重置 */
    * {
        box-sizing: border-box;
    }
    
    body {
        margin: 0;
        padding: 0;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f1419 50%, #1a1a2e 75%, #16213e 100%);
        background-size: 400% 400%;
        animation: gradientShift 20s ease infinite;
        width: 600px;
        height: 400px;
        position: relative;
        overflow: hidden;
    }
    
    /* 背景渐变动画 */
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    /* 背景装饰效果 - 仿登录页面 */
    body::before {
        content: '';
        position: fixed;
        top: -100px;
        left: -100px;
        width: calc(100% + 200px);
        height: calc(100% + 200px);
        background: radial-gradient(circle at 30% 30%, rgba(138, 43, 226, 0.3) 0%, rgba(75, 0, 130, 0.2) 30%, transparent 70%),
                    radial-gradient(circle at 70% 70%, rgba(30, 144, 255, 0.3) 0%, rgba(0, 0, 255, 0.2) 30%, transparent 70%);
        filter: blur(50px);
        opacity: 0.7;
        z-index: -1;
        animation: floatBackground 8s ease-in-out infinite;
        pointer-events: none;
    }
    
    @keyframes floatBackground {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(5deg); }
    }
    
    
    /* 主容器 */
    .plugin-container {
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 16px;
        position: relative;
        z-index: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    /* 插件头部 */
    .plugin-header {
        background: rgba(255, 255, 255, 0.08);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        color: white;
        padding: 16px 20px;
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
    }
    
    .plugin-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        z-index: 0;
    }
    
    .header-content {
        position: relative;
        z-index: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .plugin-title {
        display: flex;
        align-items: center;
        gap: 12px;
    }
    
    .plugin-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
    }
    
    .title-text h1 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        line-height: 1.2;
    }

    .title-text p {
        margin: 2px 0 0 0;
        font-size: 12px;
        opacity: 0.8;
        line-height: 1.2;
    }
    
    /* 顶部账号信息样式 */
    .header-account-info {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 14px;
    }

    .header-account-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 14px;
    }

    .header-account-details {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .header-account-name {
        font-size: 14px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        line-height: 1.2;
    }

    .header-account-email {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.2;
    }

    .header-account-status {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: var(--success-color);
        font-weight: 500;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--success-color);
        animation: pulse 2s infinite;
    }

    /* 新的状态指示器样式 */
    .status-indicator-new {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--error-color);
        margin-right: 8px;
    }

    .status-indicator-new.active {
        background: var(--success-color);
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }
    
    /* 主要内容区域 - 左右布局 */
    .main-content {
        display: flex;
        flex: 1;
        overflow: hidden;
    }
    
    /* 左侧面板 */
    .left-panel {
        width: 45%;
        padding: 12px;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    /* 右侧面板 */
    .right-panel {
        width: 55%;
        padding: 12px;
        border-left: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    /* 账号信息卡片 */
    .account-card {
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 10px;
        flex-shrink: 0;
    }
    
    .account-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .account-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 11px;
    }
    
    .account-details h3 {
        margin: 0;
        font-size: 12px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        line-height: 1.2;
    }
    
    .account-details p {
        margin: 2px 0 0 0;
        font-size: 10px;
        color: rgba(255, 255, 255, 0.7);
        line-height: 1.2;
    }
    
    .account-status {
        margin-left: auto;
        display: flex;
        align-items: center;
        gap: 3px;
        font-size: 9px;
        color: var(--success-color);
        font-weight: 500;
    }
    
    /* 控制按钮卡片 */
    .control-card {
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 10px;
        flex-shrink: 0;
    }
    
    .control-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 6px;
    }
    
    .control-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
    }

    .task-controls {
        display: flex;
        gap: 8px;
    }

    .control-btn {
        padding: 8px 12px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 4px;
        flex: 1;
        justify-content: center;
    }
    
    .control-btn.primary {
        background: var(--primary-color);
        color: white;
    }
    
    .control-btn.primary:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
    }
    
    .control-btn.secondary {
        background: #f3f4f6;
        color: var(--text-secondary);
    }
    
    .control-btn.secondary:hover {
        background: #e5e7eb;
    }
    
    /* 统计数据卡片 */
    .stats-card {
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 10px;
        padding: 10px;
        flex-shrink: 0;
    }
    
    .stats-title {
        font-size: 16px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 8px;
    }

    .progress-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 8px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 8px;
        border-left: 2px solid rgba(255, 107, 157, 0.8);
    }
    
    .progress-item:last-child {
        margin-bottom: 0;
    }
    
    .progress-info {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .progress-icon {
        width: 18px;
        height: 18px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 9px;
        color: white;
    }
    
    .progress-icon.running {
        background: var(--primary-color);
        animation: spin 1s linear infinite;
    }
    
    .progress-icon.completed {
        background: var(--success-color);
    }
    
    .progress-icon.error {
        background: var(--error-color);
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    .progress-text {
        font-size: 10px;
        color: rgba(255, 255, 255, 0.9);
    }
    
    .progress-count {
        font-size: 9px;
        color: rgba(255, 255, 255, 0.8);
        background: rgba(255, 255, 255, 0.15);
        padding: 2px 4px;
        border-radius: 6px;
        font-weight: 500;
    }
    
    /* 快速统计 - 紧凑版 */
    .quick-stats {
        display: flex;
        gap: 6px;
        margin-top: 6px;
    }
    
    .quick-stat {
        flex: 1;
        text-align: center;
        background: rgba(255, 255, 255, 0.08);
        padding: 6px 4px;
        border-radius: 6px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .quick-stat-value {
        font-size: 14px;
        font-weight: 700;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 2px;
        line-height: 1;
    }
    
    .quick-stat-label {
        font-size: 8px;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.3px;
        line-height: 1;
    }
    
    /* 任务队列卡片 */
    .task-queue-card {
        background: rgba(255, 255, 255, 0.12);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        flex: 1;
        display: flex;
        flex-direction: column;
    }
    
    .queue-header {
        padding: 10px;
        background: rgba(255, 255, 255, 0.05);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 12px;
        font-weight: 600;
        color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-shrink: 0;
    }
    
    .queue-count {
        background: rgba(255, 107, 157, 0.8);
        color: white;
        padding: 2px 5px;
        border-radius: 6px;
        font-size: 9px;
        font-weight: 500;
    }
    
    .task-list {
        flex: 1;
        overflow-y: auto;
        padding: 0;
    }
    
    .task-item {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        transition: all 0.2s ease;
    }
    
    .task-item:hover {
        background: rgba(255, 255, 255, 0.08);
    }
    
    .task-item:last-child {
        border-bottom: none;
    }
    
    .task-info {
        flex: 1;
        margin-left: 6px;
    }
    
    .task-title {
        font-size: 10px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 2px;
        line-height: 1.3;
    }
    
    .task-subtitle {
        font-size: 8px;
        color: rgba(255, 255, 255, 0.6);
        line-height: 1.2;
    }
    
    .task-priority {
        padding: 2px 3px;
        border-radius: 3px;
        font-size: 7px;
        font-weight: 500;
        text-transform: uppercase;
        min-width: 18px;
        text-align: center;
    }
    
    .task-priority.high {
        background: #fee2e2;
        color: #dc2626;
    }
    
    .task-priority.medium {
        background: #fef3c7;
        color: #d97706;
    }
    
    .task-priority.low {
        background: #f0f9ff;
        color: #0369a1;
    }
    
    /* 响应式设计 */
    @media (max-width: 480px) {
        .plugin-container {
            width: 100%;
            margin: 10px;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
        }
        
        .task-controls {
            flex-direction: column;
            width: 100%;
        }
        
        .control-btn {
            justify-content: center;
        }
    }
    </style>
</head>
<body>
    
    <div class="plugin-container">
        <!-- 插件头部 -->
        <div class="plugin-header">
            <div class="header-content">
                <div class="plugin-title">
                    <div class="plugin-icon">
                        <i class="fab fa-tiktok"></i>
                    </div>
                    <div class="title-text">
                        <h1>TikTok商家建联助手</h1>
                        <p>智能建联·消息监控·任务管理</p>
                    </div>
                </div>
                <!-- 账号信息显示在顶部导航条 -->
                <div class="header-account-info">
                    <div class="header-account-avatar" id="header-account-avatar">用</div>
                    <div class="header-account-details">
                        <div class="header-account-name" id="header-account-name">TikTok商家后台</div>
                        <div class="header-account-email" id="header-account-email"><EMAIL></div>
                    </div>
                    <div class="header-account-status">
                        <i class="fas fa-check-circle"></i>
                        <span>已同步</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 - 左右布局 -->
        <div class="main-content">
            <!-- 左侧面板 -->
            <div class="left-panel">
                <!-- 控制按钮卡片 -->
                <div class="control-card">
                    <div class="control-header">
                        <h3 class="control-title">任务控制</h3>
                    </div>
                    <div class="task-controls">
                        <button class="control-btn primary" id="start-btn">
                            <i class="fas fa-play"></i>
                            启动
                        </button>
                        <button class="control-btn secondary" id="pause-btn">
                            <i class="fas fa-pause"></i>
                            暂停
                        </button>
                    </div>
                </div>

                <!-- 任务进度卡片 - 包含插件状态 -->
                <div class="stats-card">
                    <h3 class="stats-title">执行状态</h3>
                    <!-- 插件运行状态 -->
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="status-indicator-new" id="plugin-status-indicator"></div>
                            <span class="progress-text">插件状态</span>
                        </div>
                        <span class="progress-count" id="connection-status">已停止</span>
                    </div>
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="progress-icon running">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <span class="progress-text">私信任务</span>
                        </div>
                        <span class="progress-count">3/10</span>
                    </div>
                    <div class="progress-item">
                        <div class="progress-info">
                            <div class="progress-icon completed">
                                <i class="fas fa-check"></i>
                            </div>
                            <span class="progress-text">消息监控</span>
                        </div>
                        <span class="progress-count">实时</span>
                    </div>
                </div>
            </div>

            <!-- 右侧面板 -->
            <div class="right-panel">
                <!-- 任务队列 -->
                <div class="task-queue-card">
                    <div class="queue-header">
                        <span>待执行任务</span>
                        <span class="queue-count" id="queue-count">6</span>
                    </div>
                    <div class="task-list" id="task-list">
                        <div class="task-item">
                            <div class="task-priority high">高</div>
                            <div class="task-info">
                                <div class="task-title">联盟营销邀请 Creator_Sarah</div>
                                <div class="task-subtitle">Seller Center - 达人合作</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-priority medium">中</div>
                            <div class="task-info">
                                <div class="task-title">监控后台私信回复</div>
                                <div class="task-subtitle">seller/im - 3个新对话</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-priority low">低</div>
                            <div class="task-info">
                                <div class="task-title">检查合作伙伴状态</div>
                                <div class="task-subtitle">Partnership Dashboard</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-priority medium">中</div>
                            <div class="task-info">
                                <div class="task-title">处理商家后台验证</div>
                                <div class="task-subtitle">Auto-captcha 处理中...</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-priority low">低</div>
                            <div class="task-info">
                                <div class="task-title">数据同步检查</div>
                                <div class="task-subtitle">商家中心 - 库存同步</div>
                            </div>
                        </div>
                        <div class="task-item">
                            <div class="task-priority high">高</div>
                            <div class="task-info">
                                <div class="task-title">新达人建联</div>
                                <div class="task-subtitle">Creator Program - 邀请发送</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="popup.js"></script>
</body>
</html>