/**
 * TikTok建联插件 - 页面注入脚本
 * 在TikTok页面的JavaScript环境中运行，获取深层数据
 */

(function() {
    'use strict';
    
    // 防止重复注入
    if (window.tikTokPluginInjected) {
        return;
    }
    window.tikTokPluginInjected = true;
    
    console.log('TikTok插件页面脚本已注入');
    
    // 存储原始函数引用
    const originalFetch = window.fetch;
    const originalXHR = XMLHttpRequest.prototype.open;
    
    // 数据收集器
    const dataCollector = {
        userProfiles: new Map(),
        conversations: new Map(),
        messages: []
    };
    
    /**
     * 拦截fetch请求
     */
    window.fetch = function(...args) {
        const [url, options] = args;
        
        // 发起原始请求
        const promise = originalFetch.apply(this, args);
        
        // 拦截特定API响应
        if (typeof url === 'string') {
            promise.then(response => {
                const clonedResponse = response.clone();
                handleAPIResponse(url, clonedResponse);
            }).catch(() => {
                // 忽略错误
            });
        }
        
        return promise;
    };
    
    /**
     * 拦截XMLHttpRequest
     */
    XMLHttpRequest.prototype.open = function(method, url, ...rest) {
        this._pluginUrl = url;
        this._pluginMethod = method;
        
        // 监听响应
        this.addEventListener('readystatechange', function() {
            if (this.readyState === 4 && this.status === 200) {
                handleAPIResponse(this._pluginUrl, {
                    json: () => Promise.resolve(JSON.parse(this.responseText))
                });
            }
        });
        
        return originalXHR.apply(this, [method, url, ...rest]);
    };
    
    /**
     * 处理API响应
     */
    async function handleAPIResponse(url, response) {
        try {
            // 用户资料API
            if (url.includes('/api/user/detail/') || url.includes('/api/user/profile/')) {
                const data = await response.json();
                if (data.userInfo) {
                    collectUserProfile(data.userInfo);
                }
            }
            
            // 消息列表API
            if (url.includes('/api/message/conversation/') || url.includes('/api/im/')) {
                const data = await response.json();
                if (data.messages || data.messageList) {
                    collectMessages(data.messages || data.messageList);
                }
            }
            
            // 对话列表API
            if (url.includes('/api/message/conversation_list/') || url.includes('/api/im/conversation/')) {
                const data = await response.json();
                if (data.conversations || data.conversationList) {
                    collectConversations(data.conversations || data.conversationList);
                }
            }
            
        } catch (error) {
            // 忽略解析错误
        }
    }
    
    /**
     * 收集用户资料数据
     */
    function collectUserProfile(userInfo) {
        const profile = {
            id: userInfo.id || userInfo.uid,
            username: userInfo.uniqueId || userInfo.username,
            nickname: userInfo.nickname || userInfo.displayName,
            avatar: userInfo.avatarThumb || userInfo.avatar,
            followers: userInfo.followerCount || userInfo.followers,
            following: userInfo.followingCount || userInfo.following,
            verified: userInfo.verified || false,
            bio: userInfo.signature || userInfo.bio,
            timestamp: Date.now()
        };
        
        dataCollector.userProfiles.set(profile.id, profile);
        
        // 通知content script
        notifyContentScript('USER_PROFILE_COLLECTED', profile);
    }
    
    /**
     * 收集消息数据
     */
    function collectMessages(messages) {
        if (!Array.isArray(messages)) return;
        
        messages.forEach(message => {
            const msgData = {
                id: message.id || message.messageId,
                conversationId: message.conversationId || message.chatId,
                from: message.from || message.senderId,
                to: message.to || message.receiverId,
                content: message.content || message.text,
                type: message.type || 'text',
                timestamp: message.timestamp || message.createTime,
                read: message.read || false
            };
            
            dataCollector.messages.push(msgData);
            
            // 如果是新消息，通知content script
            if (!message.read && message.from !== getCurrentUserId()) {
                notifyContentScript('NEW_MESSAGE_DETECTED', msgData);
            }
        });
    }
    
    /**
     * 收集对话数据
     */
    function collectConversations(conversations) {
        if (!Array.isArray(conversations)) return;
        
        conversations.forEach(conversation => {
            const convData = {
                id: conversation.id || conversation.conversationId,
                partnerId: conversation.partnerId || conversation.userId,
                partnerName: conversation.partnerName || conversation.username,
                partnerAvatar: conversation.partnerAvatar || conversation.avatar,
                lastMessage: conversation.lastMessage || conversation.latestMessage,
                lastTime: conversation.lastTime || conversation.updateTime,
                unreadCount: conversation.unreadCount || 0
            };
            
            dataCollector.conversations.set(convData.id, convData);
        });
    }
    
    /**
     * 获取当前用户ID
     */
    function getCurrentUserId() {
        // 尝试从全局对象获取
        if (window.__INITIAL_STATE__ && window.__INITIAL_STATE__.user) {
            return window.__INITIAL_STATE__.user.id;
        }
        
        // 尝试从localStorage获取
        try {
            const userData = localStorage.getItem('user_info') || localStorage.getItem('currentUser');
            if (userData) {
                const user = JSON.parse(userData);
                return user.id || user.uid;
            }
        } catch (e) {
            // 忽略错误
        }
        
        return null;
    }
    
    /**
     * 通知content script
     */
    function notifyContentScript(type, data) {
        window.postMessage({
            source: 'tiktok-plugin-injected',
            type: type,
            data: data
        }, '*');
    }
    
    /**
     * 监听来自content script的消息
     */
    window.addEventListener('message', function(event) {
        if (event.data.source !== 'tiktok-plugin-content') {
            return;
        }
        
        switch (event.data.type) {
            case 'GET_USER_PROFILES':
                notifyContentScript('USER_PROFILES_DATA', Array.from(dataCollector.userProfiles.values()));
                break;
                
            case 'GET_CONVERSATIONS':
                notifyContentScript('CONVERSATIONS_DATA', Array.from(dataCollector.conversations.values()));
                break;
                
            case 'GET_MESSAGES':
                notifyContentScript('MESSAGES_DATA', dataCollector.messages);
                break;
                
            case 'CLEAR_DATA':
                dataCollector.userProfiles.clear();
                dataCollector.conversations.clear();
                dataCollector.messages = [];
                break;
        }
    });
    
    /**
     * 页面数据监控
     */
    function startPageMonitoring() {
        // 监控DOM变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 检测新的消息元素
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        checkForNewMessages(node);
                        checkForUserProfiles(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * 检查新消息
     */
    function checkForNewMessages(element) {
        // 查找消息元素
        const messageElements = element.querySelectorAll ? 
            element.querySelectorAll('[data-e2e="message"], .message-item') : [];
        
        messageElements.forEach(msgEl => {
            if (msgEl.dataset.pluginProcessed) return;
            msgEl.dataset.pluginProcessed = 'true';
            
            const messageData = extractMessageData(msgEl);
            if (messageData && messageData.from !== getCurrentUserId()) {
                notifyContentScript('NEW_MESSAGE_DETECTED', messageData);
            }
        });
    }
    
    /**
     * 检查用户资料
     */
    function checkForUserProfiles(element) {
        // 查找用户卡片元素
        const userElements = element.querySelectorAll ? 
            element.querySelectorAll('[data-e2e="user-card"], .user-card') : [];
        
        userElements.forEach(userEl => {
            if (userEl.dataset.pluginProcessed) return;
            userEl.dataset.pluginProcessed = 'true';
            
            const userData = extractUserData(userEl);
            if (userData) {
                collectUserProfile(userData);
            }
        });
    }
    
    /**
     * 提取消息数据
     */
    function extractMessageData(element) {
        try {
            const textEl = element.querySelector('.message-text, [data-e2e="message-text"]');
            const timeEl = element.querySelector('.message-time, [data-e2e="message-time"]');
            const senderEl = element.querySelector('.sender-name, [data-e2e="sender"]');
            
            return {
                id: 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                content: textEl ? textEl.textContent.trim() : '',
                timestamp: timeEl ? new Date(timeEl.textContent).getTime() : Date.now(),
                from: senderEl ? senderEl.textContent.trim() : 'unknown',
                element: element.outerHTML.substring(0, 200) + '...'
            };
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 提取用户数据
     */
    function extractUserData(element) {
        try {
            const usernameEl = element.querySelector('.username, [data-e2e="username"]');
            const nicknameEl = element.querySelector('.nickname, [data-e2e="nickname"]');
            const followersEl = element.querySelector('.followers, [data-e2e="followers"]');
            const avatarEl = element.querySelector('img[src*="avatar"], img[alt*="avatar"]');
            
            return {
                id: 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                username: usernameEl ? usernameEl.textContent.trim() : '',
                nickname: nicknameEl ? nicknameEl.textContent.trim() : '',
                followers: followersEl ? parseFollowersText(followersEl.textContent) : 0,
                avatar: avatarEl ? avatarEl.src : '',
                element: element.outerHTML.substring(0, 200) + '...'
            };
        } catch (error) {
            return null;
        }
    }
    
    /**
     * 解析粉丝数文本
     */
    function parseFollowersText(text) {
        if (!text) return 0;
        
        const match = text.match(/([\d.]+)\s*([KM]?)/i);
        if (!match) return 0;
        
        const num = parseFloat(match[1]);
        const unit = match[2].toUpperCase();
        
        if (unit === 'K') return num * 1000;
        if (unit === 'M') return num * 1000000;
        return num;
    }
    
    // 启动监控
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', startPageMonitoring);
    } else {
        startPageMonitoring();
    }
    
    // 暴露调试接口
    window.tikTokPluginDebug = {
        dataCollector,
        getCurrentUserId,
        notifyContentScript
    };
    
})();