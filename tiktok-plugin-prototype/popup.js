/**
 * TikTok建联插件 - 弹窗脚本
 * 处理插件弹窗的用户界面和与background script的通信
 */

// 全局状态
let pluginState = {
    isActive: false,
    currentTasks: 0,
    statistics: {
        totalTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
        messagesCollected: 0
    }
};

/**
 * 页面初始化
 */
document.addEventListener('DOMContentLoaded', async function() {
    console.log('TikTok建联插件弹窗已加载');
    
    // 初始化UI事件监听器
    initEventListeners();
    
    // 获取插件状态
    await loadPluginStatus();
    
    // 启动状态更新循环
    startStatusUpdateLoop();
    
    // 显示欢迎信息
    showWelcomeMessage();
});

/**
 * 初始化事件监听器
 */
function initEventListeners() {
    // 切换按钮
    const toggleBtn = document.getElementById('toggle-btn');

    if (toggleBtn) {
        toggleBtn.addEventListener('click', togglePlugin);
    }
    
    // 监听来自background script的消息
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        switch (message.type) {
            case 'STATUS_CHANGED':
                updatePluginStatus(message.isActive);
                break;
            case 'STATISTICS_UPDATED':
                updateStatistics(message.statistics);
                break;
            case 'TASK_PROGRESS':
                updateTaskProgress(message.progress);
                break;
        }
    });
}

/**
 * 加载插件状态
 */
async function loadPluginStatus() {
    try {
        // 从background script获取状态
        const response = await chrome.runtime.sendMessage({ type: 'GET_STATUS' });
        pluginState.isActive = response.isActive;
        
        // 获取统计数据
        const statistics = await chrome.runtime.sendMessage({ type: 'GET_STATISTICS' });
        if (statistics) {
            pluginState.statistics = statistics;
        }
        
        // 获取用户登录信息
        await loadUserInfo();
        
        // 更新UI
        updateUI();
        
    } catch (error) {
        console.error('加载插件状态失败:', error);
        showErrorMessage('无法连接到插件后台');
    }
}

/**
 * 切换插件状态
 */
async function togglePlugin() {
    try {
        const newState = !pluginState.isActive;
        
        // 发送状态变更消息到background script
        await chrome.runtime.sendMessage({
            type: 'SET_ACTIVE',
            active: newState
        });
        
        pluginState.isActive = newState;
        updateUI();
        
        showNotification(newState ? '插件已启动' : '插件已停止');
        
    } catch (error) {
        console.error('切换插件状态失败:', error);
        showErrorMessage('操作失败，请重试');
    }
}

/**
 * 暂停插件
 */
async function pausePlugin() {
    if (!pluginState.isActive) return;
    
    try {
        await chrome.runtime.sendMessage({
            type: 'SET_ACTIVE',
            active: false
        });
        
        pluginState.isActive = false;
        updateUI();
        
        showNotification('插件已暂停');
        
    } catch (error) {
        console.error('暂停插件失败:', error);
    }
}

/**
 * 加载用户信息 - 简化版本，检测不到就提示登录
 */
async function loadUserInfo() {
    try {
        // 尝试获取TuringMarket用户信息
        const turingMarketUser = await chrome.runtime.sendMessage({ type: 'GET_TURINGMARKET_USER' });
        
        if (turingMarketUser && turingMarketUser.isLoggedIn) {
            updateUserDisplay(turingMarketUser, 'turingmarket');
            console.log('TuringMarket用户信息已加载:', turingMarketUser);
            return;
        }
        
        // 尝试获取TikTok登录信息
        const tiktokUser = await chrome.runtime.sendMessage({ type: 'GET_USER_INFO' });
        
        if (tiktokUser && tiktokUser.isLoggedIn) {
            updateUserDisplay(tiktokUser, 'tiktok');
            console.log('TikTok用户信息已加载:', tiktokUser);
            return;
        }
        
        // 如果都没有登录信息，显示登录提示
        showLoginPrompt();
        
    } catch (error) {
        console.error('加载用户信息失败:', error);
        showLoginPrompt();
    }
}

/**
 * 显示登录提示界面
 */
function showLoginPrompt() {
    // 更新头部显示未登录状态
    const headerAvatar = document.getElementById('header-account-avatar');
    const headerName = document.getElementById('header-account-name');
    const headerEmail = document.getElementById('header-account-email');
    
    if (headerAvatar) headerAvatar.textContent = '未';
    if (headerName) headerName.textContent = '需要登录';
    if (headerEmail) {
        headerEmail.innerHTML = '点击登录到 <span style="color: #8b5cf6; cursor: pointer;" onclick="openTuringMarket()">TuringMarket</span>';
    }
    
    // 显示登录提示通知
    showNotification('请先登录 TuringMarket 或 TikTok 商家后台', 'warning');
    
    console.log('显示登录提示');
}

/**
 * 打开TuringMarket登录页面
 */
function openTuringMarket() {
    chrome.tabs.create({
        url: 'https://agent.turingmarket.ai/'
    });
    
    showNotification('正在打开 TuringMarket...', 'info');
}

/**
 * 更新用户信息显示 - 聚星式双账户显示
 */
function updateUserDisplay(userInfo, source = 'tiktok') {
    // 更新头部用户信息
    const headerAvatar = document.getElementById('header-account-avatar');
    const headerName = document.getElementById('header-account-name');
    const headerEmail = document.getElementById('header-account-email');
    
    if (headerAvatar && headerName) {
        // 如果有头像URL，显示真实头像；否则显示用户名首字母
        if (userInfo.avatarUrl) {
            headerAvatar.innerHTML = `<img src="${userInfo.avatarUrl}" alt="头像" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">`;
        } else {
            const firstLetter = userInfo.username ? userInfo.username.charAt(0).toUpperCase() : '用';
            headerAvatar.textContent = firstLetter;
        }
        
        // 根据来源显示不同的信息
        if (source === 'turingmarket') {
            // TuringMarket用户信息优先显示
            headerName.textContent = userInfo.username || 'TuringMarket用户';
            
            if (headerEmail) {
                const displayInfo = userInfo.email || 'agent.turingmarket.ai';
                headerEmail.textContent = displayInfo;
            }
            
            // 更新头像样式为TuringMarket主题色
            headerAvatar.style.background = 'linear-gradient(135deg, #8b5cf6, #7c3aed)';
            
        } else if (source === 'tiktok') {
            // TikTok用户信息
            headerName.textContent = userInfo.shopName || userInfo.username || 'TikTok商家';
            
            if (headerEmail) {
                const displayEmail = userInfo.email || `${userInfo.region || 'US'} • ${userInfo.sellerId || '商家后台'}`;
                headerEmail.textContent = displayEmail;
            }
            
            // 恢复默认头像样式
            headerAvatar.style.background = 'linear-gradient(135deg, var(--primary-color), var(--primary-dark))';
        }
    }
    
    // 更新插件状态为已连接
    const statusElement = document.getElementById('connection-status');
    if (statusElement && userInfo.isLoggedIn) {
        const statusText = source === 'turingmarket' ? '已连接TuringMarket' : '已连接TikTok';
        statusElement.textContent = statusText;
    }
    
    // 更新头部账号状态
    const headerStatusElement = document.querySelector('.header-account-status');
    if (headerStatusElement && userInfo.isLoggedIn) {
        headerStatusElement.innerHTML = `
            <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
            <span style="color: var(--success-color);">已登录</span>
        `;
    }
    
    console.log(`${source}用户信息显示已更新:`, userInfo.username);
}

// showDefaultUserInfo函数已移除 - 默认状态现在在HTML中设置

/**
 * 更新UI显示
 */
function updateUI() {
    updatePluginStatus(pluginState.isActive);
    updateStatistics(pluginState.statistics);
    updateControlButtons();
}

/**
 * 更新插件状态显示
 */
function updatePluginStatus(isActive, isPaused = false) {
    const statusElement = document.getElementById('connection-status');

    if (statusElement) {
        // 移除所有状态类
        statusElement.classList.remove('stopped', 'running', 'paused');

        if (isActive && !isPaused) {
            statusElement.textContent = '执行中';
            statusElement.classList.add('running');
        } else if (isActive && isPaused) {
            statusElement.textContent = '暂停';
            statusElement.classList.add('paused');
        } else {
            statusElement.textContent = '已停止';
            statusElement.classList.add('stopped');
        }
    }
}

/**
 * 更新统计数据
 */
function updateStatistics(statistics) {
    if (!statistics) return;
    
    const elements = {
        totalTasks: document.getElementById('total-tasks'),
        completedTasks: document.getElementById('completed-tasks'),
        successRate: document.getElementById('success-rate')
    };
    
    Object.keys(elements).forEach(key => {
        const element = elements[key];
        if (element) {
            if (key === 'successRate') {
                const rate = statistics.totalTasks > 0 
                    ? Math.round((statistics.completedTasks / statistics.totalTasks) * 100)
                    : 0;
                element.textContent = rate + '%';
            } else {
                element.textContent = statistics[key] || 0;
            }
        }
    });
    
    pluginState.statistics = statistics;
}

/**
 * 更新控制按钮
 */
function updateControlButtons() {
    const toggleBtn = document.getElementById('toggle-btn');
    const toggleIcon = document.getElementById('toggle-icon');
    const toggleText = document.getElementById('toggle-text');

    if (toggleBtn && toggleIcon && toggleText) {
        if (pluginState.isActive) {
            toggleIcon.className = 'fas fa-stop';
            toggleText.textContent = '停止';
            toggleBtn.className = 'control-btn secondary';
        } else {
            toggleIcon.className = 'fas fa-play';
            toggleText.textContent = '启动';
            toggleBtn.className = 'control-btn primary';
        }
    }
}

/**
 * 更新任务进度
 */
function updateTaskProgress(progress) {
    // 更新进度条和任务状态显示
    const progressItems = document.querySelectorAll('.progress-item');
    
    if (progressItems.length > 0) {
        // 更新第一个进度项（建联任务）
        const firstProgress = progressItems[0];
        const countElement = firstProgress.querySelector('.progress-count');
        const iconElement = firstProgress.querySelector('.progress-icon');
        
        if (countElement) {
            countElement.textContent = `${progress.current}/${progress.total}`;
        }
        
        if (iconElement) {
            if (progress.current > 0 && progress.current < progress.total) {
                iconElement.className = 'progress-icon running';
            } else if (progress.current === progress.total) {
                iconElement.className = 'progress-icon completed';
            }
        }
    }
}

/**
 * 启动状态更新循环
 */
function startStatusUpdateLoop() {
    setInterval(async () => {
        try {
            // 定期获取最新统计数据
            const statistics = await chrome.runtime.sendMessage({ type: 'GET_STATISTICS' });
            if (statistics) {
                updateStatistics(statistics);
            }
        } catch (error) {
            // 静默处理错误，避免干扰用户
            console.log('状态更新失败:', error.message);
        }
    }, 2000); // 每2秒更新一次
}

/**
 * 显示欢迎消息
 */
function showWelcomeMessage() {
    // 检查是否是首次使用
    chrome.storage.local.get(['firstTime'], (result) => {
        if (result.firstTime !== false) {
            showNotification('欢迎使用TikTok建联助手！');
            
            // 标记为已使用
            chrome.storage.local.set({ firstTime: false });
        }
    });
}

/**
 * 显示通知
 */
function showNotification(message, type = 'info') {
    // 根据类型选择颜色
    let backgroundColor;
    switch (type) {
        case 'success':
            backgroundColor = 'var(--success-color)';
            break;
        case 'warning':
            backgroundColor = 'var(--warning-color)';
            break;
        case 'error':
            backgroundColor = 'var(--error-color)';
            break;
        default:
            backgroundColor = 'var(--primary-color)';
    }
    
    // 创建通知元素
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 10px;
        right: 10px;
        background: ${backgroundColor};
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 12px;
        z-index: 1000;
        animation: fadeIn 0.15s ease;
        max-width: 200px;
        word-wrap: break-word;
    `;
    
    notification.textContent = message;
    document.body.appendChild(notification);
    
    // 自动消失时间根据类型调整
    const duration = type === 'warning' ? 3000 : 2000;
    
    setTimeout(() => {
        notification.style.animation = 'fadeOut 0.15s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 150);
    }, duration);
    
    // 添加动画样式
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; transform: scale(0.9); }
                to { opacity: 1; transform: scale(1); }
            }
            @keyframes fadeOut {
                from { opacity: 1; transform: scale(1); }
                to { opacity: 0; transform: scale(0.9); }
            }
        `;
        document.head.appendChild(style);
    }
}

/**
 * 显示错误消息
 */
function showErrorMessage(message) {
    showNotification(message, 'error');
}

/**
 * 打开后端管理页面
 */
function openBackendPages() {
    // 打开任务分发页面
    chrome.tabs.create({
        url: 'http://localhost:8080/task-dispatch.html'
    });
    
    // 打开消息收集页面
    chrome.tabs.create({
        url: 'http://localhost:8080/message-collect.html'
    });
}

/**
 * 查看使用帮助
 */
function showHelp() {
    const helpContent = `
        TikTok建联助手使用说明：
        
        1. 点击"启动"按钮开始运行插件
        2. 插件会自动接收后端分发的任务
        3. 在TikTok页面上自动执行建联操作
        4. 实时监控网红回复并上传到后端
        
        快捷键：
        - Ctrl+Shift+T: 快速启动/停止
        
        如需技术支持，请联系开发团队。
    `;
    
    alert(helpContent);
}

/**
 * 导出数据
 */
async function exportData() {
    try {
        const data = {
            statistics: pluginState.statistics,
            timestamp: new Date().toISOString(),
            version: '1.0.0'
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `tiktok-plugin-data-${new Date().toISOString().split('T')[0]}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        showNotification('数据已导出');
        
    } catch (error) {
        showErrorMessage('导出失败');
    }
}

/**
 * 设置插件配置
 */
function openSettings() {
    // 这里可以打开设置页面或显示设置面板
    showNotification('设置功能即将推出');
}

// 全局函数，供HTML调用
window.togglePlugin = togglePlugin;
window.pausePlugin = pausePlugin;
window.openBackendPages = openBackendPages;
window.showHelp = showHelp;
window.exportData = exportData;
window.openSettings = openSettings;
window.openTuringMarket = openTuringMarket;

// 添加键盘快捷键支持
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        togglePlugin();
    }
});

console.log('TikTok建联插件弹窗脚本已加载');