/**
 * TikTok建联插件 - 内容脚本样式
 */

/* 状态指示器样式 */
#tiktok-plugin-indicator {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    width: 12px !important;
    height: 12px !important;
    background: #10b981 !important;
    border-radius: 50% !important;
    z-index: 10000 !important;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5) !important;
    display: none !important;
    pointer-events: none !important;
}

/* 脉搏动画 */
@keyframes tiktok-plugin-pulse {
    0%, 100% { 
        opacity: 1;
        transform: scale(1);
    }
    50% { 
        opacity: 0.7;
        transform: scale(1.1);
    }
}

#tiktok-plugin-indicator.active {
    animation: tiktok-plugin-pulse 2s infinite !important;
}

/* 任务执行时的高亮样式 */
.tiktok-plugin-highlight {
    outline: 2px solid #ff0050 !important;
    outline-offset: 2px !important;
    animation: tiktok-plugin-highlight-pulse 1s ease-in-out infinite !important;
}

@keyframes tiktok-plugin-highlight-pulse {
    0%, 100% { outline-color: #ff0050; }
    50% { outline-color: #ff6b9d; }
}

/* 消息提示框 */
.tiktok-plugin-tooltip {
    position: fixed !important;
    background: rgba(0, 0, 0, 0.8) !important;
    color: white !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    font-size: 12px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    z-index: 9999 !important;
    pointer-events: none !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
}

.tiktok-plugin-tooltip.show {
    opacity: 1 !important;
}

/* 验证码检测提示 */
.tiktok-plugin-captcha-detected {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background: white !important;
    border: 2px solid #ff0050 !important;
    border-radius: 12px !important;
    padding: 20px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
    z-index: 10001 !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    text-align: center !important;
}

.tiktok-plugin-captcha-detected h3 {
    margin: 0 0 10px 0 !important;
    color: #ff0050 !important;
    font-size: 16px !important;
}

.tiktok-plugin-captcha-detected p {
    margin: 0 !important;
    color: #666 !important;
    font-size: 14px !important;
}

/* 执行状态覆盖层 */
.tiktok-plugin-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    background: rgba(0, 0, 0, 0.3) !important;
    z-index: 9998 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
}

.tiktok-plugin-overlay.show {
    display: flex !important;
}

.tiktok-plugin-status-card {
    background: white !important;
    border-radius: 12px !important;
    padding: 24px !important;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
    text-align: center !important;
    min-width: 200px !important;
}

.tiktok-plugin-status-card h3 {
    margin: 0 0 12px 0 !important;
    color: #333 !important;
    font-size: 18px !important;
    font-weight: 600 !important;
}

.tiktok-plugin-status-card p {
    margin: 0 !important;
    color: #666 !important;
    font-size: 14px !important;
}

.tiktok-plugin-spinner {
    width: 24px !important;
    height: 24px !important;
    border: 3px solid #f3f3f3 !important;
    border-top: 3px solid #ff0050 !important;
    border-radius: 50% !important;
    animation: tiktok-plugin-spin 1s linear infinite !important;
    margin: 0 auto 12px auto !important;
}

@keyframes tiktok-plugin-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏元素的通用类 */
.tiktok-plugin-hidden {
    display: none !important;
}

/* 成功状态样式 */
.tiktok-plugin-success {
    color: #10b981 !important;
}

.tiktok-plugin-success-bg {
    background-color: #10b981 !important;
}

/* 错误状态样式 */
.tiktok-plugin-error {
    color: #ef4444 !important;
}

.tiktok-plugin-error-bg {
    background-color: #ef4444 !important;
}

/* 警告状态样式 */
.tiktok-plugin-warning {
    color: #f59e0b !important;
}

.tiktok-plugin-warning-bg {
    background-color: #f59e0b !important;
}

/* 防止与TikTok样式冲突 */
[data-tiktok-plugin] {
    all: unset !important;
}