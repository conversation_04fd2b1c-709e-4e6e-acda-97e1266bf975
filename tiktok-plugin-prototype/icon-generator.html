<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TikTok插件图标生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 800px;
            width: 100%;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .preview-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .preview-item h3 {
            margin: 0 0 15px 0;
            color: #495057;
            font-size: 14px;
        }
        
        canvas {
            border: 1px solid #dee2e6;
            border-radius: 4px;
            background: white;
        }
        
        .download-section {
            text-align: center;
            margin-top: 30px;
        }
        
        .download-btn {
            background: linear-gradient(135deg, #FF0050, #00F2EA);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 5px;
            transition: transform 0.2s ease;
        }
        
        .download-btn:hover {
            transform: translateY(-2px);
        }
        
        .instructions {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 4px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 TikTok插件图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明</h3>
            <p>1. 点击"生成图标"按钮创建所有尺寸的图标</p>
            <p>2. 右键点击图标选择"图片另存为"保存到icons文件夹</p>
            <p>3. 替换现有的图标文件后重新加载插件</p>
        </div>
        
        <div class="preview-grid">
            <div class="preview-item">
                <h3>16×16 (工具栏图标)</h3>
                <canvas id="icon16" width="16" height="16"></canvas>
            </div>
            <div class="preview-item">
                <h3>32×32 (扩展管理)</h3>
                <canvas id="icon32" width="32" height="32"></canvas>
            </div>
            <div class="preview-item">
                <h3>48×48 (扩展详情)</h3>
                <canvas id="icon48" width="48" height="48"></canvas>
            </div>
            <div class="preview-item">
                <h3>128×128 (Chrome商店)</h3>
                <canvas id="icon128" width="128" height="128"></canvas>
            </div>
        </div>
        
        <div class="download-section">
            <button class="download-btn" onclick="generateIcons()">🎨 生成图标</button>
            <button class="download-btn" onclick="downloadAll()">📥 下载全部</button>
        </div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const center = size / 2;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制圆形背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#FF0050');
            gradient.addColorStop(0.5, '#FF4081');
            gradient.addColorStop(1, '#00F2EA');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(center, center, center - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制音符符号
            ctx.fillStyle = 'white';
            const noteSize = size * 0.6;
            const noteX = center - noteSize * 0.2;
            const noteY = center - noteSize * 0.3;
            
            // 音符主体
            ctx.beginPath();
            ctx.ellipse(noteX, noteY + noteSize * 0.6, noteSize * 0.15, noteSize * 0.1, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // 音符杆
            ctx.fillRect(noteX + noteSize * 0.12, noteY, noteSize * 0.06, noteSize * 0.6);
            
            // 音符旗
            ctx.beginPath();
            ctx.moveTo(noteX + noteSize * 0.18, noteY);
            ctx.quadraticCurveTo(noteX + noteSize * 0.4, noteY - noteSize * 0.1, noteX + noteSize * 0.35, noteY + noteSize * 0.2);
            ctx.quadraticCurveTo(noteX + noteSize * 0.25, noteY + noteSize * 0.15, noteX + noteSize * 0.18, noteY + noteSize * 0.25);
            ctx.fill();
            
            // 添加小工具图标（表示助手功能）
            if (size >= 32) {
                const gearSize = size * 0.2;
                const gearX = center + size * 0.25;
                const gearY = center + size * 0.25;
                
                ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.beginPath();
                ctx.arc(gearX, gearY, gearSize * 0.5, 0, 2 * Math.PI);
                ctx.fill();
                
                // 齿轮齿
                for (let i = 0; i < 8; i++) {
                    const angle = (i * Math.PI) / 4;
                    const x1 = gearX + Math.cos(angle) * gearSize * 0.3;
                    const y1 = gearY + Math.sin(angle) * gearSize * 0.3;
                    const x2 = gearX + Math.cos(angle) * gearSize * 0.6;
                    const y2 = gearY + Math.sin(angle) * gearSize * 0.6;
                    
                    ctx.beginPath();
                    ctx.moveTo(x1, y1);
                    ctx.lineTo(x2, y2);
                    ctx.lineWidth = gearSize * 0.1;
                    ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                    ctx.stroke();
                }
                
                // 齿轮中心
                ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
                ctx.beginPath();
                ctx.arc(gearX, gearY, gearSize * 0.2, 0, 2 * Math.PI);
                ctx.fill();
            }
        }
        
        function generateIcons() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                drawIcon(canvas, size);
            });
        }
        
        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadAll() {
            const sizes = [16, 32, 48, 128];
            sizes.forEach(size => {
                const canvas = document.getElementById(`icon${size}`);
                downloadCanvas(canvas, `icon-${size}.png`);
            });
        }
        
        // 页面加载时自动生成图标
        window.onload = function() {
            generateIcons();
        };
    </script>
</body>
</html>
