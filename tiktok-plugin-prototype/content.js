/**
 * TikTok商家建联插件 - 内容脚本
 * 在TikTok商家后台页面上执行具体的自动化操作
 */

// 防止重复注入
if (window.tikTokPluginContentLoaded) {
    console.log('TikTok商家插件content script已加载，跳过重复注入');
} else {
    window.tikTokPluginContentLoaded = true;

    // 配置
    const CONFIG = {
        // 操作延迟配置
        delays: {
            typing: 100,        // 打字间隔
            click: 500,         // 点击后等待
            search: 2000,       // 搜索后等待
            navigation: 3000,   // 页面导航等待
            message: 1000       // 消息发送等待
        },
        
        // 商家后台选择器配置 - 参考达秘插件的特别支持
        selectors: {
            // 私信页面专用选择器 (seller/im路径)
            messageInput: 'textarea[placeholder*="输入消息"], #im_sdk_chat_input textarea, .message-input textarea',
            sendButton: 'button[data-e2e="send"], .send-button, .btn-send-message',
            messageList: '.message-list, .chat-list, .conversation-list',
            newMessage: '.new-message, .unread-message, [data-e2e="new-message"]',
            
            // 联盟营销页面选择器
            searchBox: 'input[placeholder*="搜索"], input[placeholder*="Search"], .search-input',
            searchButton: 'button[data-e2e="search"], .search-button',
            userCard: '.creator-card, .influencer-card, [data-e2e="creator-card"]',
            inviteButton: 'button[data-e2e="invite"], .invite-button, .btn-invite',
            
            // 验证码相关 - 扩展支持更多类型
            captcha: '.captcha, [data-e2e="captcha"], .verify-captcha, .secsdk-captcha, .geetest_holder, .yidun_intelli-mode, .tc-captcha',
            dragCaptcha: '.captcha-drag, .slide-captcha, .secsdk-captcha-drag-icon, .geetest_slider_button, .yidun_slider, .tc-captcha-slider',
            slideCaptcha: '.slide-captcha, .slider-captcha, [class*="slide"], .captcha-slider',
            puzzleCaptcha: '.puzzle-captcha, .jigsaw, .jigsaw-captcha, [class*="puzzle"], .geetest_canvas_img',
            secsdkCaptcha: '.secsdk-captcha-drag-icon, [class*="secsdk"], .secsdk-captcha',
            geetestCaptcha: '.geetest_holder, .geetest_panel, .geetest_slider_button',
            yidunCaptcha: '.yidun_intelli-mode, .yidun_slider, .yidun_panel'
        }
    };

    // 当前任务状态
    let currentTask = null;
    let isExecuting = false;
    let messageObserver = null;

    /**
     * 初始化内容脚本
     */
    function initContentScript() {
        console.log('TikTok商家建联插件内容脚本已加载');
        
        // 检测页面类型
        const pageType = detectPageType();
        console.log('检测到页面类型:', pageType);
        
        // 创建悬浮状态指示器
        createStatusIndicator();
        
        // 开始监控消息
        startMessageMonitoring(pageType);
        
        // 注入页面脚本
        injectPageScript();
        
        // 检测并同步用户登录状态
        checkAndSyncLoginStatus();
    }

    /**
     * 检测当前页面类型
     */
    function detectPageType() {
        const url = window.location.href;
        const hostname = window.location.hostname;
        
        // 商家后台私信页面
        if (url.includes('/seller/im') || url.includes('/messages')) {
            return 'seller_messages';
        }
        
        // 联盟营销页面
        if (hostname.includes('affiliate') || url.includes('/affiliate')) {
            return 'affiliate_marketing';
        }
        
        // TikTok Shop相关
        if (hostname.includes('tiktokshop') || hostname.includes('tiktokglobalshop')) {
            if (url.includes('/seller')) {
                return 'shop_seller';
            } else if (url.includes('/partner')) {
                return 'shop_partner';
            }
        }
        
        return 'unknown';
    }

    /**
     * 创建状态指示器
     */
    function createStatusIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'tiktok-plugin-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #10b981;
            border-radius: 50%;
            z-index: 10000;
            box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
            animation: pulse 2s infinite;
            display: none;
        `;
        
        const style = document.createElement('style');
        style.textContent = `
            @keyframes pulse {
                0%, 100% { opacity: 1; transform: scale(1); }
                50% { opacity: 0.7; transform: scale(1.1); }
            }
        `;
        document.head.appendChild(style);
        document.body.appendChild(indicator);
    }

    /**
     * 显示/隐藏状态指示器
     */
    function showStatusIndicator(show = true) {
        const indicator = document.getElementById('tiktok-plugin-indicator');
        if (indicator) {
            indicator.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * 注入页面脚本
     */
    function injectPageScript() {
        const script = document.createElement('script');
        script.src = chrome.runtime.getURL('injected.js');
        document.head.appendChild(script);
    }

    /**
     * 执行任务
     */
    async function executeTask(task) {
        if (isExecuting) {
            console.log('已有任务在执行中，跳过');
            return { success: false, error: '已有任务在执行中' };
        }

        isExecuting = true;
        currentTask = task;
        showStatusIndicator(true);

        try {
            console.log(`开始执行任务: ${task.type}`);
            let result;

            switch (task.type) {
                case 'search_influencer':
                    result = await searchInfluencer(task.data);
                    break;
                case 'send_message':
                    result = await sendMessage(task.data);
                    break;
                case 'check_reply':
                    result = await checkReply(task.data);
                    break;
                case 'solve_captcha':
                    result = await solveCaptcha(task.data);
                    break;
                default:
                    throw new Error(`未知任务类型: ${task.type}`);
            }

            console.log(`任务完成: ${task.type}`, result);
            
            // 通知后台脚本任务完成
            chrome.runtime.sendMessage({
                type: 'TASK_RESULT',
                taskId: task.id,
                result: { success: true, data: result }
            });

            return { success: true, data: result };

        } catch (error) {
            console.error(`任务执行失败: ${task.type}`, error);
            
            // 通知后台脚本任务失败
            chrome.runtime.sendMessage({
                type: 'TASK_RESULT',
                taskId: task.id,
                result: { success: false, error: error.message }
            });

            return { success: false, error: error.message };

        } finally {
            isExecuting = false;
            currentTask = null;
            showStatusIndicator(false);
        }
    }

    /**
     * 搜索网红
     */
    async function searchInfluencer(data) {
        const { username, category, minFollowers, maxFollowers } = data;
        
        // 1. 检查是否在合适的搜索页面
        if (!window.location.pathname.includes('/search') && 
            !window.location.pathname.includes('/creator') &&
            !window.location.pathname.includes('/affiliate')) {
            console.log('当前页面不支持搜索功能，请手动导航到创作者搜索页面');
            return {
                found: false,
                username: username,
                reason: '需要在创作者搜索页面执行此操作'
            };
        }

        // 2. 在搜索框输入用户名
        const searchBox = await waitForElement(CONFIG.selectors.searchBox);
        await simulateTyping(searchBox, username);
        
        // 3. 点击搜索或按回车
        await simulateKeyPress(searchBox, 'Enter');
        await delay(CONFIG.delays.search);

        // 4. 查找用户卡片
        const userCards = document.querySelectorAll(CONFIG.selectors.userCard);
        
        for (const card of userCards) {
            const usernameText = card.textContent;
            if (usernameText.includes(username) || usernameText.includes('@' + username)) {
                // 找到目标用户
                const followersText = card.textContent.match(/(\d+\.?\d*[KM]?)\s*粉丝|(\d+\.?\d*[KM]?)\s*Followers/i);
                const followersCount = followersText ? parseFollowersCount(followersText[1] || followersText[2]) : 0;
                
                if (followersCount >= minFollowers && followersCount <= maxFollowers) {
                    return {
                        found: true,
                        username: username,
                        followers: followersCount,
                        profile: {
                            url: window.location.href,
                            element: card.outerHTML.substring(0, 200) + '...'
                        }
                    };
                }
            }
        }

        return {
            found: false,
            username: username,
            reason: '未找到符合条件的用户'
        };
    }

    /**
     * 发送消息
     */
    async function sendMessage(data) {
        const { username, message, template } = data;
        
        // 1. 导航到用户主页
        await navigateToUserProfile(username);
        
        // 2. 点击私信按钮
        const messageButton = await waitForElement(CONFIG.selectors.messageButton);
        await simulateClick(messageButton);
        await delay(CONFIG.delays.navigation);
        
        // 3. 检查是否有验证码
        if (await checkForCaptcha()) {
            const captchaResult = await solveCaptcha({ type: 'auto_detect' });
            if (!captchaResult.success) {
                throw new Error('验证码处理失败');
            }
        }
        
        // 4. 在消息输入框输入消息
        const messageInput = await waitForElement(CONFIG.selectors.messageInput);
        await simulateTyping(messageInput, message);
        
        // 5. 发送消息
        const sendButton = await waitForElement(CONFIG.selectors.sendButton);
        await simulateClick(sendButton);
        await delay(CONFIG.delays.message);
        
        return {
            sent: true,
            username: username,
            message: message,
            timestamp: Date.now()
        };
    }

    /**
     * 检查回复
     */
    async function checkReply(data) {
        const { username } = data;
        
        // 1. 导航到消息页面
        await navigateToMessages();
        
        // 2. 查找对话
        const conversationList = document.querySelectorAll('.conversation-item, [data-e2e="conversation"]');
        
        for (const conversation of conversationList) {
            if (conversation.textContent.includes(username)) {
                // 点击进入对话
                await simulateClick(conversation);
                await delay(CONFIG.delays.click);
                
                // 检查是否有新消息
                const messages = document.querySelectorAll('.message-item, [data-e2e="message"]');
                const lastMessage = messages[messages.length - 1];
                
                if (lastMessage && !lastMessage.classList.contains('own-message')) {
                    // 发现新回复
                    const messageText = lastMessage.textContent.trim();
                    const timestamp = extractTimestamp(lastMessage);
                    
                    // 通知后台脚本发现新消息
                    chrome.runtime.sendMessage({
                        type: 'NEW_MESSAGE',
                        message: {
                            from: username,
                            content: messageText,
                            timestamp: timestamp,
                            url: window.location.href
                        }
                    });
                    
                    return {
                        hasReply: true,
                        from: username,
                        content: messageText,
                        timestamp: timestamp
                    };
                }
            }
        }
        
        return {
            hasReply: false,
            username: username,
            lastChecked: Date.now()
        };
    }

    /**
     * 高级验证码处理系统 - 基于e建联最佳实践
     */
    async function solveCaptcha(data) {
        const captchaElement = document.querySelector(CONFIG.selectors.captcha);
        if (!captchaElement) {
            return { success: false, reason: '未找到验证码' };
        }

        console.log('🔍 检测到验证码，开始智能处理...');
        
        // 检测验证码类型
        const captchaType = await detectCaptchaType(captchaElement);
        console.log(`验证码类型: ${captchaType}`);
        
        // 根据类型选择处理策略
        let result;
        switch (captchaType) {
            case 'drag':
                result = await enhancedSolveDragCaptcha(captchaElement);
                break;
            case 'slide':
                result = await solveSlideCaptcha(captchaElement);
                break;
            case 'secsdk':
                result = await solveSecsdkCaptcha(captchaElement);
                break;
            case 'geetest':
                result = await solveGeetestCaptcha(captchaElement);
                break;
            case 'yidun':
                result = await solveYidunCaptcha(captchaElement);
                break;
            case 'puzzle':
                result = await solvePuzzleCaptcha(captchaElement);
                break;
            default:
                result = await enhancedSolveDragCaptcha(captchaElement); // 默认拖拽处理
        }
        
        // 处理失败时重试
        if (!result.success && data.retry !== false) {
            console.log('⚠️ 验证码处理失败，尝试重试...');
            await delay(1000 + Math.random() * 2000);
            return await solveCaptcha({ ...data, retry: false });
        }
        
        return result;
    }
    
    /**
     * 智能检测验证码类型 - 支持多种主流验证码
     */
    async function detectCaptchaType(captchaElement) {
        // 检测顺序按重要性排列
        
        // 1. secsdk验证码（字节跳动安全SDK）- TikTok主要使用
        if (captchaElement.querySelector(CONFIG.selectors.secsdkCaptcha)) {
            return 'secsdk';
        }
        
        // 2. 极验(GeeTest)验证码
        if (captchaElement.querySelector(CONFIG.selectors.geetestCaptcha)) {
            return 'geetest';
        }
        
        // 3. 易盾验证码
        if (captchaElement.querySelector(CONFIG.selectors.yidunCaptcha)) {
            return 'yidun';
        }
        
        // 4. 拼图验证码
        if (captchaElement.querySelector(CONFIG.selectors.puzzleCaptcha)) {
            return 'puzzle';
        }
        
        // 5. 滑动验证码
        if (captchaElement.querySelector(CONFIG.selectors.slideCaptcha)) {
            return 'slide';
        }
        
        // 6. 通用拖拽验证码
        if (captchaElement.querySelector(CONFIG.selectors.dragCaptcha)) {
            return 'drag';
        }
        
        // 7. 默认拖拽验证码
        return 'drag';
    }

    /**
     * 增强版拖拽验证码处理 - 采用贝塞尔曲线轨迹算法
     * 基于e建联插件的最佳实践
     */
    async function enhancedSolveDragCaptcha(element) {
        try {
            console.log('🎯 开始处理拖拽验证码...');
            
            // 查找滑块元素
            const slider = element.querySelector('.slider, .drag-slider, [class*="drag"], [class*="slider"]');
            if (!slider) {
                throw new Error('未找到拖拽滑块元素');
            }

            // 计算拖拽参数
            const params = calculateDragParams(slider, element);
            console.log('📐 拖拽参数计算完成:', params);
            
            // 生成贝塞尔曲线轨迹（模拟人工拖拽）
            const trajectory = generateBezierTrajectory(params);
            console.log(`🚀 生成轨迹点数: ${trajectory.length}`);
            
            // 执行智能拖拽
            await executeDragWithTrajectory(slider, trajectory);
            
            // 等待并验证结果
            const success = await verifyDragResult(element);
            
            return {
                success,
                method: 'enhanced_bezier_drag',
                reason: success ? '贝塞尔曲线拖拽成功' : '拖拽验证失败，可能需要重试'
            };

        } catch (error) {
            console.error('❌ 拖拽验证码处理异常:', error);
            return { success: false, reason: error.message };
        }
    }
    
    /**
     * 计算拖拽参数
     */
    function calculateDragParams(slider, container) {
        const sliderRect = slider.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        return {
            startX: sliderRect.left + sliderRect.width / 2,
            startY: sliderRect.top + sliderRect.height / 2,
            endX: containerRect.right - sliderRect.width / 2 - (10 + Math.random() * 10),
            endY: sliderRect.top + sliderRect.height / 2,
            distance: containerRect.width - sliderRect.width - 20
        };
    }
    
    /**
     * 生成贝塞尔曲线轨迹 - 核心算法来自e建联最佳实践
     */
    function generateBezierTrajectory(params) {
        const { startX, startY, endX, endY } = params;
        
        // 控制点：创建自然的弧形轨迹
        const controlX = startX + (endX - startX) * (0.4 + Math.random() * 0.2);
        const controlY = Math.min(startY, endY) - (20 + Math.random() * 30);
        
        // 轨迹点数：根据距离动态调整
        const stepCount = Math.floor(Math.abs(endX - startX) / 3) + 15 + Math.floor(Math.random() * 10);
        
        const trajectory = [];
        
        for (let i = 0; i <= stepCount; i++) {
            const t = i / stepCount;
            
            // 贝塞尔曲线公式（二次）
            const x = Math.round(
                Math.pow(1 - t, 2) * startX +
                2 * (1 - t) * t * controlX +
                Math.pow(t, 2) * endX
            );
            
            const y = Math.round(
                Math.pow(1 - t, 2) * startY +
                2 * (1 - t) * t * controlY +
                Math.pow(t, 2) * endY
            );
            
            // 添加自然抖动（模拟手部微颤）
            const jitterX = (Math.random() - 0.5) * 3;
            const jitterY = (Math.random() - 0.5) * 2;
            
            trajectory.push({
                x: x + jitterX,
                y: y + jitterY,
                timestamp: Date.now() + i * (8 + Math.random() * 12) // 随机时间间隔
            });
        }
        
        return trajectory;
    }
    
    /**
     * 执行贝塞尔轨迹拖拽
     */
    async function executeDragWithTrajectory(slider, trajectory) {
        if (trajectory.length === 0) return;
        
        // 开始拖拽
        const startPoint = trajectory[0];
        const mouseDownEvent = new MouseEvent('mousedown', {
            clientX: startPoint.x,
            clientY: startPoint.y,
            bubbles: true,
            cancelable: true,
            button: 0
        });
        
        slider.dispatchEvent(mouseDownEvent);
        console.log('🖱️ 开始拖拽...');
        
        // 执行轨迹移动
        for (let i = 1; i < trajectory.length; i++) {
            const point = trajectory[i];
            const prevPoint = trajectory[i - 1];
            
            // 计算动态延迟
            const baseDelay = point.timestamp - prevPoint.timestamp;
            const randomDelay = baseDelay + Math.random() * 5;
            
            await delay(Math.max(randomDelay, 8));
            
            const mouseMoveEvent = new MouseEvent('mousemove', {
                clientX: point.x,
                clientY: point.y,
                bubbles: true,
                cancelable: true,
                button: 0
            });
            
            // 分别触发滑块和文档事件
            slider.dispatchEvent(mouseMoveEvent);
            document.dispatchEvent(mouseMoveEvent);
        }
        
        // 结束拖拽
        await delay(50 + Math.random() * 100);
        const endPoint = trajectory[trajectory.length - 1];
        const mouseUpEvent = new MouseEvent('mouseup', {
            clientX: endPoint.x,
            clientY: endPoint.y,
            bubbles: true,
            cancelable: true,
            button: 0
        });
        
        slider.dispatchEvent(mouseUpEvent);
        document.dispatchEvent(mouseUpEvent);
        
        console.log('✅ 拖拽轨迹执行完成');
    }
    
    /**
     * 验证拖拽结果
     */
    async function verifyDragResult(captchaElement) {
        console.log('🔍 验证拖拽结果...');
        
        // 等待验证处理
        await delay(1500 + Math.random() * 1000);
        
        // 多重验证检查
        const checks = [
            // 检查验证码元素是否消失
            () => !document.contains(captchaElement),
            // 检查是否隐藏
            () => captchaElement.style.display === 'none',
            // 检查是否有成功类名
            () => captchaElement.classList.contains('success') || 
                  captchaElement.classList.contains('verified'),
            // 检查是否出现成功标识
            () => !!captchaElement.querySelector('.success, .verified, [class*="success"]'),
            // 检查父级验证码容器是否消失
            () => !document.querySelector(CONFIG.selectors.captcha)
        ];
        
        const isSuccess = checks.some(check => {
            try {
                return check();
            } catch (error) {
                return false;
            }
        });
        
        console.log(isSuccess ? '✅ 验证码处理成功' : '❌ 验证码处理失败');
        return isSuccess;
    }
    
    /**
     * 处理滑动验证码
     */
    async function solveSlideCaptcha(element) {
        console.log('🎯 处理滑动验证码...');
        // 滑动验证码的处理逻辑，可以复用拖拽算法
        return await enhancedSolveDragCaptcha(element);
    }
    
    /**
     * 处理secsdk验证码（字节跳动安全SDK）
     */
    async function solveSecsdkCaptcha(element) {
        console.log('🎯 处理secsdk验证码...');
        // secsdk验证码通常也是拖拽类型，使用增强算法
        return await enhancedSolveDragCaptcha(element);
    }
    
    /**
     * 处理极验(GeeTest)验证码
     */
    async function solveGeetestCaptcha(element) {
        console.log('🎯 处理极验(GeeTest)验证码...');
        
        try {
            // 检查是否有滑块按钮
            const sliderButton = element.querySelector('.geetest_slider_button');
            if (sliderButton) {
                // 使用贝塞尔曲线算法处理滑块
                const sliderRect = sliderButton.getBoundingClientRect();
                const trackElement = element.querySelector('.geetest_slider_track');
                const trackRect = trackElement ? trackElement.getBoundingClientRect() : element.getBoundingClientRect();
                
                const params = {
                    startX: sliderRect.left + sliderRect.width / 2,
                    startY: sliderRect.top + sliderRect.height / 2,
                    endX: trackRect.right - sliderRect.width / 2 - 10,
                    endY: sliderRect.top + sliderRect.height / 2,
                    distance: trackRect.width - sliderRect.width - 20
                };
                
                const trajectory = generateBezierTrajectory(params);
                await executeDragWithTrajectory(sliderButton, trajectory);
                
                return await verifyDragResult(element);
            }
            
            return { success: false, reason: '未找到GeeTest滑块按钮' };
            
        } catch (error) {
            console.error('处理GeeTest验证码失败:', error);
            return { success: false, reason: error.message };
        }
    }
    
    /**
     * 处理易盾验证码
     */
    async function solveYidunCaptcha(element) {
        console.log('🎯 处理易盾验证码...');
        
        try {
            // 检查是否有滑块
            const slider = element.querySelector('.yidun_slider');
            if (slider) {
                // 使用增强算法处理易盾滑块
                return await enhancedSolveDragCaptcha(element);
            }
            
            return { success: false, reason: '未找到易盾滑块' };
            
        } catch (error) {
            console.error('处理易盾验证码失败:', error);
            return { success: false, reason: error.message };
        }
    }
    
    /**
     * 处理拼图验证码
     */
    async function solvePuzzleCaptcha(element) {
        console.log('🎯 处理拼图验证码...');
        
        try {
            // 拼图验证码通常有缺口需要填补
            const puzzle = element.querySelector('.puzzle-captcha, .jigsaw, .geetest_canvas_img');
            if (puzzle) {
                // 检查是否有拖拽控制器
                const dragController = element.querySelector('.jigsaw-slider, .geetest_slider_button, [class*="drag"]');
                if (dragController) {
                    // 使用贝塞尔曲线算法处理拼图拖拽
                    return await enhancedSolveDragCaptcha(element);
                }
            }
            
            return { success: false, reason: '未找到拼图验证码控制器' };
            
        } catch (error) {
            console.error('处理拼图验证码失败:', error);
            return { success: false, reason: error.message };
        }
    }

    /**
     * 开始消息监控
     */
    function startMessageMonitoring(pageType) {
        if (messageObserver) return;

        console.log(`为页面类型 ${pageType} 启动消息监控`);

        // 根据页面类型选择不同的监控策略
        const monitorConfig = getMonitorConfigByPageType(pageType);

        // 创建DOM观察器
        messageObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 使用页面特定的选择器检测新消息
                            const isNewMessage = monitorConfig.messageSelectors.some(selector => {
                                return node.matches && node.matches(selector) || 
                                       node.querySelector && node.querySelector(selector);
                            });
                            
                            if (isNewMessage) {
                                handleNewMessageElement(node, pageType);
                            }
                        }
                    });
                }
            });
        });

        // 开始观察消息容器
        const messageContainer = document.querySelector(monitorConfig.containerSelector) || document.body;
        messageObserver.observe(messageContainer, {
            childList: true,
            subtree: true
        });
    }

    /**
     * 根据页面类型获取监控配置
     */
    function getMonitorConfigByPageType(pageType) {
        const configs = {
            'seller_messages': {
                containerSelector: '.chat-container, .message-container, #im_sdk_chat_input',
                messageSelectors: [
                    '.message-item',
                    '.chat-message', 
                    '.im-message',
                    '[data-e2e="message-item"]'
                ]
            },
            'affiliate_marketing': {
                containerSelector: '.conversation-list, .chat-list',
                messageSelectors: [
                    '.message-item',
                    '.notification-item',
                    '.response-item'
                ]
            },
            'shop_seller': {
                containerSelector: '.seller-messages, .communication-panel',
                messageSelectors: [
                    '.message-item',
                    '.seller-message'
                ]
            },
            'unknown': {
                containerSelector: 'body',
                messageSelectors: [
                    '.message-item',
                    '.chat-message',
                    '[data-e2e="message"]'
                ]
            }
        };

        return configs[pageType] || configs['unknown'];
    }

    /**
     * 处理新消息元素
     */
    function handleNewMessageElement(element, pageType) {
        // 根据页面类型使用不同的消息解析策略
        const messageInfo = parseMessageByPageType(element, pageType);
        
        if (!messageInfo) {
            return; // 无法解析或是自己发送的消息
        }

        console.log(`检测到新消息 [${pageType}]:`, messageInfo);
        
        // 通知后台脚本
        chrome.runtime.sendMessage({
            type: 'NEW_MESSAGE',
            pageType: pageType,
            message: {
                ...messageInfo,
                timestamp: Date.now(),
                url: window.location.href
            }
        }).catch(error => {
            console.log('发送消息到后台失败:', error);
        });
    }

    /**
     * 根据页面类型解析消息
     */
    function parseMessageByPageType(element, pageType) {
        const messageContent = element.textContent.trim();
        if (!messageContent) return null;

        // 检查是否是自己发送的消息（跳过）
        const ownMessageSelectors = [
            '.own-message', 
            '.sent-message',
            '.message-out',
            '[data-direction="outgoing"]'
        ];
        
        if (ownMessageSelectors.some(selector => 
            element.matches(selector) || element.querySelector(selector)
        )) {
            return null; // 跳过自己发送的消息
        }

        let senderInfo = { name: 'Unknown', id: null };
        
        // 根据页面类型使用不同的发送者信息提取策略
        switch (pageType) {
            case 'seller_messages':
                // 商家后台私信页面的发送者信息提取
                const sellerSender = element.querySelector('.sender-name, .username, [data-e2e="sender"]');
                if (sellerSender) {
                    senderInfo.name = sellerSender.textContent.trim();
                }
                break;
                
            case 'affiliate_marketing':
                // 联盟营销页面的发送者信息提取
                const affiliateSender = element.querySelector('.creator-name, .influencer-name, .partner-name');
                if (affiliateSender) {
                    senderInfo.name = affiliateSender.textContent.trim();
                }
                break;
                
            default:
                // 通用发送者信息提取
                const genericSender = element.querySelector('.sender-name, .username, [data-e2e="sender"]');
                if (genericSender) {
                    senderInfo.name = genericSender.textContent.trim();
                }
        }

        return {
            from: senderInfo.name,
            fromId: senderInfo.id,
            content: messageContent,
            pageType: pageType
        };
    }

    // 工具函数

    /**
     * 等待元素出现
     */
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver(() => {
                const element = document.querySelector(selector);
                if (element) {
                    observer.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`元素未找到: ${selector}`));
            }, timeout);
        });
    }

    /**
     * 模拟点击
     */
    async function simulateClick(element) {
        const rect = element.getBoundingClientRect();
        const x = rect.left + rect.width / 2;
        const y = rect.top + rect.height / 2;

        // 滚动到元素可见
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await delay(500);

        // 模拟鼠标事件
        ['mousedown', 'mouseup', 'click'].forEach(type => {
            const event = new MouseEvent(type, {
                clientX: x,
                clientY: y,
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(event);
        });

        await delay(CONFIG.delays.click);
    }

    /**
     * 模拟输入
     */
    async function simulateTyping(element, text) {
        element.focus();
        element.value = '';

        for (const char of text) {
            element.value += char;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await delay(CONFIG.delays.typing);
        }

        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    /**
     * 模拟按键
     */
    async function simulateKeyPress(element, key) {
        const event = new KeyboardEvent('keydown', {
            key: key,
            code: key,
            bubbles: true
        });
        element.dispatchEvent(event);
        await delay(CONFIG.delays.click);
    }

    /**
     * 延迟函数
     */
    function delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 导航到搜索页面 - 商家后台版本
     */
    async function navigateToSearch() {
        // 商家后台的搜索功能通常在创作者中心或联盟营销页面
        const baseUrl = window.location.origin;
        if (baseUrl.includes('affiliate')) {
            window.location.href = `${baseUrl}/creator/search`;
        } else {
            // seller域名下，搜索功能可能在creator或affiliate板块
            window.location.href = `${baseUrl}/creator/search`;
        }
        await delay(CONFIG.delays.navigation);
    }

    /**
     * 导航到用户主页 - 商家后台版本
     */
    async function navigateToUserProfile(username) {
        // 商家后台中用户主页路径可能不同，使用相对路径
        const baseUrl = window.location.origin;
        const profileUrl = `${baseUrl}/creator/@${username}`;
        if (window.location.href !== profileUrl) {
            window.location.href = profileUrl;
            await delay(CONFIG.delays.navigation);
        }
    }

    /**
     * 导航到消息页面 - 商家后台版本
     */
    async function navigateToMessages() {
        // 商家后台消息页面通常在 /seller/im 路径
        const baseUrl = window.location.origin;
        const messagesUrl = `${baseUrl}/seller/im`;
        if (!window.location.href.includes('/messages') && !window.location.href.includes('/im')) {
            window.location.href = messagesUrl;
            await delay(CONFIG.delays.navigation);
        }
    }

    /**
     * 检查验证码
     */
    async function checkForCaptcha() {
        await delay(1000);
        return document.querySelector(CONFIG.selectors.captcha) !== null;
    }

    /**
     * 解析粉丝数
     */
    function parseFollowersCount(text) {
        if (!text) return 0;
        
        const num = parseFloat(text);
        if (text.includes('K')) return num * 1000;
        if (text.includes('M')) return num * 1000000;
        return num;
    }

    /**
     * 提取时间戳
     */
    function extractTimestamp(element) {
        const timeElement = element.querySelector('.timestamp, [data-e2e="timestamp"]');
        if (timeElement) {
            return new Date(timeElement.textContent).getTime();
        }
        return Date.now();
    }

    /**
     * 检测并同步用户登录状态
     */
    async function checkAndSyncLoginStatus() {
        try {
            const userInfo = await extractUserInfo();
            
            if (userInfo.isLoggedIn) {
                // 发送登录状态到后台脚本
                chrome.runtime.sendMessage({
                    type: 'USER_LOGIN_STATUS',
                    userInfo: userInfo
                });
                
                console.log('用户登录状态已同步:', userInfo);
            } else {
                console.log('用户未登录');
            }
        } catch (error) {
            console.error('同步登录状态失败:', error);
        }
    }

    /**
     * 提取用户信息
     */
    async function extractUserInfo() {
        // 等待页面加载完成
        await new Promise(resolve => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });

        const userInfo = {
            isLoggedIn: false,
            username: null,
            email: null,
            avatarUrl: null,
            sellerId: null,
            shopName: null,
            region: null
        };

        // 方法1: 从页面DOM提取用户信息
        const userSelectors = [
            '[data-testid="user-menu"]',
            '.user-profile',
            '.seller-info',
            '.account-info',
            '[data-e2e="user-avatar"]',
            '.user-avatar'
        ];

        for (const selector of userSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                // 提取用户名
                const usernameEl = element.querySelector('.username, .user-name, .seller-name');
                if (usernameEl) {
                    userInfo.username = usernameEl.textContent.trim();
                    userInfo.isLoggedIn = true;
                }

                // 提取头像
                const avatarEl = element.querySelector('img[alt*="avatar"], .avatar img, .user-avatar img');
                if (avatarEl) {
                    userInfo.avatarUrl = avatarEl.src;
                }
                break;
            }
        }

        // 方法2: 从localStorage提取信息
        try {
            const storageKeys = [
                'user_info',
                'seller_info', 
                'account_info',
                'user_profile',
                'tt_user_info'
            ];

            for (const key of storageKeys) {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        if (parsed.username || parsed.name || parsed.seller_name) {
                            userInfo.isLoggedIn = true;
                            userInfo.username = parsed.username || parsed.name || parsed.seller_name;
                            userInfo.email = parsed.email;
                            userInfo.sellerId = parsed.seller_id || parsed.id;
                            userInfo.shopName = parsed.shop_name;
                            userInfo.avatarUrl = parsed.avatar || parsed.avatar_url;
                            break;
                        }
                    } catch (e) {
                        console.log(`解析 ${key} 失败:`, e);
                    }
                }
            }
        } catch (error) {
            console.log('读取localStorage失败:', error);
        }

        // 方法3: 从URL路径提取区域信息
        const hostname = window.location.hostname;
        if (hostname.includes('seller-us')) userInfo.region = 'US';
        else if (hostname.includes('seller-uk')) userInfo.region = 'UK';
        else if (hostname.includes('seller-sg')) userInfo.region = 'SG';
        else if (hostname.includes('seller-my')) userInfo.region = 'MY';
        else if (hostname.includes('seller-th')) userInfo.region = 'TH';
        else if (hostname.includes('seller-vn')) userInfo.region = 'VN';
        else if (hostname.includes('seller-ph')) userInfo.region = 'PH';
        else if (hostname.includes('seller-id')) userInfo.region = 'ID';

        // 方法4: 通过页面脚本注入获取更深层数据
        try {
            const pageUserInfo = await getPageUserInfo();
            if (pageUserInfo) {
                Object.assign(userInfo, pageUserInfo);
            }
        } catch (error) {
            console.log('从页面脚本获取用户信息失败:', error);
        }

        return userInfo;
    }

    /**
     * 通过页面脚本获取用户信息
     */
    function getPageUserInfo() {
        return new Promise((resolve) => {
            // 创建一个脚本元素注入到页面
            const script = document.createElement('script');
            script.textContent = `
                (function() {
                    const userInfo = {};
                    
                    // 尝试从全局变量获取用户信息
                    const globalVars = ['__INITIAL_STATE__', 'window.USER_INFO', 'window.SELLER_INFO'];
                    
                    for (const varName of globalVars) {
                        try {
                            const data = eval(varName);
                            if (data && (data.user || data.seller || data.account)) {
                                const user = data.user || data.seller || data.account;
                                userInfo.isLoggedIn = true;
                                userInfo.username = user.username || user.name || user.nickname;
                                userInfo.email = user.email;
                                userInfo.sellerId = user.seller_id || user.id;
                                userInfo.shopName = user.shop_name;
                                userInfo.avatarUrl = user.avatar || user.avatar_url;
                                break;
                            }
                        } catch (e) {
                            // 忽略错误继续尝试
                        }
                    }
                    
                    // 发送消息给content script
                    window.postMessage({
                        type: 'PAGE_USER_INFO',
                        userInfo: userInfo
                    }, '*');
                })();
            `;
            
            // 监听页面消息
            const messageHandler = (event) => {
                if (event.data && event.data.type === 'PAGE_USER_INFO') {
                    window.removeEventListener('message', messageHandler);
                    document.head.removeChild(script);
                    resolve(event.data.userInfo);
                }
            };
            
            window.addEventListener('message', messageHandler);
            
            document.head.appendChild(script);
            
            // 5秒超时
            setTimeout(() => {
                window.removeEventListener('message', messageHandler);
                if (script.parentNode) {
                    document.head.removeChild(script);
                }
                resolve(null);
            }, 5000);
        });
    }

    // 消息监听
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        switch (message.type) {
            case 'EXECUTE_TASK':
                executeTask(message.task).then(result => {
                    sendResponse(result);
                });
                return true; // 异步响应

            case 'CHECK_MESSAGES':
                // 在消息页面检查新消息
                if (window.location.pathname.includes('/messages')) {
                    checkReply({ username: 'all' }).then(result => {
                        sendResponse(result);
                    });
                    return true;
                }
                sendResponse({ success: false, reason: '不在消息页面' });
                break;

            case 'GET_LOGIN_STATUS':
                checkAndSyncLoginStatus();
                sendResponse({ success: true });
                break;

            default:
                console.log('未知消息类型:', message.type);
        }
    });

    // 初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initContentScript);
    } else {
        initContentScript();
    }

    console.log('TikTok建联插件内容脚本初始化完成');
}