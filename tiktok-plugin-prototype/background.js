/**
 * TikTok建联插件 - 后台脚本
 * 负责任务管理、与后端通信、消息监控等核心功能
 */

// 配置
const CONFIG = {
    // 模拟后端API地址
    BACKEND_BASE_URL: 'http://localhost:8080',
    TASK_DISPATCH_URL: 'http://localhost:8080/task-dispatch.html',
    MESSAGE_COLLECT_URL: 'http://localhost:8080/message-collect.html',
    
    // 轮询间隔
    TASK_POLL_INTERVAL: 5000,      // 5秒检查一次新任务
    MESSAGE_POLL_INTERVAL: 3000,   // 3秒检查一次新消息
    
    // 任务配置
    MAX_CONCURRENT_TASKS: 3,
    TASK_RETRY_COUNT: 3,
    
    // 持久化配置
    ALARMS: {
        TASK_POLLING: 'taskPolling',
        MESSAGE_MONITORING: 'messageMonitoring',
        HEARTBEAT_CHECK: 'heartbeatCheck',
        KEEP_ALIVE: 'keepAlive'
    },
    
    // 保活配置
    KEEP_ALIVE_INTERVAL: 25000,    // 25秒保活一次
    HEARTBEAT_TIMEOUT: 60000,      // 60秒心跳超时
    
    // 自动创建标签页配置
    AUTO_OPEN_PAGES: [
        'https://seller-us.tiktok.com/seller/im',
        'https://seller-uk.tiktok.com/seller/im'
    ]
};

// 全局状态
let isPluginActive = false;
let currentTasks = new Map();
let taskQueue = [];
let activeSellerIMTabs = new Map(); // 跟踪活跃的seller/im标签页
let lastHeartbeats = new Map(); // 记录最后心跳时间

/**
 * 插件安装/启动时初始化
 */
chrome.runtime.onInstalled.addListener(async () => {
    console.log('TikTok商家建联插件已安装');
    
    // 初始化存储
    await chrome.storage.local.set({
        isActive: false,
        tasks: [],
        statistics: {
            totalTasks: 0,
            completedTasks: 0,
            failedTasks: 0,
            messagesCollected: 0
        },
        settings: {
            autoStart: false,
            notifications: true,
            taskDelay: 2000
        }
    });
    
    // 创建右键菜单（Service Worker中需要先清除再创建）
    try {
        await chrome.contextMenus.removeAll();
        chrome.contextMenus.create({
            id: 'tiktok-helper-menu',
            title: 'TikTok商家建联助手',
            contexts: ['page'],
            documentUrlPatterns: [
                'https://seller-us.tiktok.com/*',
                'https://seller-uk.tiktok.com/*', 
                'https://seller-sg.tiktok.com/*',
                'https://seller-my.tiktok.com/*',
                'https://*.tiktokglobalshop.com/*',
                'https://affiliate.tiktok.com/*',
                'https://*.tiktokshop.com/*'
            ]
        });
    } catch (error) {
        console.log('创建右键菜单失败:', error);
    }
});

/**
 * Chrome Alarms监听器 - 实现持久化任务调度
 */
chrome.alarms.onAlarm.addListener(async (alarm) => {
    console.log(`⏰ Alarm触发: ${alarm.name}`);
    
    switch (alarm.name) {
        case CONFIG.ALARMS.TASK_POLLING:
            if (isPluginActive) {
                await pollForNewTasks();
            }
            break;
            
        case CONFIG.ALARMS.MESSAGE_MONITORING:
            if (isPluginActive) {
                await monitorMessages();
            }
            break;
            
        case CONFIG.ALARMS.HEARTBEAT_CHECK:
            await checkHeartbeats();
            break;
            
        case CONFIG.ALARMS.KEEP_ALIVE:
            await keepServiceWorkerAlive();
            break;
    }
});

/**
 * 插件激活/停用控制 - 使用Chrome Alarms
 */
async function setPluginActive(active) {
    isPluginActive = active;
    await chrome.storage.local.set({ isActive: active });
    
    if (active) {
        await startPersistentTasks();
        await ensureSellerIMTabs();
        console.log('🚀 插件已启动 - 使用持久化任务调度');
    } else {
        await stopPersistentTasks();
        console.log('⏹️ 插件已停止');
    }
    
    // 通知popup更新状态
    chrome.runtime.sendMessage({
        type: 'STATUS_CHANGED',
        isActive: active
    }).catch(() => {
        // popup可能未打开，忽略错误
    });
}

/**
 * 启动持久化任务
 */
async function startPersistentTasks() {
    // 清除所有现有alarms
    await chrome.alarms.clearAll();
    
    // 创建持久化任务alarms
    chrome.alarms.create(CONFIG.ALARMS.TASK_POLLING, {
        delayInMinutes: 0.1, // 立即开始
        periodInMinutes: CONFIG.TASK_POLL_INTERVAL / 60000 // 转换为分钟
    });
    
    chrome.alarms.create(CONFIG.ALARMS.MESSAGE_MONITORING, {
        delayInMinutes: 0.05,
        periodInMinutes: CONFIG.MESSAGE_POLL_INTERVAL / 60000
    });
    
    chrome.alarms.create(CONFIG.ALARMS.HEARTBEAT_CHECK, {
        delayInMinutes: 1,
        periodInMinutes: 1 // 每分钟检查一次心跳
    });
    
    chrome.alarms.create(CONFIG.ALARMS.KEEP_ALIVE, {
        delayInMinutes: 0.5,
        periodInMinutes: CONFIG.KEEP_ALIVE_INTERVAL / 60000
    });
    
    console.log('✅ 所有持久化任务已启动');
}

/**
 * 停止持久化任务
 */
async function stopPersistentTasks() {
    await chrome.alarms.clearAll();
    console.log('🛑 所有持久化任务已停止');
}

/**
 * 保持Service Worker活跃
 */
async function keepServiceWorkerAlive() {
    console.log('💗 Service Worker保活心跳');
    
    // 检查插件状态
    const { isActive } = await chrome.storage.local.get(['isActive']);
    if (!isActive) {
        await stopPersistentTasks();
        return;
    }
    
    // 检查并重新创建seller/im标签页
    await ensureSellerIMTabs();
}

/**
 * 确保seller/im标签页存在
 */
async function ensureSellerIMTabs() {
    try {
        // 查找现有的seller/im标签页
        const tabs = await chrome.tabs.query({
            url: [
                'https://seller-us.tiktok.com/seller/im*',
                'https://seller-uk.tiktok.com/seller/im*',
                'https://seller-sg.tiktok.com/seller/im*',
                'https://*.tiktokglobalshop.com/seller/im*'
            ]
        });
        
        console.log(`📊 找到 ${tabs.length} 个seller/im标签页`);
        
        // 如果没有seller/im标签页且插件激活，创建一个
        if (tabs.length === 0 && isPluginActive) {
            const sellerImUrl = CONFIG.AUTO_OPEN_PAGES[0]; // 默认使用美国站
            console.log(`🔥 创建新的seller/im标签页: ${sellerImUrl}`);
            
            const newTab = await chrome.tabs.create({
                url: sellerImUrl,
                active: false // 后台创建，不激活
            });
            
            // 记录新标签页
            activeSellerIMTabs.set(newTab.id, {
                url: sellerImUrl,
                created: Date.now(),
                lastHeartbeat: Date.now()
            });
        } else {
            // 更新现有标签页状态
            tabs.forEach(tab => {
                activeSellerIMTabs.set(tab.id, {
                    url: tab.url,
                    updated: Date.now(),
                    lastHeartbeat: lastHeartbeats.get(tab.id) || Date.now()
                });
            });
        }
        
    } catch (error) {
        console.error('确保seller/im标签页失败:', error);
    }
}

/**
 * 检查心跳状态
 */
async function checkHeartbeats() {
    const now = Date.now();
    const expiredTabs = [];
    
    for (const [tabId, heartbeatTime] of lastHeartbeats.entries()) {
        if (now - heartbeatTime > CONFIG.HEARTBEAT_TIMEOUT) {
            expiredTabs.push(tabId);
        }
    }
    
    // 清理过期的标签页记录
    expiredTabs.forEach(tabId => {
        lastHeartbeats.delete(tabId);
        activeSellerIMTabs.delete(tabId);
        console.log(`🗑️ 清理过期标签页: ${tabId}`);
    });
    
    if (expiredTabs.length > 0) {
        console.log(`💔 ${expiredTabs.length} 个标签页心跳超时`);
    }
}

// 旧的setInterval方法已替换为Chrome Alarms

/**
 * 轮询新任务
 */
async function pollForNewTasks() {
    try {
        // 模拟向后端请求新任务
        const response = await fetch(`${CONFIG.BACKEND_BASE_URL}/api/tasks/pending`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer mock-token'
            }
        }).catch(() => {
            // 如果后端未启动，使用模拟数据
            return {
                ok: true,
                json: () => Promise.resolve({
                    success: true,
                    tasks: generateMockTasks()
                })
            };
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success && data.tasks && data.tasks.length > 0) {
                await addTasksToQueue(data.tasks);
                console.log(`获取到 ${data.tasks.length} 个新任务`);
            }
        }
        
        // 执行队列中的任务
        await processTaskQueue();
        
    } catch (error) {
        console.error('轮询任务失败:', error);
    }
}

/**
 * 生成模拟任务（用于演示）
 */
function generateMockTasks() {
    const mockTasks = [
        {
            id: `task_${Date.now()}_1`,
            type: 'search_influencer',
            priority: 'high',
            data: {
                username: 'beauty_queen_2024',
                category: 'beauty',
                minFollowers: 50000,
                maxFollowers: 200000
            },
            createdAt: Date.now()
        },
        {
            id: `task_${Date.now()}_2`,
            type: 'send_message',
            priority: 'medium',
            data: {
                username: 'fashion_lover_style',
                message: '您好！我们对您的时尚内容很感兴趣，希望能与您合作推广我们的新品。期待您的回复！',
                template: 'collaboration_invite'
            },
            createdAt: Date.now()
        }
    ];
    
    // 随机返回0-2个任务
    return Math.random() > 0.7 ? mockTasks.slice(0, Math.floor(Math.random() * 2) + 1) : [];
}

/**
 * 添加任务到队列
 */
async function addTasksToQueue(tasks) {
    taskQueue.push(...tasks);
    
    // 保存到存储
    const { tasks: storedTasks = [] } = await chrome.storage.local.get(['tasks']);
    storedTasks.push(...tasks);
    await chrome.storage.local.set({ tasks: storedTasks });
    
    // 更新统计
    await updateStatistics({ totalTasks: tasks.length });
}

/**
 * 处理任务队列
 */
async function processTaskQueue() {
    while (taskQueue.length > 0 && currentTasks.size < CONFIG.MAX_CONCURRENT_TASKS && isPluginActive) {
        const task = taskQueue.shift();
        await executeTask(task);
    }
}

/**
 * 执行单个任务
 */
async function executeTask(task) {
    try {
        console.log(`开始执行任务: ${task.type} - ${task.id}`);
        currentTasks.set(task.id, { ...task, status: 'running', startTime: Date.now() });
        
        // 发送任务到content script - 查找商家后台标签页
        const tabs = await chrome.tabs.query({ 
            url: [
                'https://seller-us.tiktok.com/*',
                'https://seller-uk.tiktok.com/*',
                'https://seller-sg.tiktok.com/*',
                'https://*.tiktokglobalshop.com/*',
                'https://affiliate.tiktok.com/*',
                'https://*.tiktokshop.com/*'
            ] 
        });
        
        if (tabs.length === 0) {
            // 如果没有商家后台标签页，打开一个
            const tab = await chrome.tabs.create({ url: 'https://seller-us.tiktok.com' });
            await waitForTabLoaded(tab.id);
        }
        
        // 选择一个商家后台标签页执行任务
        const targetTab = tabs[0] || await chrome.tabs.query({ 
            url: ['https://seller-us.tiktok.com/*', 'https://*.tiktokglobalshop.com/*'] 
        })[0];
        
        if (targetTab) {
            // 确保content script已注入
            await chrome.scripting.executeScript({
                target: { tabId: targetTab.id },
                files: ['content.js']
            }).catch(() => {
                // 可能已经注入了，忽略错误
            });
            
            // 发送任务到content script
            await chrome.tabs.sendMessage(targetTab.id, {
                type: 'EXECUTE_TASK',
                task: task
            });
        } else {
            throw new Error('无法找到TikTok标签页');
        }
        
    } catch (error) {
        console.error(`任务执行失败: ${task.id}`, error);
        await handleTaskResult(task.id, { success: false, error: error.message });
    }
}

/**
 * 处理任务结果
 */
async function handleTaskResult(taskId, result) {
    const task = currentTasks.get(taskId);
    if (!task) return;
    
    task.endTime = Date.now();
    task.duration = task.endTime - task.startTime;
    task.result = result;
    
    if (result.success) {
        task.status = 'completed';
        await updateStatistics({ completedTasks: 1 });
        console.log(`任务完成: ${taskId}`);
        
        // 发送通知
        if (await getNotificationSetting()) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'TikTok建联助手',
                message: `任务完成: ${task.type}`
            });
        }
    } else {
        task.status = 'failed';
        task.retryCount = (task.retryCount || 0) + 1;
        
        // 重试逻辑
        if (task.retryCount < CONFIG.TASK_RETRY_COUNT) {
            task.status = 'pending';
            taskQueue.unshift(task); // 重新加入队列开头
            console.log(`任务将重试: ${taskId} (${task.retryCount}/${CONFIG.TASK_RETRY_COUNT})`);
        } else {
            await updateStatistics({ failedTasks: 1 });
            console.log(`任务最终失败: ${taskId}`);
        }
    }
    
    currentTasks.delete(taskId);
    
    // 保存任务记录
    await saveTaskRecord(task);
}

// 旧的消息监控方法已替换为Chrome Alarms和seller-im.js

/**
 * 监控新消息
 */
async function monitorMessages() {
    try {
        // 查找商家后台消息页面标签
        const tabs = await chrome.tabs.query({ 
            url: [
                'https://seller-us.tiktok.com/*/messages*',
                'https://seller-uk.tiktok.com/*/messages*',
                'https://seller-sg.tiktok.com/*/messages*',
                'https://*.tiktokglobalshop.com/*/messages*',
                'https://affiliate.tiktok.com/*/messages*'
            ] 
        });
        
        for (const tab of tabs) {
            // 向content script请求检查新消息
            try {
                await chrome.tabs.sendMessage(tab.id, {
                    type: 'CHECK_MESSAGES'
                });
            } catch (error) {
                // 标签页可能已关闭或content script未加载
                console.log(`无法向标签页 ${tab.id} 发送消息:`, error.message);
            }
        }
    } catch (error) {
        console.error('消息监控失败:', error);
    }
}

/**
 * 处理新消息
 */
async function handleNewMessage(message) {
    try {
        console.log('发现新消息:', message);
        
        // 上传到后端
        await uploadMessageToBackend(message);
        
        // 更新统计
        await updateStatistics({ messagesCollected: 1 });
        
        // 发送通知
        if (await getNotificationSetting()) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'TikTok建联助手',
                message: `收到来自 ${message.from} 的新消息`
            });
        }
        
    } catch (error) {
        console.error('处理新消息失败:', error);
    }
}

/**
 * 上传消息到后端
 */
async function uploadMessageToBackend(message) {
    try {
        const response = await fetch(`${CONFIG.BACKEND_BASE_URL}/api/messages`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer mock-token'
            },
            body: JSON.stringify({
                ...message,
                timestamp: Date.now(),
                pluginVersion: '1.0.0'
            })
        });
        
        if (!response.ok) {
            throw new Error(`上传失败: ${response.status}`);
        }
        
        console.log('消息上传成功');
        
    } catch (error) {
        console.error('消息上传失败:', error);
        // 保存到本地，稍后重试
        await saveMessageLocally(message);
    }
}

/**
 * 本地保存消息
 */
async function saveMessageLocally(message) {
    const { pendingMessages = [] } = await chrome.storage.local.get(['pendingMessages']);
    pendingMessages.push({ ...message, timestamp: Date.now() });
    await chrome.storage.local.set({ pendingMessages });
}

/**
 * 更新统计数据
 */
async function updateStatistics(updates) {
    const { statistics = {} } = await chrome.storage.local.get(['statistics']);
    
    Object.keys(updates).forEach(key => {
        statistics[key] = (statistics[key] || 0) + updates[key];
    });
    
    await chrome.storage.local.set({ statistics });
}

/**
 * 获取通知设置
 */
async function getNotificationSetting() {
    const { settings = {} } = await chrome.storage.local.get(['settings']);
    return settings.notifications !== false;
}

/**
 * 保存任务记录
 */
async function saveTaskRecord(task) {
    const { taskHistory = [] } = await chrome.storage.local.get(['taskHistory']);
    taskHistory.push(task);
    
    // 只保留最近100条记录
    if (taskHistory.length > 100) {
        taskHistory.splice(0, taskHistory.length - 100);
    }
    
    await chrome.storage.local.set({ taskHistory });
}

/**
 * 等待标签页加载完成
 */
function waitForTabLoaded(tabId) {
    return new Promise((resolve) => {
        const listener = (updatedTabId, changeInfo) => {
            if (updatedTabId === tabId && changeInfo.status === 'complete') {
                chrome.tabs.onUpdated.removeListener(listener);
                resolve();
            }
        };
        chrome.tabs.onUpdated.addListener(listener);
    });
}

/**
 * 消息处理
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    switch (message.type) {
        case 'GET_STATUS':
            sendResponse({ isActive: isPluginActive });
            break;
            
        case 'SET_ACTIVE':
            setPluginActive(message.active);
            sendResponse({ success: true });
            break;
            
        case 'TASK_RESULT':
            handleTaskResult(message.taskId, message.result);
            sendResponse({ success: true });
            break;
            
        case 'NEW_MESSAGE':
            handleNewMessage(message.message);
            sendResponse({ success: true });
            break;
            
        case 'GET_STATISTICS':
            chrome.storage.local.get(['statistics']).then(({ statistics = {} }) => {
                sendResponse(statistics);
            });
            return true; // 异步响应
            
        case 'SELLER_IM_READY':
            // seller/im页面已准备就绪
            console.log('🎯 seller/im页面已连接:', message.url);
            if (sender.tab) {
                lastHeartbeats.set(sender.tab.id, Date.now());
                activeSellerIMTabs.set(sender.tab.id, {
                    url: message.url,
                    ready: true,
                    readyTime: Date.now()
                });
            }
            sendResponse({ success: true });
            break;
            
        case 'SELLER_IM_HEARTBEAT':
            // 更新seller/im页面心跳
            if (sender.tab) {
                lastHeartbeats.set(sender.tab.id, Date.now());
                console.log(`💗 收到seller/im心跳 [标签页${sender.tab.id}]`);
            }
            sendResponse({ success: true });
            break;
            
        case 'NEW_SELLER_IM_MESSAGE':
            // 处理seller/im新消息
            handleSellerIMMessage(message.message, sender.tab);
            sendResponse({ success: true });
            break;
            
        case 'GET_ACTIVE_SELLER_IM_TABS':
            // 获取活跃的seller/im标签页
            sendResponse({
                tabs: Array.from(activeSellerIMTabs.entries()).map(([tabId, info]) => ({
                    tabId,
                    ...info,
                    lastHeartbeat: lastHeartbeats.get(tabId)
                }))
            });
            break;
            
        case 'CREATE_SELLER_IM_TAB':
            // 手动创建seller/im标签页
            createSellerIMTab(message.url).then(result => {
                sendResponse(result);
            });
            return true;
            
        default:
            console.log('未知消息类型:', message.type);
    }
});

/**
 * 右键菜单点击处理
 */
chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (info.menuItemId === 'tiktok-helper-menu') {
        // 在Service Worker中无法直接打开popup，改为发送通知或执行其他操作
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'icons/icon-48.png',
            title: 'TikTok建联助手',
            message: '请点击扩展图标打开控制面板'
        });
    }
});

/**
 * 标签页更新监听
 */
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 当用户导航到商家后台页面时，自动开始监控
    if (changeInfo.status === 'complete' && tab.url && 
        ((tab.url.includes('seller-') && tab.url.includes('tiktok.com')) ||
         tab.url.includes('tiktokglobalshop.com') ||
         tab.url.includes('affiliate.tiktok.com'))) {
        if (isPluginActive) {
            // 注入content script
            chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            }).catch(() => {
                // 可能已经注入了
            });
        }
    }
});

/**
 * 处理seller/im新消息
 */
async function handleSellerIMMessage(message, tab) {
    try {
        console.log('📨 收到seller/im新消息:', message);
        
        // 增强消息信息
        const enhancedMessage = {
            ...message,
            tabId: tab?.id,
            tabUrl: tab?.url,
            receivedAt: Date.now(),
            source: 'seller_im_dedicated'
        };
        
        // 上传到后端
        await uploadMessageToBackend(enhancedMessage);
        
        // 更新统计
        await updateStatistics({ messagesCollected: 1 });
        
        // 发送通知
        if (await getNotificationSetting()) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon-48.png',
                title: 'TikTok seller/im新消息',
                message: `来自 ${message.from}: ${message.content.substring(0, 50)}...`
            });
        }
        
        console.log('✅ seller/im消息处理完成');
        
    } catch (error) {
        console.error('处理seller/im消息失败:', error);
    }
}

/**
 * 创建seller/im标签页
 */
async function createSellerIMTab(url = null) {
    try {
        const targetUrl = url || CONFIG.AUTO_OPEN_PAGES[0];
        
        console.log(`🔥 手动创建seller/im标签页: ${targetUrl}`);
        
        const newTab = await chrome.tabs.create({
            url: targetUrl,
            active: true // 手动创建时激活标签页
        });
        
        // 记录新标签页
        activeSellerIMTabs.set(newTab.id, {
            url: targetUrl,
            created: Date.now(),
            manual: true,
            lastHeartbeat: Date.now()
        });
        
        lastHeartbeats.set(newTab.id, Date.now());
        
        return {
            success: true,
            tabId: newTab.id,
            url: targetUrl,
            message: 'seller/im标签页创建成功'
        };
        
    } catch (error) {
        console.error('创建seller/im标签页失败:', error);
        return {
            success: false,
            error: error.message
        };
    }
}

/**
 * 标签页关闭监听
 */
chrome.tabs.onRemoved.addListener((tabId, removeInfo) => {
    // 清理关闭的标签页记录
    if (activeSellerIMTabs.has(tabId)) {
        console.log(`🗑️ 清理已关闭的seller/im标签页: ${tabId}`);
        activeSellerIMTabs.delete(tabId);
        lastHeartbeats.delete(tabId);
    }
});

console.log('🚀 TikTok商家建联插件后台脚本已加载 - 支持持久化运行');