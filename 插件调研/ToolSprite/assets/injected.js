window.postMessage(
  {
    type: 'seller-account-info',
    accountInfoParam: window._accountInfoParam
  },
  '*'
);
window.addEventListener('message', function (event) {
  if (event.data.type !== 'TK_XHR_REQUEST') {
    return;
  }
  const { url, method, headers, body, requestId } = event.data;
  const xhr = new XMLHttpRequest();
  try {
    xhr.open(method || 'GET', url, true);
    // 设置默认头
    xhr.setRequestHeader('Accept', 'application/json');
    // 设置自定义头
    if (headers) {
      Object.keys(headers).forEach((key) =>
        xhr.setRequestHeader(key, headers[key])
      );
    }
    // 处理 POST 请求的 Content-Type
    if (body && method !== 'GET') {
      xhr.setRequestHeader('Content-Type', 'application/json');
    }

    xhr.onreadystatechange = function () {
      if (xhr.readyState === 4) {
        const responseData =
          xhr.status >= 200 && xhr.status < 300 ? xhr.responseText : null;
        window.postMessage(
          {
            type: 'TK_XHR_RESPONSE',
            url,
            status: xhr.status,
            response: responseData,
            requestId,
            error: xhr.status >= 400 ? xhr.statusText : null
          },
          '*'
        );
      }
    };

    xhr.send(method === 'GET' ? null : body);
  } catch (e) {
    window.postMessage(
      {
        type: 'TK_XHR_RESPONSE',
        url,
        status: 0,
        response: null,
        error: e.message
      },
      window.location.origin
    );
  }
});
