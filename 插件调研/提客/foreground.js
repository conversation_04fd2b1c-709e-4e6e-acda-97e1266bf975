// This script gets injected into any opened page
// whose URL matches the pattern defined in the manifest
// (see "content_script" key).
// Several foreground scripts can be declared
// and injected into the same or different pages.

// window.onload = function () {
//     let shop = new f();
//     shop.init()
// }
let shop = new f();
shop.init()


function f() {
    this.doms = [];
    this.names = [];
    this.deleteNames = [];
    this.timeInterval = 1000;
    this.creators_filter = "";
    this.followers_filter = "";
    this.performance_filter = "";
    this.products = "1729438216673530801";
    this.activityName = "cell phone holder on sale";
    this.dateTime = "07/30/2024";
    this.email = "<EMAIL>";
    this.message = "hello";
    this.kolIndex = 0;
    this.startIng = false;
    this.is_premium = 0;
    this.token = "";
    this.product_info_list = [];
    this.shop_id = "";
    this.shop_name = "";
    this.inviteNums = 50;//需修改至50
    this.token = '';
    this.sample = '';
    this.init = function () {
        console.log("插件插入前")
        try {
            document.querySelector('.tikeDDfdsafxarw_xxza').parentNode.removeChild(document.querySelector('.tikeDDfdsafxarw_xxza'))
        } catch (e) {

        }
        try {
            let dom = document.querySelectorAll(".arco-table-body .text-body-m-medium");
            for (let item of dom) {
                let checkbox = document.createElement('input');

// 设置复选框的属性
                checkbox.type = 'checkbox'; // 设置类型为复选框
                checkbox.name = 'myCheckbox'; // 设置复选框的名称
                checkbox.value = 'checkedValue'; // 设置复选框的值
                checkbox.id = 'myCheckboxId'; // 设置复选框的 ID（可选）
                checkbox.style.position = 'absolute';
                checkbox.style.top = '0px';
                checkbox.style.left = '0px';
                checkbox.style.width = '20px';
                checkbox.style.height = '20px';
                item.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.appendChild(checkbox)
                item.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.parentNode.style.position = 'relative'
                checkbox.addEventListener('click', function (event) {
                    // 阻止事件冒泡到外部div
                    console.log('----此处点击了')
                    event.stopPropagation();
                    // 可以在这里添加复选框的点击逻辑
                });
            }
        } catch (e) {

        }
        this.receiveMsg()
    }
    this.receiveMsg = function () {
        this.input_Main_token()
    }
    this.getUserInfo = function (token) {
        this.token = token
        fetch('https://server.aivocado.online/tt-tool-server/user/get-profile', {
            method: 'GET', // 或者 'POST'、'PUT'、'DELETE' 等其他 HTTP 方法
            headers: {
                'my_token': token// 设置请求头的 'Authorization' 字段
            },
        })
            .then(response => response.json()) // 将响应转换为 JSON 对象
            .then(data => {
                if (data.code == 0) {
                    if (!data.data.premium_list) {
                        this.is_premium = 1
                    } else {
                        this.is_premium = 1
                        for (let item of data.data.premium_list) {
                            if (item.premium_type == 0 || item.premium_type == 1) {
                                this.is_premium = 2
                            }
                        }
                    }
                    if (this.is_premium == 2) {
                        this.addMainPage()
                    } else {
                        this.addMembership()
                    }
                }
            }) // 处理响应数据
            .catch(error => console.error('Error:', error)); // 处理请求错误
    }
    this.getUserData = function (callback) {
        fetch('https://server.aivocado.online/tt-tool-server/shop/list-invitation-template', {
            method: 'GET', // 或者 'POST'、'PUT'、'DELETE' 等其他 HTTP 方法
            headers: {
                'my_token': this.token// 设置请求头的 'Authorization' 字段
            },
        })
            .then(response => response.json()) // 将响应转换为 JSON 对象
            .then(data => {
                if (data.code == 0) {
                    callback(data.data.template_list)
                }
            }) // 处理响应数据
            .catch(error => console.error('Error:', error)); // 处理请求错误
    }
    this.input_Main_token = function () {
        let token = document.querySelectorAll(".usrxxxfadfsa_main_token")
        if (token.length > 0) {
            token[0].addEventListener("input", () => {
                // 在控制台输出输入框的新值
                console.log("输入框的新值为：" + token[0].value);
                this.sendBackgroundMsg(1, token[0].value)
            });
        } else {
            this.sendBackgroundMsg(0)
        }
    }
    this.sendBackgroundMsg = function (type, message) {
        if (type == 0) {
            chrome.storage.sync.get('my_token', (res) => {
                if (this.isEmptyObject(res)) {
                    this.addLoginPage()
                } else {
                    if (res.my_token) {
                        this.token = res.my_token
                        this.getUserInfo(res.my_token)
                    } else {
                        this.addLoginPage()
                    }
                }
            });
        } else {
            try {
                chrome.storage.sync.set({my_token: message});
            } catch (e) {

            }

            //chrome.storage.sync.remove('my_token');
        }

    }
    this.isEmptyObject = function (obj) {
        return !Object.keys(obj).length;
    }
    this.clickFilter = async function () {
        let arr1 = this.creators_filter.split('&category;')
        let arr2 = this.followers_filter.split('&category;')
        let arr3 = this.performance_filter.split('&category;')
        console.log(arr1, arr2, arr3)
        /**
         * creators_filter是否有筛选条件
         *
         *
         */
        if (arr1.join('')) {
            document.querySelectorAll('.arco-space-item button')[0].click();
            /**
             * 判断product category是否存在
             *
             */
            if (arr1[0]) {
                document.querySelector('#categories button').click();
                let _arr1 = arr1[0].split(';')
                for (let item of _arr1) {
                    if (item) {
                        let __arr1 = item.split('/')
                        let doms = document.querySelectorAll('.arco-cascader-list-wrapper ul')[0].querySelectorAll('li');
                        for (let item of doms) {
                            if (item.innerText == __arr1[0] && __arr1.length == 1) {
                                console.log("点击了一级菜单")
                                item.querySelector('.arco-checkbox').click()
                                await this.sleep(500)
                            } else if (item.innerText == __arr1[0] && __arr1.length != 1) {

                                /**
                                 * 判断是否一级菜单点击
                                 *
                                 */
                                if (!item.querySelector('.arco-checkbox').classList.contains('arco-checkbox-indeterminate') && !item.querySelector('.arco-checkbox').classList.contains('arco-checkbox-checked')) {
                                    console.log("准备点击二级菜单")
                                    item.querySelector('.arco-cascader-list-item-label').click()
                                }
                                let _doms = document.querySelectorAll('.arco-cascader-list-wrapper ul')[1].querySelectorAll('li')
                                for (let _item of _doms) {
                                    if (_item.innerText == __arr1[1]) {
                                        _item.querySelector('.arco-checkbox').click()
                                        await this.sleep(800)
                                    }
                                }
                                await this.sleep(500)
                            }
                        }
                    }

                }
                document.querySelector('#categories button').click();
            }

            if (arr1[1]) {
                await this.sleep(800)
                document.querySelector('#followerSize button').click();
                let followerArr = arr1[1].split(';')
                let followerDome = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of followerArr) {
                    if (item) {
                        for (let _item of followerDome) {
                            if (_item.innerText == item) {
                                if (!_item.querySelector('.arco-checkbox input').checked) {
                                    _item.querySelector('.arco-checkbox').click()
                                }
                                await this.sleep(800)
                            }

                        }
                    }

                }
                document.querySelector('#followerSize button').click();
            }

            if (arr1[2]) {
                await this.sleep(800)
                document.querySelector('#avgCommissionRate button').click();
                let avgCommissionRate = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of avgCommissionRate) {
                    if (arr1[2] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
            }

            if (arr1[3]) {
                await this.sleep(800)
                document.querySelector('#contentType button').click();
                await this.sleep(500)
                let contentType = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of contentType) {
                    if (arr1[3] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
            }

            if (arr1[4]) {
                await this.sleep(800)
                document.querySelector('#creatorAgency button').click();
                await this.sleep(500)
                let creatorAgency = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of creatorAgency) {
                    if (arr1[4] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
            }

            if (arr1[5]) {
                await this.sleep(800)
                if (arr1[5] == 'true') {
                    document.querySelector('#isFastGrowing_input').click()
                }
            }
            document.querySelectorAll('.arco-space-item button')[0].click();
        }

        if (arr2.join('')) {
            await this.sleep(800)
            document.querySelectorAll('.arco-space-item button')[1].click();

            if (arr2[0]) {
                await this.sleep(800)
                document.querySelector('#followerAge button').click();
                await this.sleep(800)
                let followerAge = arr2[0].split(';')
                let followerAgeDome = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of followerAge) {
                    if (item) {
                        for (let _item of followerAgeDome) {
                            if (_item.innerText == item) {
                                if (!_item.querySelector('.arco-checkbox input').checked) {
                                    _item.querySelector('.arco-checkbox').click()
                                }
                                await this.sleep(800)
                            }

                        }
                    }

                }
                document.querySelector('#followerAge button').click();
            }

            if (arr2[1]) {
                await this.sleep(800)
                document.querySelector('#followerGender button').click();
                await this.sleep(500)
                let followerGender = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of followerGender) {
                    if (arr2[1] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
            }
            document.querySelectorAll('.arco-space-item button')[1].click();
        }

        if (arr3.join('')) {
            await this.sleep(800)
            document.querySelectorAll('.arco-space-item button')[2].click();
            if (arr3[0]) {
                await this.sleep(800)
                document.querySelector('#gmv button').click();
                await this.sleep(800)
                let gmv = arr3[0].split(';')
                let gmvDome = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of gmv) {
                    if (item) {
                        for (let _item of gmvDome) {
                            if (_item.innerText == item) {
                                if (!_item.querySelector('.arco-checkbox input').checked) {
                                    _item.querySelector('.arco-checkbox').click()
                                }
                                await this.sleep(800)
                            }

                        }
                    }

                }
                document.querySelector('#gmv button').click();
            }

            if (arr3[1]) {
                await this.sleep(800)
                document.querySelector('#unitsSold button').click();
                await this.sleep(800)
                let unitsSold = arr3[1].split(';')
                let unitsSoldDome = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of unitsSold) {
                    if (item) {
                        for (let _item of unitsSoldDome) {
                            if (_item.innerText == item) {
                                if (!_item.querySelector('.arco-checkbox input').checked) {
                                    _item.querySelector('.arco-checkbox').click()
                                }
                                await this.sleep(800)
                            }

                        }
                    }

                }
                document.querySelector('#unitsSold button').click();
            }
            if (arr3[2]) {
                await this.sleep(800)
                document.querySelector('#avgVideoViews button').click();
                await this.sleep(500)
                let avgVideoViews = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of avgVideoViews) {
                    if (arr3[2] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
                await this.sleep(500)
                document.querySelector('#avgVideoViews button').click();
            }
            if (arr3[3]) {
                await this.sleep(800)
                document.querySelector('#avgLiveViews button').click();
                await this.sleep(500)
                let avgLiveViews = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of avgLiveViews) {
                    if (arr3[3] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
                await this.sleep(500)
                document.querySelector('#avgLiveViews button').click();
            }
            if (arr3[4]) {
                await this.sleep(800)
                document.querySelector('#engagementRate button').click();
                await this.sleep(500)
                let engagementRate = document.querySelectorAll('.arco-select-popup-inner li');
                for (let item of engagementRate) {
                    if (arr3[4] == item.innerText) {
                        item.querySelector('.arco-radio').click()
                    }
                }
                await this.sleep(500)
                document.querySelector('#engagementRate button').click();
            }
            document.querySelectorAll('.arco-space-item button')[2].click();
        }
        await this.sleep(1000)
        this.startTest()

    }
    this.sleep = function (time) {
        return new Promise(resolve => {
            setTimeout(() => {
                resolve()
            }, time)
        })
    }
    this.startTest = function () {
        if (!this.startIng) return;
        if (this.is_premium != 2) return;
        if (document.querySelectorAll(".arco-spin-icon").length > 0) {
            setTimeout(() => {
                this.startTest()
            }, this.timeInterval * 5)
            return;
        }
        this.names = [];
        this.product_info_list = [];
        this.kolIndex = 0;
        /**
         * 获取当前页面所有的names
         * @type {NodeListOf<Element>}
         */
        this.doms = document.querySelectorAll(".arco-table-body .text-body-m-medium")
        for (let index = 0; index < this.doms.length; index++) {
            this.names.push({creator_id: this.doms[index].innerHTML})
        }

        /**
         * 点击进入邀请页面
         */
        try {
            document.querySelectorAll(".arco-table-body .arco-btn-primary")[0].click()
            this.msg(1, `添加${this.names[0].creator_id}成功!`)
        } catch (e) {
            setTimeout(() => {
                this.startTest()
            }, this.timeInterval * 2)
        }
        setTimeout(() => {
            this.addKol(this.names[this.kolIndex].creator_id)
        }, this.timeInterval * 2)


    }
    this.addKol = function (name) {
        if (!this.startIng) return;
        console.log("----此处执行了")
        /**
         * 如果length>0则表示邀请大页面并未加载完成
         *
         */
        if (document.querySelectorAll(".arco-spin-icon").length > 0) {
            setTimeout(() => {
                this.addKol(name)
            }, this.timeInterval)
            return;
        }
        /**
         * 打开折叠面板
         */
        if (document.querySelectorAll(".arco-collapse-item-header-title")[0].querySelector(".alliance-icon-ArrowUp").classList.contains('rotate-180')) {
            document.querySelectorAll(".arco-collapse-item-header-title")[0].click()
        }
        /**
         * 输入kolID
         * @type {Event}
         */
        var focusin = new Event('focusin', {
            bubbles: true,
            cancelable: true
        });

        let inputLabel = document.querySelector(".arco-collapse-item-content.arco-collapse-item-content-expanded .arco-input-size-default");
        inputLabel.dispatchEvent(focusin);
        inputLabel.value = name;
        let event = new Event("input", {bubbles: true});
        event.simulated = true;
        let tracker = inputLabel._valueTracker;
        if (tracker) {
            //console.log("111")
        }
        this.msg(0, `正在添加${name}...`)
        inputLabel.dispatchEvent(event);
        setTimeout(() => {
            this.clickKol(name)
        }, this.timeInterval * 2)
    }
    this.clickKol = function (name) {
        if (!this.startIng) return;
        /**
         * 是否还在加载kol列表
         */
        if (document.querySelectorAll(".arco-spin-icon").length > 0) {
            setTimeout(() => {
                this.clickKol(name);
            }, this.timeInterval * 2)
            return;
        }
        let items = document.querySelectorAll(".arco-list-item")
        if (items.length > 0) {
            //document.querySelectorAll(".arco-list-item")[0].classList.contains("cursor-not-allowed")
            for (let item of items) {
                console.log(item.querySelectorAll('.text-body-m-regular span')[1].innerText, name)
                if (item.querySelectorAll('.text-body-m-regular span')[1].innerText == name && !item.classList.contains("cursor-not-allowed")) {
                    item.click()
                }
            }
            this.msg(1, `添加${name}成功!`)
        }
        this.kolIndex++;
        setTimeout(() => {
            if (this.kolIndex >= this.names.length || this.kolIndex >= this.inviteNums) {
                this.selectProducts()
            } else {
                this.addKol(this.names[this.kolIndex].creator_id)
            }
        }, this.timeInterval * 2)
    }
    this.selectProducts = function () {
        if (!this.startIng) return;
        /**
         * 如果length>0则表示货物列表还在加载中
         */
        if (document.querySelectorAll(".arco-drawer-scroll .arco-spin-icon").length > 0) {

            setTimeout(() => {
                this.selectProducts()
            }, this.timeInterval)
            return;
        }
        /**
         * 打开折叠面板
         */
        if (document.querySelectorAll(".arco-collapse-item-header-title")[1].querySelector(".alliance-icon-ArrowUp").classList.contains('rotate-180')) {
            document.querySelectorAll(".arco-collapse-item-header-title")[1].click()
        }
        /**
         * 打开货物选择面板
         */
        document.querySelectorAll(".arco-collapse-item-header-title")[1].parentNode.parentNode.querySelector(".arco-btn-primary").click()
        setTimeout(() => {
            this._selectProducts()
        }, this.timeInterval)


    }
    this._selectProducts = function () {
        if (!this.startIng) return;
        /**
         * 获取所有货物
         * 如果货物长度为0，则表示未加载出来
         *
         */

        if (document.querySelectorAll('.arco-spin-loading-layer').length != 0) {
            setTimeout(() => {
                this._selectProducts()
            }, this.timeInterval)
            return;
        }
        let allGoods = document.querySelectorAll(".arco-drawer-scroll tbody .arco-table-tr");
        /**
         * 通过用户填写的商品id选中商品货物
         *
         */
        for (let item of this.products) {
            for (let i = 0; i < allGoods.length; i++) {
                if (item.product_id == allGoods[i].querySelector('.arco-table-cell .text-body-s-regular').innerHTML.split(":")[1].trim()) {
                    /**
                     * 选中该商品
                     *
                     */

                    allGoods[i].querySelector('.arco-checkbox').click()
                    let obj = {
                        product_id: allGoods[i].querySelector('.arco-table-cell .text-body-s-regular').innerHTML.split(":")[1].trim(),
                        product_img_link: allGoods[i].querySelector(".arco-image-img").src,
                        product_name: allGoods[i].querySelector(".arco-typography").innerText,
                        product_commission_rate: item.commission_rate
                    }
                    this.product_info_list.push(obj)
                    break;
                }
            }
            if (this.product_info_list.length == this.products.length) {
                break;
            }
        }
        if (this.product_info_list.length != this.products.length) {
            if (!document.querySelector(".arco-drawer-scroll .arco-drawer-content .arco-pagination-list").querySelectorAll('li')[document.querySelector(".arco-drawer-scroll .arco-drawer-content .arco-pagination-list").querySelectorAll('li').length - 1].classList.contains('arco-pagination-item-disabled')) {
                document.querySelector(".arco-drawer-scroll .arco-drawer-content .arco-pagination-list").querySelectorAll('li')[document.querySelector(".arco-drawer-scroll .arco-drawer-content .arco-pagination-list").querySelectorAll('li').length - 1].click()
                setTimeout(() => {
                    this._selectProducts()
                }, this.timeInterval)
                return;
            }

        }

        /**
         * 点击添加按钮
         */
        document.querySelector(".arco-drawer-scroll .arco-btn-primary").click()


        setTimeout(() => {
            this.fillPrice()
        }, this.timeInterval)
    }
    this.deleteRepeat = function () {
        let allDomes = document.querySelectorAll(".zoomModal-appear-done")[0].querySelectorAll('tbody .arco-table-tr');

        if (allDomes.length > 0) {
            for (let item of allDomes) {
                this.deleteNames.push(item.querySelectorAll('.arco-table-td .arco-typography')[1].innerHTML.replace('@', ''))
            }
            document.querySelectorAll(".zoomModal-appear-done")[0].querySelector('.arco-modal-footer .arco-btn-primary').click()
            setTimeout(() => {
                console.log("-----开始删除", this.deleteNames)
                this._deleteRepeat();
            }, this.timeInterval)

        } else {
            setTimeout(() => {
                this.deleteRepeat()
            }, this.timeInterval)
        }
    }
    this._deleteRepeat = function (val) {
        /**
         * 打开折叠面板
         */
        if (document.querySelectorAll(".arco-collapse-item-header-title")[0].querySelector(".alliance-icon-ArrowUp").classList.contains('rotate-180')) {
            document.querySelectorAll(".arco-collapse-item-header-title")[0].click()
        }
        let pageDomes = document.querySelectorAll(".arco-collapse-item-header-title")[0].parentNode.parentNode.querySelector('.arco-pagination-list').querySelectorAll('li');
        if (!val) {
            pageDomes[1].click()
            this._deleteRepeat(true)
            return;
        }
        let allKol = document.querySelectorAll(".arco-collapse-item-header-title")[0].parentNode.parentNode.querySelectorAll('tbody tr')
        for (let item of allKol) {
            let name = item.querySelector('.text-body-m-medium .arco-typography').innerHTML
            if (this.deleteNames.includes(name)) {
                item.querySelector('.arco-btn-primary').click()
                for (let i = 0; i < this.names.length; i++) {
                    if (this.names[i].creator_id == name) {
                        this.names.splice(i, 1)
                    }
                }
                break;
            }
        }
        if (!pageDomes[pageDomes.length - 1].classList.contains('arco-pagination-item-disabled')) {
            pageDomes[pageDomes.length - 1].click()
            this._deleteRepeat(true)
            return;
        }
        setTimeout(() => {
            this.deleteNames = []
            if (this.names.length == 0) {
                window.history.back();
                this.clickFilter()
                return;
            }
            this.selectProducts()
        }, this.timeInterval * 2)

    }
    this.fillPrice = function () {
        if (!this.startIng) return;
        /**
         * 邀请是否成功
         */
        if (document.querySelectorAll(".arco-table-body")[0].querySelectorAll(".arco-table-td")[7].querySelectorAll(".arco-spin").length > 0) {
            /**
             * 是否重复
             *
             */
            console.log("是否重复", document.querySelectorAll('.zoomModal-appear-done'))
            if (document.querySelectorAll('.zoomModal-appear-done').length > 0) {
                setTimeout(() => {
                    this.deleteRepeat()
                }, this.timeInterval)
            } else {
                setTimeout(() => {
                    this.fillPrice()
                }, this.timeInterval)
            }

            return;
        }

        /**
         * 向价格input中填值
         * @type {Event}
         */
        var focusout = new Event('focusout', {
            bubbles: true,
            cancelable: true
        });
        let allInputLabel = document.querySelectorAll(".arco-table-body")[0].querySelectorAll(".arco-table-tr");

        for (let i = 0; i < allInputLabel.length; i++) {
            let item = allInputLabel[i]
            let product_id = item.querySelector('.arco-table-checkbox input').value

            for (let j = 0; j < this.product_info_list.length; j++) {
                let _item = this.product_info_list[j];
                if (_item.product_id == product_id) {
                    item.querySelector('.arco-input-group input').value = _item.product_commission_rate;
                    let event = new Event("input", {bubbles: true});
                    event.simulated = true;
                    let tracker = item.querySelector('.arco-input-group input')._valueTracker;
                    if (tracker) {
                        //tracker.setValue(lastValue);
                    }
                    item.querySelector('.arco-input-group input').dispatchEvent(event);
                    item.querySelector('.arco-input-group input').dispatchEvent(focusout);
                }
            }

        }
        let pageDoms = document.querySelector(".arco-table-body").parentNode.parentNode.parentNode.parentNode.querySelector('.arco-pagination .arco-pagination-list').querySelectorAll('li');
        if (!pageDoms[pageDoms.length - 1].classList.contains('arco-pagination-item-disabled')) {
            pageDoms[pageDoms.length - 1].click()
            setTimeout(() => {
                this.fillPrice()
            }, this.timeInterval)
            return;
        }


        setTimeout(() => {
            this.autoSamples()
        }, this.timeInterval * 3)

    }
    this.autoSamples = async function () {
        let doms = document.querySelectorAll(".arco-collapse-item-header-title")
        if (doms.length == 4) {
            if (doms[2].querySelector(".alliance-icon-ArrowUp").classList.contains('rotate-180')) {
                doms[2].click()
            }
            let arr = this.sample.split(";")
            if (arr[0] == '2') {
                doms[2].parentNode.parentNode.querySelector('.arco-collapse-item-content-box .arco-switch').click()
                await this.sleep(500)
                if (arr[1] == '2') {
                    doms[2].parentNode.parentNode.querySelectorAll('.arco-collapse-item-content-box label')[1].click()
                    await this.sleep(500)
                    document.querySelectorAll('.arco-modal-footer button')[1].click()
                }
            }

        }
        await this.sleep(500)
        this.msg(0, `填写分成`)
        this.fillAviteData()

    }
    this.fillAviteData = function () {
        if (!this.startIng) return;
        if (document.querySelectorAll(".arco-collapse-item-header-title")[document.querySelectorAll(".arco-collapse-item-header-title").length - 1].querySelector(".alliance-icon-ArrowUp").classList.contains('rotate-180')) {
            document.querySelectorAll(".arco-collapse-item-header-title")[document.querySelectorAll(".arco-collapse-item-header-title").length - 1].click()
        }
        /**
         * 填写活动名称
         * @type {Event}
         */
        var focusout = new Event('focusout', {
            bubbles: true,
            cancelable: true
        });
        let inputLabel = document.querySelector("#target_complete_details_name input");
        inputLabel.value = this.activityName;
        this.msg(0, `填写活动名称`)
        let event = new Event("input", {bubbles: true});
        event.simulated = true;
        let tracker = inputLabel._valueTracker;
        if (tracker) {
            //tracker.setValue(lastValue);
        }
        inputLabel.dispatchEvent(event);
        inputLabel.dispatchEvent(focusout);

        /**
         * 填写邮箱
         */
        let inputLabel2 = document.querySelector("#target_complete_details_contacts_7 input");
        inputLabel2.value = this.email;

        let tracker2 = inputLabel2._valueTracker;
        if (tracker2) {
            //tracker.setValue(lastValue);
        }
        inputLabel2.dispatchEvent(event);
        inputLabel2.dispatchEvent(focusout);
        this.msg(0, `填写邮箱${this.email}`)
        /**
         * 填写私信
         */
        let inputLabel3 = document.querySelector("#target_complete_details_message_input");
        inputLabel3.value = this.message;

        let tracker3 = inputLabel3._valueTracker;
        if (tracker3) {
            //tracker.setValue(lastValue);
        }
        inputLabel3.dispatchEvent(event);
        inputLabel3.dispatchEvent(focusout);
        this.msg(0, `填写私信`)

        /**
         * 查看店铺名称id
         */
        document.querySelector("#region-selector").click()
        this.shop_id = document.querySelector('.arco-popover-inner-content').querySelector(".text-body-s.text-neutral-text4").innerHTML.split(":")[1].trim()
        this.shop_name = document.querySelector('.arco-popover-inner-content').querySelector(".text-body-s.text-neutral-text4").parentNode.querySelector(".text-body-m-medium").innerHTML
        document.querySelector("#region-selector").click()
        /**
         * 填写日期
         *
         */
        document.querySelector("#target_complete_details_until input").click()
        this.selectDate(() => {
            this.msg(0, `填写日期${this.dateTime}`)
            setTimeout(() => {
                this.send()
            }, this.timeInterval * 2)
        })

    }
    this.selectDate = function (callback) {
        let arrTime = this.dateTime.split('/')
        let nowYear = parseInt(document.querySelectorAll('.arco-picker-header-label')[0].innerHTML)
        let nowMonth = parseInt(document.querySelectorAll('.arco-picker-header-label')[1].innerHTML)
        if (nowYear != parseInt(arrTime[2])) {
            //切换日期，年份只会向后切换
            if (nowYear < parseInt(arrTime[2])) {
                document.querySelectorAll('.arco-picker-header-icon')[3].click();
            } else {
                document.querySelectorAll('.arco-picker-header-icon')[0].click();
            }
            setTimeout(() => {
                this.selectDate(callback)
            }, 200)

        } else {
            if (nowMonth != parseInt(arrTime[0])) {
                //切换月份
                if (parseInt(arrTime[0]) > nowMonth) {
                    document.querySelectorAll('.arco-picker-header-icon')[2].click();
                } else {
                    document.querySelectorAll('.arco-picker-header-icon')[1].click();
                }
                setTimeout(() => {
                    this.selectDate(callback)
                }, 200)
            } else {
                let doms = document.querySelectorAll(".arco-picker-container .arco-picker-cell-in-view")
                for (let index = 0; index < doms.length; index++) {
                    if (parseInt(doms[index].innerText) == parseInt(arrTime[1])) {
                        doms[index].click()
                        callback()
                        return;
                    }
                }

            }
        }

    }
    this.send = function () {
        if (!this.startIng) return;
        /**
         * 发送私信信息
         */
        document.querySelectorAll(".self-end .arco-btn-primary")[0].click()
        setTimeout(() => {
            this.blackHome()
        }, this.timeInterval * 2)


    }
    this.sendBackgroundAdmin = function () {
        let obj = {
            creator_info_list: this.names,
            product_info_list: this.product_info_list,
            invitation_info: {
                shop_name: this.shop_name,
                shop_id: this.shop_id,
                shop_name: this.shop_name,
                invitation_email: this.email,
                invitation_name: this.activityName,
                valid_until: this.dateTime
            }
        }
        fetch('https://server.aivocado.online/tt-tool-server/shop/invitation-log', {
            method: 'POST', // 或者 'POST'、'PUT'、'DELETE' 等其他 HTTP 方法
            headers: {
                'my_token': this.token,// 设置请求头的 'Authorization' 字段
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(obj)
        })
            .then(response => response.json()) // 将响应转换为 JSON 对象
            .then(data => {
            }) // 处理响应数据
            .catch(error => console.error('Error:', error)); // 处理请求错误
    }
    this.blackHome = function () {
        if (!this.startIng) return;
        if (document.querySelectorAll(".arco-btn-primary").length == 2) {
            this.msg(1, `邀请成功`)
            this.inviteNums = this.inviteNums - this.names.length
            this.sendBackgroundAdmin()
            setTimeout(() => {
                document.querySelectorAll(".arco-btn-primary")[1].click()
                setTimeout(() => {
                    if (this.inviteNums <= 0) {
                        this.startIng = false
                        document.querySelector('.tike_start_btn_mode').style.display = 'none'
                        this.msg(1, `单次邀请完成`)
                        return
                    }
                    this.clickFilter();
                }, this.timeInterval * 5)
            }, this.timeInterval)
        } else {
            setTimeout(() => {
                this.blackHome()
            }, this.timeInterval * 2)

        }
    }
    this.addLoginPage = function () {
        var body = document.body;

        // 创建按钮元素
        var btn = document.createElement("div");

        // 设置按钮的class属性
        btn.className = "tike_start_btn_test";

        btn.style.cssText = `width: 350px;
    height: auto;
    background: rgb(255 255 255);
    border: none;
    outline: none;
    position: fixed;
    z-index: 9999999;
    bottom: 10px;
    right: 10px;
    border-radius: 10px;
    box-shadow: 0px 0px 15px 3px #ccc;
    overflow:hidden;
    font-size:13px;
    `
        // 设置按钮的文本内容
        btn.innerHTML = `<div style="
    width: 100%;
    height: 30px;
    background: rgb(244,236,236);
    co accent-color: colo;
    text-align: center;
    font-weight: 600;
    line-height: 30px;
">
<div style="
    display: flex;
    position: absolute;
    left: 10px;
    top: 10px;
">
<!--<div class="tikeCloseShopPage" style="-->
<!--    width: 15px;-->
<!--    height: 15px;-->
<!--    background: rgb(252,69,69);-->
<!--    border-radius: 10px;-->
<!--    cursor: pointer;-->
<!--">-->
<!--</div>-->
<div class="tikeMinShopPage" style="
    width: 15px;
    height: 15px;
    background: rgb(252,175,36);
    border-radius: 10px;
    margin-left: 10px;
    cursor: pointer;
">
</div>
</div>
提客 自动化工具

</div>
<div class="tikeTestContent" style="padding: 10px;height: 400px">
   <div style="
    text-align: center;
    margin-top: 80px;
    font-size: 20px;
">
    <svg t="1706519615359" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2440" width="200" height="200" style="
    width: 40px;
    height: 40px;
    margin: auto;
"><path d="M800 416V288a288 288 0 0 0-288-288 288 288 0 0 0-288 288v128a96 96 0 0 0-96 96v224a288 288 0 0 0 288 288h192a288 288 0 0 0 288-288v-224a96 96 0 0 0-96-96zM288 288a224 224 0 1 1 448 0v128h-64V288.064a160 160 0 0 0-160-160 160 160 0 0 0-160 160V416H288V288z m352 0v128h-256V288a128 128 0 0 1 256 0z m192 320v128c0 123.488-100.512 224-224 224h-192c-123.488 0-224-100.512-224-224v-224a32 32 0 0 1 32-32h576c17.632 0 32 14.336 32 32v96z" fill="#333333" p-id="2441"></path><path d="M512 608a64 64 0 0 0-64 64c0 19.424 10.656 56.32 21.344 85.504 8.704 23.744 19.648 42.432 42.656 42.432 25.024 0 33.952-18.496 42.688-42.112 10.816-29.248 21.312-66.336 21.312-85.824a64 64 0 0 0-64-64z" fill="#333333" p-id="2442"></path></svg>
    <div style="
    margin: 15px 0px;
">请先登录或者注册</div>
    <button class="btnconseladjfeja_test" style="
    width: 130px;
    height: 40px;
    border-radius: 24px;
    background: linear-gradient(to right, red, orange);
    color: white;
    font-weight: 600;
">登录/注册</button>
</div>
<div>

</div>
</div>
`;

        //btn.onclick = () => this.startTest()
        // 将按钮添加到body中
        body.appendChild(btn);
        // document.querySelector(".tikeCloseShopPage").addEventListener('click', function () {
        //     body.removeChild(document.querySelector('.tike_start_btn_test'))
        //     this.startIng = false
        // });
        document.querySelector(".tikeMinShopPage").addEventListener('click', function () {
            document.querySelector('.tike_start_btn_test').style.display = 'none';
            document.querySelector('.tike_min_btn').style.display = 'block';
        });
        document.querySelector(".btnconseladjfeja_test").addEventListener('click', function () {
            window.open('https://tike888.com/admin/#/welcome', '_blank');
        });
        this.addMinBtn()
    }
    this.addMembership = function () {
        var body = document.body;

        // 创建按钮元素
        var btn = document.createElement("div");

        // 设置按钮的class属性
        btn.className = "tike_start_btn_test";

        btn.style.cssText = `width: 350px;
    height: auto;
    background: rgb(255 255 255);
    border: none;
    outline: none;
    position: fixed;
    z-index: 9999999;
    bottom: 10px;
    right: 10px;
    border-radius: 10px;
    box-shadow: 0px 0px 15px 3px #ccc;
    overflow:hidden;
    font-size:13px;
    `
        // 设置按钮的文本内容
        btn.innerHTML = `<div style="
    width: 100%;
    height: 30px;
    background: rgb(244,236,236);
    co accent-color: colo;
    text-align: center;
    font-weight: 600;
    line-height: 30px;
">
<div style="
    display: flex;
    position: absolute;
    left: 10px;
    top: 10px;
">
<!--<div class="tikeCloseShopPage" style="-->
<!--    width: 15px;-->
<!--    height: 15px;-->
<!--    background: rgb(252,69,69);-->
<!--    border-radius: 10px;-->
<!--    cursor: pointer;-->
<!--">-->
<!--</div>-->
<div class="tikeMinShopPage" style="
    width: 15px;
    height: 15px;
    background: rgb(252,175,36);
    border-radius: 10px;
    margin-left: 10px;
    cursor: pointer;
">
</div>
</div>
提客 自动化工具

</div>
<div class="tikeTestContent" style="padding: 10px;height: 400px">
   <div style="
    text-align: center;
    margin-top: 80px;
    font-size: 20px;
">
    <svg t="1706519615359" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2440" width="200" height="200" style="
    width: 40px;
    height: 40px;
    margin: auto;
"><path d="M800 416V288a288 288 0 0 0-288-288 288 288 0 0 0-288 288v128a96 96 0 0 0-96 96v224a288 288 0 0 0 288 288h192a288 288 0 0 0 288-288v-224a96 96 0 0 0-96-96zM288 288a224 224 0 1 1 448 0v128h-64V288.064a160 160 0 0 0-160-160 160 160 0 0 0-160 160V416H288V288z m352 0v128h-256V288a128 128 0 0 1 256 0z m192 320v128c0 123.488-100.512 224-224 224h-192c-123.488 0-224-100.512-224-224v-224a32 32 0 0 1 32-32h576c17.632 0 32 14.336 32 32v96z" fill="#333333" p-id="2441"></path><path d="M512 608a64 64 0 0 0-64 64c0 19.424 10.656 56.32 21.344 85.504 8.704 23.744 19.648 42.432 42.656 42.432 25.024 0 33.952-18.496 42.688-42.112 10.816-29.248 21.312-66.336 21.312-85.824a64 64 0 0 0-64-64z" fill="#333333" p-id="2442"></path></svg>
    <div style="
    margin: 15px 0px;
">开通会员即可享受所有服务</div>
    <button class="btnconseladjfeja_test" style="
    width: 130px;
    height: 40px;
    border-radius: 24px;
    background: linear-gradient(to right, red, orange);
    color: white;
    font-weight: 600;
">开通会员</button>
</div>
<div>

</div>
</div>
`;

        //btn.onclick = () => this.startTest()
        // 将按钮添加到body中
        body.appendChild(btn);
        // document.querySelector(".tikeCloseShopPage").addEventListener('click', function () {
        //     body.removeChild(document.querySelector('.tike_start_btn_test'))
        //     this.startIng = false
        // });
        document.querySelector(".tikeMinShopPage").addEventListener('click', function () {
            document.querySelector('.tike_start_btn_test').style.display = 'none';
            document.querySelector('.tike_min_btn').style.display = 'block';
        });
        document.querySelector(".btnconseladjfeja_test").addEventListener('click', function () {
            window.open('https://tike888.com/admin/#/welcome', '_blank');
        });
        this.addMinBtn()
    }
    this.addMainPage = function () {
        var body = document.body;

        // 创建按钮元素
        var btn = document.createElement("div");

        // 设置按钮的class属性
        btn.className = "tike_start_btn_test";

        btn.style.cssText = `width: 350px;
    height: auto;
    background: rgb(255 255 255);
    border: none;
    outline: none;
    position: fixed;
    z-index: 9999999;
    bottom: 10px;
    right: 10px;
    border-radius: 10px;
    box-shadow: 0px 0px 15px 3px #ccc;
    overflow:hidden;
    font-size:13px;
    `
        // 设置按钮的文本内容
        btn.innerHTML = `<div style="
    width: 100%;
    height: 30px;
    background: rgb(244,236,236);
    co accent-color: colo;
    text-align: center;
    font-weight: 600;
    line-height: 30px;
">
<div style="
    display: flex;
    position: absolute;
    left: 10px;
    top: 10px;
">
<!--<div class="tikeCloseShopPage" style="-->
<!--    width: 15px;-->
<!--    height: 15px;-->
<!--    background: rgb(252,69,69);-->
<!--    border-radius: 10px;-->
<!--    cursor: pointer;-->
<!--">-->
<!--</div>-->
<div class="tikeMinShopPage" style="
    width: 15px;
    height: 15px;
    background: rgb(252,175,36);
    border-radius: 10px;
    margin-left: 10px;
    cursor: pointer;
">
</div>
</div>
提客 自动化工具

</div>
<div class="tikeTestContent" style="padding: 10px;">
<div><span>选择邀约模版:</span><a href="https://tike888.com/admin/#/other/Template" target="_blank" style="color: #00a6ff">添加/修改模版</a><br />
<div class="allInvity_XBd7fd293t12kei">

</div>

</div>
<div><span>活动名称:</span><br /><input type="text" placeholder="invitation name" class="tike_activityName" disabled/></div>
<div><span>活动时间:</span><br /><input type="text" placeholder="End date" class="tike_activity_date" disabled/></div>
<div><span>邮件地址:</span><br /><input type="text" placeholder="email" class="tike_email" disabled/></div>
<div><span>单次邀请人数（默认单次邀请人数50）:</span><br /><input type="text" placeholder="有效值（1 - 100）" class="tike_invite_nums" disabled/></div>
<div><span>私信内容（500字以内）:</span>
<br />
<textarea disabled name="" id="" cols="30" rows="5" placeholder="请输入私信内容" maxlength="500" style="max-height: 100px" class="tike_send_message"></textarea>
</div>
<div class="tikeConsoleLog" style="height: 200px;overflow-y: auto;">

</div>
<div>
<button class="_XBd7fd293t12kei" style="
    width: 100px;
    height: 25px;
    border-radius: 4px;
">开始邀约</button>
<button class="__XBd7fd29fdajfkekei" style="
    width: 100px;
    height: 25px;
    margin-left: 10px;
    border-radius: 4px;
">暂停/停止</button>
</div>
</div>

`;

        //btn.onclick = () => this.startTest()
        // 将按钮添加到body中
        var mode = document.createElement("div");
        mode.className = "tike_start_btn_mode";

        mode.style.cssText = `width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0,0.7);
    position: fixed;
    top:0px;
    left:0px;
    z-index: 9999998;
    overflow:hidden;
    font-size:26px;
    color:white;
    text-align: center;
     align-items: center;
    display: none;
    justify-content: center;
    font-weight: 800;
    `
        mode.innerHTML = `自动邀约中，点击 "暂停/停止" 撤除遮罩`
        body.appendChild(btn);
        body.appendChild(mode)
        // 创建按钮元素

        // document.querySelector(".tikeCloseShopPage").addEventListener('click', function () {
        //     body.removeChild(document.querySelector('.tike_start_btn_test'))
        //     this.startIng = false
        // });
        this.getUserData(res => {
            console.log(res)
            let str = ''
            for (let item of res) {
                str += `<div class="radio-inline">
                <input type="radio"  name="killOrder" id="killOrder${item.template_id}" class="killOrder12312" value="${item.template_id}"/>
                <label for="killOrder${item.template_id}" >${item.invitation_name}</label>
                </div>`
            }
            document.querySelector('.allInvity_XBd7fd293t12kei').innerHTML = str
            document.querySelectorAll(".killOrder12312").forEach((element) => {
                element.addEventListener('click', () => {
                    var tesObj = document.getElementsByName("killOrder");
                    for (var i = 0; i < tesObj.length; i++) {
                        if (tesObj[i].checked == true) {
                            this.initUserData(res, tesObj[i].value)
                        }
                    }
                });
            });
            document.getElementsByName("killOrder")[0].click()
        })
        document.querySelector(".tikeMinShopPage").addEventListener('click', function () {
            document.querySelector('.tike_start_btn_test').style.display = 'none';
            document.querySelector('.tike_min_btn').style.display = 'block';
        });
        document.querySelector("._XBd7fd293t12kei").addEventListener('click', () => {
            if (!this.startIng) {

                let activityName = document.querySelector(".tike_activityName").value;
                let activityDate = document.querySelector(".tike_activity_date").value;
                let email = document.querySelector(".tike_email").value;
                let message = document.querySelector(".tike_send_message").value;
                let inviteNums = document.querySelector(".tike_invite_nums").value;

                if (activityName.trim() == "") {
                    this.msg(2, "填写活动名称")
                    return
                }
                if (activityDate.trim() == "") {
                    this.msg(2, "填写邀请日期")
                    return
                }
                if (email.trim() == "") {
                    this.msg(2, "填写电子邮件")
                    return
                }
                if (message.trim() == "") {
                    this.msg(2, "填写私信内容")
                    return
                }
                if (parseInt(inviteNums.trim()) <= 0) {
                    this.msg(2, "请填写合法的值")
                    return
                }
                if (parseInt(inviteNums.trim())) {
                    this.inviteNums = parseInt(inviteNums.trim())
                }

                this.activityName = activityName;
                this.dateTime = activityDate;
                this.email = email;
                this.message = message;
                // this.sample=
                this.startIng = true
                //console.log(date)
                document.querySelector('.tike_start_btn_mode').style.display = 'flex'
                this.clickFilter()

            }
        });
        document.querySelector(".__XBd7fd29fdajfkekei").addEventListener('click', () => {
            this.startIng = false
            document.querySelector('.tike_start_btn_mode').style.display = 'none'
            this.msg(2, `停止！`)
        });

        this.addMinBtn()
    }
    this.initUserData = function (arr, id) {
        let item = {}
        for (let _item of arr) {
            if (_item.template_id == parseInt(id)) {
                item = _item
            }
        }
        //console.log(item)
        document.querySelector(".tike_activityName").value = item.invitation_name
        document.querySelector(".tike_activity_date").value = this.formatTime(item.invitation_time)
        document.querySelector(".tike_email").value = item.email
        document.querySelector(".tike_send_message").value = item.message
        document.querySelector(".tike_invite_nums").value = item.creator_batch_size
        this.products = item.product_list
        this.creators_filter = item.creators_filter
        this.followers_filter = item.followers_filter
        this.performance_filter = item.performance_filter
        this.sample = item.sample_option
    }
    this.formatTime = function (timestampSec) {
        var date = new Date(timestampSec * 1000);

// 获取年、月、日
        var year = date.getFullYear(); // 获取年份
        var month = String(date.getMonth() + 1).padStart(2, '0'); // 获取月份，并确保它是两位数（例如：'01'、'02'）
        var day = String(date.getDate()).padStart(2, '0'); // 获取日期，并确保它是两位数（例如：'01'、'02'）

// 将年、月、日拼接成所需的格式
        return month + '/' + day + '/' + year;

    }
    this.addMinBtn = function () {
        var body = document.body;
        // 创建按钮元素
        var btn = document.createElement("div");
        // 设置按钮的class属性
        btn.className = "tike_min_btn";
        btn.style.cssText = `width: 30px;
        height: 122px;
        background: rgb(255 255 255);
        border: none;
        outline: none;
        position: fixed;
        z-index: 9999999;
        bottom: 80px;
        right: 0px;
        box-shadow: 0px 0px 15px 3px #ccc;
        overflow:hidden;
        border-top-left-radius: 10px;
        border-bottom-left-radius: 10px;
        text-align: center;
        font-size: 16px;
        cursor: pointer;
        display:none
        `
        btn.innerHTML = "提客自动化"
        body.appendChild(btn);
        document.querySelector(".tike_min_btn").addEventListener('click', function () {
            document.querySelector('.tike_start_btn_test').style.display = 'block';
            document.querySelector('.tike_min_btn').style.display = 'none';
        });
    }
    this.msg = function (type, msg) {
        let hos = document.querySelector('.tikeConsoleLog').innerHTML;
        let div;
        if (type == 0) {
            div = '<div>' + msg + '</div>'
        } else if (type == 1) {
            div = '<div style="color: green">' + msg + '</div>'
        } else if (type == 2) {
            div = '<div style="color: red">' + msg + '</div>'
        }
        document.querySelector('.tikeConsoleLog').innerHTML = div + hos;
    }
}
