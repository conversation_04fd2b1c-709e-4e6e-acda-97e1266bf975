var i18n=function(){"use strict";const g=a=>typeof a=="string",j=()=>{let a,e;const t=new Promise((s,n)=>{a=s,e=n});return t.resolve=a,t.reject=e,t},_=a=>a==null?"":""+a,Se=(a,e,t)=>{a.forEach(s=>{e[s]&&(t[s]=e[s])})},be=/###/g,ee=a=>a&&a.indexOf("###")>-1?a.replace(be,"."):a,te=a=>!a||g(a),D=(a,e,t)=>{const s=g(e)?e.split("."):e;let n=0;for(;n<s.length-1;){if(te(a))return{};const i=ee(s[n]);!a[i]&&t&&(a[i]=new t),Object.prototype.hasOwnProperty.call(a,i)?a=a[i]:a={},++n}return te(a)?{}:{obj:a,k:ee(s[n])}},se=(a,e,t)=>{const{obj:s,k:n}=D(a,e,Object);if(s!==void 0||e.length===1){s[n]=t;return}let i=e[e.length-1],r=e.slice(0,e.length-1),l=D(a,r,Object);for(;l.obj===void 0&&r.length;)i=`${r[r.length-1]}.${i}`,r=r.slice(0,r.length-1),l=D(a,r,Object),l!=null&&l.obj&&typeof l.obj[`${l.k}.${i}`]<"u"&&(l.obj=void 0);l.obj[`${l.k}.${i}`]=t},Pe=(a,e,t,s)=>{const{obj:n,k:i}=D(a,e,Object);n[i]=n[i]||[],n[i].push(t)},z=(a,e)=>{const{obj:t,k:s}=D(a,e);if(t&&Object.prototype.hasOwnProperty.call(t,s))return t[s]},ve=(a,e,t)=>{const s=z(a,t);return s!==void 0?s:z(e,t)},ne=(a,e,t)=>{for(const s in e)s!=="__proto__"&&s!=="constructor"&&(s in a?g(a[s])||a[s]instanceof String||g(e[s])||e[s]instanceof String?t&&(a[s]=e[s]):ne(a[s],e[s],t):a[s]=e[s]);return a},k=a=>a.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var Oe={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const Le=a=>g(a)?a.replace(/[&<>"'\/]/g,e=>Oe[e]):a;class Ce{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(t!==void 0)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}const we=[" ",",","?","!",";"],Re=new Ce(20),Ae=(a,e,t)=>{e=e||"",t=t||"";const s=we.filter(r=>e.indexOf(r)<0&&t.indexOf(r)<0);if(s.length===0)return!0;const n=Re.getRegExp(`(${s.map(r=>r==="?"?"\\?":r).join("|")})`);let i=!n.test(a);if(!i){const r=a.indexOf(t);r>0&&!n.test(a.substring(0,r))&&(i=!0)}return i},q=function(a,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!a)return;if(a[e])return Object.prototype.hasOwnProperty.call(a,e)?a[e]:void 0;const s=e.split(t);let n=a;for(let i=0;i<s.length;){if(!n||typeof n!="object")return;let r,l="";for(let o=i;o<s.length;++o)if(o!==i&&(l+=t),l+=s[o],r=n[l],r!==void 0){if(["string","number","boolean"].indexOf(typeof r)>-1&&o<s.length-1)continue;i+=o-i+1;break}n=r}return n},B=a=>a==null?void 0:a.replace("_","-"),Te={type:"logger",log(a){this.output("log",a)},warn(a){this.output("warn",a)},error(a){this.output("error",a)},output(a,e){var t,s;(s=(t=console==null?void 0:console[a])==null?void 0:t.apply)==null||s.call(t,console,e)}};class J{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||Te,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,s,n){return n&&!this.debug?null:(g(e[0])&&(e[0]=`${s}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new J(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new J(this.logger,e)}}var R=new J;class W{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(s=>{this.observers[s]||(this.observers[s]=new Map);const n=this.observers[s].get(t)||0;this.observers[s].set(t,n+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),n=1;n<t;n++)s[n-1]=arguments[n];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(r=>{let[l,o]=r;for(let u=0;u<o;u++)l(...s)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(r=>{let[l,o]=r;for(let u=0;u<o;u++)l.apply(l,[e,...s])})}}class ie extends W{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,s){var u,h;let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=n.keySeparator!==void 0?n.keySeparator:this.options.keySeparator,r=n.ignoreJSONStructure!==void 0?n.ignoreJSONStructure:this.options.ignoreJSONStructure;let l;e.indexOf(".")>-1?l=e.split("."):(l=[e,t],s&&(Array.isArray(s)?l.push(...s):g(s)&&i?l.push(...s.split(i)):l.push(s)));const o=z(this.data,l);return!o&&!t&&!s&&e.indexOf(".")>-1&&(e=l[0],t=l[1],s=l.slice(2).join(".")),o||!r||!g(s)?o:q((h=(u=this.data)==null?void 0:u[e])==null?void 0:h[t],s,i)}addResource(e,t,s,n){let i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1};const r=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator;let l=[e,t];s&&(l=l.concat(r?s.split(r):s)),e.indexOf(".")>-1&&(l=e.split("."),n=t,t=l[1]),this.addNamespaces(t),se(this.data,l,n),i.silent||this.emit("added",e,t,s,n)}addResources(e,t,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(const i in s)(g(s[i])||Array.isArray(s[i]))&&this.addResource(e,t,i,s[i],{silent:!0});n.silent||this.emit("added",e,t,s)}addResourceBundle(e,t,s,n,i){let r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},l=[e,t];e.indexOf(".")>-1&&(l=e.split("."),n=s,s=t,t=l[1]),this.addNamespaces(t);let o=z(this.data,l)||{};r.skipCopy||(s=JSON.parse(JSON.stringify(s))),n?ne(o,s,i):o={...o,...s},se(this.data,l,o),r.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(n=>t[n]&&Object.keys(t[n]).length>0)}toJSON(){return this.data}}var re={processors:{},addPostProcessor(a){this.processors[a.name]=a},handle(a,e,t,s,n){return a.forEach(i=>{var r;e=((r=this.processors[i])==null?void 0:r.process(e,t,s,n))??e}),e}};const ae={},oe=a=>!g(a)&&typeof a!="boolean"&&typeof a!="number";class G extends W{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),Se(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=R.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;const s=this.resolve(e,t);return(s==null?void 0:s.res)!==void 0}extractFromKey(e,t){let s=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;s===void 0&&(s=":");const n=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator;let i=t.ns||this.options.defaultNS||[];const r=s&&e.indexOf(s)>-1,l=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!Ae(e,s,n);if(r&&!l){const o=e.match(this.interpolator.nestingRegexp);if(o&&o.length>0)return{key:e,namespaces:g(i)?[i]:i};const u=e.split(s);(s!==n||s===n&&this.options.ns.indexOf(u[0])>-1)&&(i=u.shift()),e=u.join(n)}return{key:e,namespaces:g(i)?[i]:i}}translate(e,t,s){if(typeof t!="object"&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),typeof t=="object"&&(t={...t}),t||(t={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);const n=t.returnDetails!==void 0?t.returnDetails:this.options.returnDetails,i=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,{key:r,namespaces:l}=this.extractFromKey(e[e.length-1],t),o=l[l.length-1],u=t.lng||this.language,h=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if((u==null?void 0:u.toLowerCase())==="cimode"){if(h){const O=t.nsSeparator||this.options.nsSeparator;return n?{res:`${o}${O}${r}`,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:`${o}${O}${r}`}return n?{res:r,usedKey:r,exactUsedKey:r,usedLng:u,usedNS:o,usedParams:this.getUsedParamsDetails(t)}:r}const c=this.resolve(e,t);let f=c==null?void 0:c.res;const m=(c==null?void 0:c.usedKey)||r,d=(c==null?void 0:c.exactUsedKey)||r,p=["[object Number]","[object Function]","[object RegExp]"],y=t.joinArrays!==void 0?t.joinArrays:this.options.joinArrays,b=!this.i18nFormat||this.i18nFormat.handleAsObject,P=t.count!==void 0&&!g(t.count),A=G.hasDefaultValue(t),C=P?this.pluralResolver.getSuffix(u,t.count,t):"",K=t.ordinal&&P?this.pluralResolver.getSuffix(u,t.count,{ordinal:!1}):"",M=P&&!t.ordinal&&t.count===0,S=M&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${C}`]||t[`defaultValue${K}`]||t.defaultValue;let v=f;b&&!f&&A&&(v=S);const F=oe(v),E=Object.prototype.toString.apply(v);if(b&&v&&F&&p.indexOf(E)<0&&!(g(y)&&Array.isArray(v))){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const O=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,v,{...t,ns:l}):`key '${r} (${this.language})' returned an object instead of string.`;return n?(c.res=O,c.usedParams=this.getUsedParamsDetails(t),c):O}if(i){const O=Array.isArray(v),w=O?[]:{},pe=O?d:m;for(const T in v)if(Object.prototype.hasOwnProperty.call(v,T)){const $=`${pe}${i}${T}`;A&&!f?w[T]=this.translate($,{...t,defaultValue:oe(S)?S[T]:void 0,joinArrays:!1,ns:l}):w[T]=this.translate($,{...t,joinArrays:!1,ns:l}),w[T]===$&&(w[T]=v[T])}f=w}}else if(b&&g(y)&&Array.isArray(f))f=f.join(y),f&&(f=this.extendTranslation(f,e,t,s));else{let O=!1,w=!1;!this.isValidLookup(f)&&A&&(O=!0,f=S),this.isValidLookup(f)||(w=!0,f=r);const T=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&w?void 0:f,$=A&&S!==f&&this.options.updateMissing;if(w||O||$){if(this.logger.log($?"updateKey":"missingKey",u,o,r,$?S:f),i){const L=this.resolve(r,{...t,keySeparator:!1});L&&L.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let U=[];const Z=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if(this.options.saveMissingTo==="fallback"&&Z&&Z[0])for(let L=0;L<Z.length;L++)U.push(Z[L]);else this.options.saveMissingTo==="all"?U=this.languageUtils.toResolveHierarchy(t.lng||this.language):U.push(t.lng||this.language);const me=(L,N,H)=>{var xe;const ye=A&&H!==f?H:T;this.options.missingKeyHandler?this.options.missingKeyHandler(L,o,N,ye,$,t):(xe=this.backendConnector)!=null&&xe.saveMissing&&this.backendConnector.saveMissing(L,o,N,ye,$,t),this.emit("missingKey",L,o,N,f)};this.options.saveMissing&&(this.options.saveMissingPlurals&&P?U.forEach(L=>{const N=this.pluralResolver.getSuffixes(L,t);M&&t[`defaultValue${this.options.pluralSeparator}zero`]&&N.indexOf(`${this.options.pluralSeparator}zero`)<0&&N.push(`${this.options.pluralSeparator}zero`),N.forEach(H=>{me([L],r+H,t[`defaultValue${H}`]||S)})}):me(U,r,S))}f=this.extendTranslation(f,e,t,c,s),w&&f===r&&this.options.appendNamespaceToMissingKey&&(f=`${o}:${r}`),(w||O)&&this.options.parseMissingKeyHandler&&(f=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${o}:${r}`:r,O?f:void 0))}return n?(c.res=f,c.usedParams=this.getUsedParamsDetails(t),c):f}extendTranslation(e,t,s,n,i){var u,h;var r=this;if((u=this.i18nFormat)!=null&&u.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...s},s.lng||this.language||n.usedLng,n.usedNS,n.usedKey,{resolved:n});else if(!s.skipInterpolation){s.interpolation&&this.interpolator.init({...s,interpolation:{...this.options.interpolation,...s.interpolation}});const c=g(e)&&(((h=s==null?void 0:s.interpolation)==null?void 0:h.skipOnVariables)!==void 0?s.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let f;if(c){const d=e.match(this.interpolator.nestingRegexp);f=d&&d.length}let m=s.replace&&!g(s.replace)?s.replace:s;if(this.options.interpolation.defaultVariables&&(m={...this.options.interpolation.defaultVariables,...m}),e=this.interpolator.interpolate(e,m,s.lng||this.language||n.usedLng,s),c){const d=e.match(this.interpolator.nestingRegexp),p=d&&d.length;f<p&&(s.nest=!1)}!s.lng&&n&&n.res&&(s.lng=this.language||n.usedLng),s.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var d=arguments.length,p=new Array(d),y=0;y<d;y++)p[y]=arguments[y];return(i==null?void 0:i[0])===p[0]&&!s.context?(r.logger.warn(`It seems you are nesting recursively key: ${p[0]} in key: ${t[0]}`),null):r.translate(...p,t)},s)),s.interpolation&&this.interpolator.reset()}const l=s.postProcess||this.options.postProcess,o=g(l)?[l]:l;return e!=null&&(o!=null&&o.length)&&s.applyPostProcessor!==!1&&(e=re.handle(o,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...n,usedParams:this.getUsedParamsDetails(s)},...s}:s,this)),e}resolve(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s,n,i,r,l;return g(e)&&(e=[e]),e.forEach(o=>{if(this.isValidLookup(s))return;const u=this.extractFromKey(o,t),h=u.key;n=h;let c=u.namespaces;this.options.fallbackNS&&(c=c.concat(this.options.fallbackNS));const f=t.count!==void 0&&!g(t.count),m=f&&!t.ordinal&&t.count===0,d=t.context!==void 0&&(g(t.context)||typeof t.context=="number")&&t.context!=="",p=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);c.forEach(y=>{var b,P;this.isValidLookup(s)||(l=y,!ae[`${p[0]}-${y}`]&&((b=this.utils)!=null&&b.hasLoadedNamespace)&&!((P=this.utils)!=null&&P.hasLoadedNamespace(l))&&(ae[`${p[0]}-${y}`]=!0,this.logger.warn(`key "${n}" for languages "${p.join(", ")}" won't get resolved as namespace "${l}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach(A=>{var M;if(this.isValidLookup(s))return;r=A;const C=[h];if((M=this.i18nFormat)!=null&&M.addLookupKeys)this.i18nFormat.addLookupKeys(C,h,A,y,t);else{let S;f&&(S=this.pluralResolver.getSuffix(A,t.count,t));const v=`${this.options.pluralSeparator}zero`,F=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(f&&(C.push(h+S),t.ordinal&&S.indexOf(F)===0&&C.push(h+S.replace(F,this.options.pluralSeparator)),m&&C.push(h+v)),d){const E=`${h}${this.options.contextSeparator}${t.context}`;C.push(E),f&&(C.push(E+S),t.ordinal&&S.indexOf(F)===0&&C.push(E+S.replace(F,this.options.pluralSeparator)),m&&C.push(E+v))}}let K;for(;K=C.pop();)this.isValidLookup(s)||(i=K,s=this.getResource(A,y,K,t))}))})}),{res:s,usedKey:n,exactUsedKey:i,usedLng:r,usedNS:l}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,s){var i;let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return(i=this.i18nFormat)!=null&&i.getResource?this.i18nFormat.getResource(e,t,s,n):this.resourceStore.getResource(e,t,s,n)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],s=e.replace&&!g(e.replace);let n=s?e.replace:e;if(s&&typeof e.count<"u"&&(n.count=e.count),this.options.interpolation.defaultVariables&&(n={...this.options.interpolation.defaultVariables,...n}),!s){n={...n};for(const i of t)delete n[i]}return n}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,t.length)&&e[s]!==void 0)return!0;return!1}}class le{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=R.create("languageUtils")}getScriptPartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return null;const t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=B(e),!e||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(g(e)&&e.indexOf("-")>-1){let t;try{t=Intl.getCanonicalLocales(e)[0]}catch{}return t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t||(this.options.lowerCaseLng?e.toLowerCase():e)}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(s=>{if(t)return;const n=this.formatLanguageCode(s);(!this.options.supportedLngs||this.isSupportedCode(n))&&(t=n)}),!t&&this.options.supportedLngs&&e.forEach(s=>{if(t)return;const n=this.getLanguagePartFromCode(s);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find(i=>{if(i===n)return i;if(!(i.indexOf("-")<0&&n.indexOf("-")<0)&&(i.indexOf("-")>0&&n.indexOf("-")<0&&i.substring(0,i.indexOf("-"))===n||i.indexOf(n)===0&&n.length>1))return i})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),g(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let s=e[t];return s||(s=e[this.getScriptPartFromCode(t)]),s||(s=e[this.formatLanguageCode(t)]),s||(s=e[this.getLanguagePartFromCode(t)]),s||(s=e.default),s||[]}toResolveHierarchy(e,t){const s=this.getFallbackCodes(t||this.options.fallbackLng||[],e),n=[],i=r=>{r&&(this.isSupportedCode(r)?n.push(r):this.logger.warn(`rejecting language code not found in supportedLngs: ${r}`))};return g(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&i(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&i(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&i(this.getLanguagePartFromCode(e))):g(e)&&i(this.formatLanguageCode(e)),s.forEach(r=>{n.indexOf(r)<0&&i(this.formatLanguageCode(r))}),n}}const ue={zero:0,one:1,two:2,few:3,many:4,other:5},fe={select:a=>a===1?"one":"other",resolvedOptions:()=>({pluralCategories:["one","other"]})};class $e{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=R.create("pluralResolver"),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};const s=B(e==="dev"?"en":e),n=t.ordinal?"ordinal":"cardinal",i=JSON.stringify({cleanedCode:s,type:n});if(i in this.pluralRulesCache)return this.pluralRulesCache[i];let r;try{r=new Intl.PluralRules(s,{type:n})}catch{if(!Intl)return this.logger.error("No Intl support, please use an Intl polyfill!"),fe;if(!e.match(/-|_/))return fe;const o=this.languageUtils.getLanguagePartFromCode(e);r=this.getRule(o,t)}return this.pluralRulesCache[i]=r,r}needsPlural(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),(s==null?void 0:s.resolvedOptions().pluralCategories.length)>1}getPluralFormsOfKey(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,s).map(n=>`${t}${n}`)}getSuffixes(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=this.getRule(e,t);return s||(s=this.getRule("dev",t)),s?s.resolvedOptions().pluralCategories.sort((n,i)=>ue[n]-ue[i]).map(n=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${n}`):[]}getSuffix(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};const n=this.getRule(e,s);return n?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${n.select(t)}`:(this.logger.warn(`no plural rule found for: ${e}`),this.getSuffix("dev",t,s))}}const he=function(a,e,t){let s=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",n=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,i=ve(a,e,t);return!i&&n&&g(t)&&(i=q(a,t,s),i===void 0&&(i=q(e,t,s))),i},X=a=>a.replace(/\$/g,"$$$$");class Ne{constructor(){var t;let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=R.create("interpolator"),this.options=e,this.format=((t=e==null?void 0:e.interpolation)==null?void 0:t.format)||(s=>s),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:n,prefix:i,prefixEscaped:r,suffix:l,suffixEscaped:o,formatSeparator:u,unescapeSuffix:h,unescapePrefix:c,nestingPrefix:f,nestingPrefixEscaped:m,nestingSuffix:d,nestingSuffixEscaped:p,nestingOptionsSeparator:y,maxReplaces:b,alwaysFormat:P}=e.interpolation;this.escape=t!==void 0?t:Le,this.escapeValue=s!==void 0?s:!0,this.useRawValueToEscape=n!==void 0?n:!1,this.prefix=i?k(i):r||"{{",this.suffix=l?k(l):o||"}}",this.formatSeparator=u||",",this.unescapePrefix=h?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=f?k(f):m||k("$t("),this.nestingSuffix=d?k(d):p||k(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=b||1e3,this.alwaysFormat=P!==void 0?P:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(t,s)=>(t==null?void 0:t.source)===s?(t.lastIndex=0,t):new RegExp(s,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,s,n){var m;let i,r,l;const o=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=d=>{if(d.indexOf(this.formatSeparator)<0){const P=he(t,o,d,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(P,void 0,s,{...n,...t,interpolationkey:d}):P}const p=d.split(this.formatSeparator),y=p.shift().trim(),b=p.join(this.formatSeparator).trim();return this.format(he(t,o,y,this.options.keySeparator,this.options.ignoreJSONStructure),b,s,{...n,...t,interpolationkey:y})};this.resetRegExp();const h=(n==null?void 0:n.missingInterpolationHandler)||this.options.missingInterpolationHandler,c=((m=n==null?void 0:n.interpolation)==null?void 0:m.skipOnVariables)!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:d=>X(d)},{regex:this.regexp,safeValue:d=>this.escapeValue?X(this.escape(d)):X(d)}].forEach(d=>{for(l=0;i=d.regex.exec(e);){const p=i[1].trim();if(r=u(p),r===void 0)if(typeof h=="function"){const b=h(e,i,n);r=g(b)?b:""}else if(n&&Object.prototype.hasOwnProperty.call(n,p))r="";else if(c){r=i[0];continue}else this.logger.warn(`missed to pass in variable ${p} for interpolating ${e}`),r="";else!g(r)&&!this.useRawValueToEscape&&(r=_(r));const y=d.safeValue(r);if(e=e.replace(i[0],y),c?(d.regex.lastIndex+=r.length,d.regex.lastIndex-=i[0].length):d.regex.lastIndex=0,l++,l>=this.maxReplaces)break}}),e}nest(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n,i,r;const l=(o,u)=>{const h=this.nestingOptionsSeparator;if(o.indexOf(h)<0)return o;const c=o.split(new RegExp(`${h}[ ]*{`));let f=`{${c[1]}`;o=c[0],f=this.interpolate(f,r);const m=f.match(/'/g),d=f.match(/"/g);(((m==null?void 0:m.length)??0)%2===0&&!d||d.length%2!==0)&&(f=f.replace(/'/g,'"'));try{r=JSON.parse(f),u&&(r={...u,...r})}catch(p){return this.logger.warn(`failed parsing options string in nesting for key ${o}`,p),`${o}${h}${f}`}return r.defaultValue&&r.defaultValue.indexOf(this.prefix)>-1&&delete r.defaultValue,o};for(;n=this.nestingRegexp.exec(e);){let o=[];r={...s},r=r.replace&&!g(r.replace)?r.replace:r,r.applyPostProcessor=!1,delete r.defaultValue;let u=!1;if(n[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(n[1])){const h=n[1].split(this.formatSeparator).map(c=>c.trim());n[1]=h.shift(),o=h,u=!0}if(i=t(l.call(this,n[1].trim(),r),r),i&&n[0]===e&&!g(i))return i;g(i)||(i=_(i)),i||(this.logger.warn(`missed to resolve ${n[1]} for nesting ${e}`),i=""),u&&(i=o.reduce((h,c)=>this.format(h,c,s.lng,{...s,interpolationkey:n[1].trim()}),i.trim())),e=e.replace(n[0],i),this.regexp.lastIndex=0}return e}}const ke=a=>{let e=a.toLowerCase().trim();const t={};if(a.indexOf("(")>-1){const s=a.split("(");e=s[0].toLowerCase().trim();const n=s[1].substring(0,s[1].length-1);e==="currency"&&n.indexOf(":")<0?t.currency||(t.currency=n.trim()):e==="relativetime"&&n.indexOf(":")<0?t.range||(t.range=n.trim()):n.split(";").forEach(r=>{if(r){const[l,...o]=r.split(":"),u=o.join(":").trim().replace(/^'+|'+$/g,""),h=l.trim();t[h]||(t[h]=u),u==="false"&&(t[h]=!1),u==="true"&&(t[h]=!0),isNaN(u)||(t[h]=parseInt(u,10))}})}return{formatName:e,formatOptions:t}},I=a=>{const e={};return(t,s,n)=>{let i=n;n&&n.interpolationkey&&n.formatParams&&n.formatParams[n.interpolationkey]&&n[n.interpolationkey]&&(i={...i,[n.interpolationkey]:void 0});const r=s+JSON.stringify(i);let l=e[r];return l||(l=a(B(s),n),e[r]=l),l(t)}};class Ie{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=R.create("formatter"),this.options=e,this.formats={number:I((t,s)=>{const n=new Intl.NumberFormat(t,{...s});return i=>n.format(i)}),currency:I((t,s)=>{const n=new Intl.NumberFormat(t,{...s,style:"currency"});return i=>n.format(i)}),datetime:I((t,s)=>{const n=new Intl.DateTimeFormat(t,{...s});return i=>n.format(i)}),relativetime:I((t,s)=>{const n=new Intl.RelativeTimeFormat(t,{...s});return i=>n.format(i,s.range||"day")}),list:I((t,s)=>{const n=new Intl.ListFormat(t,{...s});return i=>n.format(i)})},this.init(e)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=I(t)}format(e,t,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};const i=t.split(this.formatSeparator);if(i.length>1&&i[0].indexOf("(")>1&&i[0].indexOf(")")<0&&i.find(l=>l.indexOf(")")>-1)){const l=i.findIndex(o=>o.indexOf(")")>-1);i[0]=[i[0],...i.splice(1,l)].join(this.formatSeparator)}return i.reduce((l,o)=>{var c;const{formatName:u,formatOptions:h}=ke(o);if(this.formats[u]){let f=l;try{const m=((c=n==null?void 0:n.formatParams)==null?void 0:c[n.interpolationkey])||{},d=m.locale||m.lng||n.locale||n.lng||s;f=this.formats[u](l,d,{...h,...n,...m})}catch(m){this.logger.warn(m)}return f}else this.logger.warn(`there was no format function for ${u}`);return l},e)}}const Fe=(a,e)=>{a.pending[e]!==void 0&&(delete a.pending[e],a.pendingCount--)};class Ee extends W{constructor(e,t,s){var i,r;let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=n,this.logger=R.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=n.maxParallelReads||10,this.readingCalls=0,this.maxRetries=n.maxRetries>=0?n.maxRetries:5,this.retryTimeout=n.retryTimeout>=1?n.retryTimeout:350,this.state={},this.queue=[],(r=(i=this.backend)==null?void 0:i.init)==null||r.call(i,s,n.backend,n)}queueLoad(e,t,s,n){const i={},r={},l={},o={};return e.forEach(u=>{let h=!0;t.forEach(c=>{const f=`${u}|${c}`;!s.reload&&this.store.hasResourceBundle(u,c)?this.state[f]=2:this.state[f]<0||(this.state[f]===1?r[f]===void 0&&(r[f]=!0):(this.state[f]=1,h=!1,r[f]===void 0&&(r[f]=!0),i[f]===void 0&&(i[f]=!0),o[c]===void 0&&(o[c]=!0)))}),h||(l[u]=!0)}),(Object.keys(i).length||Object.keys(r).length)&&this.queue.push({pending:r,pendingCount:Object.keys(r).length,loaded:{},errors:[],callback:n}),{toLoad:Object.keys(i),pending:Object.keys(r),toLoadLanguages:Object.keys(l),toLoadNamespaces:Object.keys(o)}}loaded(e,t,s){const n=e.split("|"),i=n[0],r=n[1];t&&this.emit("failedLoading",i,r,t),!t&&s&&this.store.addResourceBundle(i,r,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const l={};this.queue.forEach(o=>{Pe(o.loaded,[i],r),Fe(o,e),t&&o.errors.push(t),o.pendingCount===0&&!o.done&&(Object.keys(o.loaded).forEach(u=>{l[u]||(l[u]={});const h=o.loaded[u];h.length&&h.forEach(c=>{l[u][c]===void 0&&(l[u][c]=!0)})}),o.done=!0,o.errors.length?o.callback(o.errors):o.callback())}),this.emit("loaded",l),this.queue=this.queue.filter(o=>!o.done)}read(e,t,s){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,r=arguments.length>5?arguments[5]:void 0;if(!e.length)return r(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:s,tried:n,wait:i,callback:r});return}this.readingCalls++;const l=(u,h)=>{if(this.readingCalls--,this.waitingReads.length>0){const c=this.waitingReads.shift();this.read(c.lng,c.ns,c.fcName,c.tried,c.wait,c.callback)}if(u&&h&&n<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,s,n+1,i*2,r)},i);return}r(u,h)},o=this.backend[s].bind(this.backend);if(o.length===2){try{const u=o(e,t);u&&typeof u.then=="function"?u.then(h=>l(null,h)).catch(l):l(null,u)}catch(u){l(u)}return}return o(e,t,l)}prepareLoading(e,t){let s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},n=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),n&&n();g(e)&&(e=this.languageUtils.toResolveHierarchy(e)),g(t)&&(t=[t]);const i=this.queueLoad(e,t,s,n);if(!i.toLoad.length)return i.pending.length||n(),null;i.toLoad.forEach(r=>{this.loadOne(r)})}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"";const s=e.split("|"),n=s[0],i=s[1];this.read(n,i,"read",void 0,void 0,(r,l)=>{r&&this.logger.warn(`${t}loading namespace ${i} for language ${n} failed`,r),!r&&l&&this.logger.log(`${t}loaded namespace ${i} for language ${n}`,l),this.loaded(e,r,l)})}saveMissing(e,t,s,n,i){var o,u,h,c,f;let r=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},l=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if((u=(o=this.services)==null?void 0:o.utils)!=null&&u.hasLoadedNamespace&&!((c=(h=this.services)==null?void 0:h.utils)!=null&&c.hasLoadedNamespace(t))){this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(s==null||s==="")){if((f=this.backend)!=null&&f.create){const m={...r,isUpdate:i},d=this.backend.create.bind(this.backend);if(d.length<6)try{let p;d.length===5?p=d(e,t,s,n,m):p=d(e,t,s,n),p&&typeof p.then=="function"?p.then(y=>l(null,y)).catch(l):l(null,p)}catch(p){l(p)}else d(e,t,s,n,l,m)}!e||!e[0]||this.store.addResource(e[0],t,s,n)}}}const ce=()=>({debug:!1,initAsync:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:a=>{let e={};if(typeof a[1]=="object"&&(e=a[1]),g(a[1])&&(e.defaultValue=a[1]),g(a[2])&&(e.tDescription=a[2]),typeof a[2]=="object"||typeof a[3]=="object"){const t=a[3]||a[2];Object.keys(t).forEach(s=>{e[s]=t[s]})}return e},interpolation:{escapeValue:!0,format:a=>a,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),de=a=>{var e,t;return g(a.ns)&&(a.ns=[a.ns]),g(a.fallbackLng)&&(a.fallbackLng=[a.fallbackLng]),g(a.fallbackNS)&&(a.fallbackNS=[a.fallbackNS]),((t=(e=a.supportedLngs)==null?void 0:e.indexOf)==null?void 0:t.call(e,"cimode"))<0&&(a.supportedLngs=a.supportedLngs.concat(["cimode"])),typeof a.initImmediate=="boolean"&&(a.initAsync=a.initImmediate),a},Y=()=>{},je=a=>{Object.getOwnPropertyNames(Object.getPrototypeOf(a)).forEach(t=>{typeof a[t]=="function"&&(a[t]=a[t].bind(a))})};class V extends W{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=de(e),this.services={},this.logger=R,this.modules={external:[]},je(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initAsync)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},s=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof t=="function"&&(s=t,t={}),t.defaultNS==null&&t.ns&&(g(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const n=ce();this.options={...n,...this.options,...de(t)},this.options.interpolation={...n.interpolation,...this.options.interpolation},t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);const i=h=>h?typeof h=="function"?new h:h:null;if(!this.options.isClone){this.modules.logger?R.init(i(this.modules.logger),this.options):R.init(null,this.options);let h;this.modules.formatter?h=this.modules.formatter:h=Ie;const c=new le(this.options);this.store=new ie(this.options.resources,this.options);const f=this.services;f.logger=R,f.resourceStore=this.store,f.languageUtils=c,f.pluralResolver=new $e(c,{prepend:this.options.pluralSeparator,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),h&&(!this.options.interpolation.format||this.options.interpolation.format===n.interpolation.format)&&(f.formatter=i(h),f.formatter.init(f,this.options),this.options.interpolation.format=f.formatter.format.bind(f.formatter)),f.interpolator=new Ne(this.options),f.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},f.backendConnector=new Ee(i(this.modules.backend),f.resourceStore,f,this.options),f.backendConnector.on("*",function(m){for(var d=arguments.length,p=new Array(d>1?d-1:0),y=1;y<d;y++)p[y-1]=arguments[y];e.emit(m,...p)}),this.modules.languageDetector&&(f.languageDetector=i(this.modules.languageDetector),f.languageDetector.init&&f.languageDetector.init(f,this.options.detection,this.options)),this.modules.i18nFormat&&(f.i18nFormat=i(this.modules.i18nFormat),f.i18nFormat.init&&f.i18nFormat.init(this)),this.translator=new G(this.services,this.options),this.translator.on("*",function(m){for(var d=arguments.length,p=new Array(d>1?d-1:0),y=1;y<d;y++)p[y-1]=arguments[y];e.emit(m,...p)}),this.modules.external.forEach(m=>{m.init&&m.init(this)})}if(this.format=this.options.interpolation.format,s||(s=Y),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const h=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);h.length>0&&h[0]!=="dev"&&(this.options.lng=h[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(h=>{this[h]=function(){return e.store[h](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(h=>{this[h]=function(){return e.store[h](...arguments),e}});const o=j(),u=()=>{const h=(c,f)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(f),s(c,f)};if(this.languages&&!this.isInitialized)return h(null,this.t.bind(this));this.changeLanguage(this.options.lng,h)};return this.options.resources||!this.options.initAsync?u():setTimeout(u,0),o}loadResources(e){var i,r;let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Y;const n=g(e)?e:this.language;if(typeof e=="function"&&(s=e),!this.options.resources||this.options.partialBundledLanguages){if((n==null?void 0:n.toLowerCase())==="cimode"&&(!this.options.preload||this.options.preload.length===0))return s();const l=[],o=u=>{if(!u||u==="cimode")return;this.services.languageUtils.toResolveHierarchy(u).forEach(c=>{c!=="cimode"&&l.indexOf(c)<0&&l.push(c)})};n?o(n):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(h=>o(h)),(r=(i=this.options.preload)==null?void 0:i.forEach)==null||r.call(i,u=>o(u)),this.services.backendConnector.load(l,this.options.ns,u=>{!u&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),s(u)})}else s(null)}reloadResources(e,t,s){const n=j();return typeof e=="function"&&(s=e,e=void 0),typeof t=="function"&&(s=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),s||(s=Y),this.services.backendConnector.reload(e,t,i=>{n.resolve(),s(i)}),n}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&re.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const s=this.languages[t];if(!(["cimode","dev"].indexOf(s)>-1)&&this.store.hasLanguageSomeTranslations(s)){this.resolvedLanguage=s;break}}}changeLanguage(e,t){var s=this;this.isLanguageChangingTo=e;const n=j();this.emit("languageChanging",e);const i=o=>{this.language=o,this.languages=this.services.languageUtils.toResolveHierarchy(o),this.resolvedLanguage=void 0,this.setResolvedLanguage(o)},r=(o,u)=>{u?(i(u),this.translator.changeLanguage(u),this.isLanguageChangingTo=void 0,this.emit("languageChanged",u),this.logger.log("languageChanged",u)):this.isLanguageChangingTo=void 0,n.resolve(function(){return s.t(...arguments)}),t&&t(o,function(){return s.t(...arguments)})},l=o=>{var h,c;!e&&!o&&this.services.languageDetector&&(o=[]);const u=g(o)?o:this.services.languageUtils.getBestMatchFromCodes(o);u&&(this.language||i(u),this.translator.language||this.translator.changeLanguage(u),(c=(h=this.services.languageDetector)==null?void 0:h.cacheUserLanguage)==null||c.call(h,u)),this.loadResources(u,f=>{r(f,u)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?l(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(e),n}getFixedT(e,t,s){var n=this;const i=function(r,l){let o;if(typeof l!="object"){for(var u=arguments.length,h=new Array(u>2?u-2:0),c=2;c<u;c++)h[c-2]=arguments[c];o=n.options.overloadTranslationOptionHandler([r,l].concat(h))}else o={...l};o.lng=o.lng||i.lng,o.lngs=o.lngs||i.lngs,o.ns=o.ns||i.ns,o.keyPrefix!==""&&(o.keyPrefix=o.keyPrefix||s||i.keyPrefix);const f=n.options.keySeparator||".";let m;return o.keyPrefix&&Array.isArray(r)?m=r.map(d=>`${o.keyPrefix}${f}${d}`):m=o.keyPrefix?`${o.keyPrefix}${f}${r}`:r,n.t(m,o)};return g(e)?i.lng=e:i.lngs=e,i.ns=t,i.keyPrefix=s,i}t(){var n;for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return(n=this.translator)==null?void 0:n.translate(...t)}exists(){var n;for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return(n=this.translator)==null?void 0:n.exists(...t)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],n=this.options?this.options.fallbackLng:!1,i=this.languages[this.languages.length-1];if(s.toLowerCase()==="cimode")return!0;const r=(l,o)=>{const u=this.services.backendConnector.state[`${l}|${o}`];return u===-1||u===0||u===2};if(t.precheck){const l=t.precheck(this,r);if(l!==void 0)return l}return!!(this.hasResourceBundle(s,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||r(s,e)&&(!n||r(i,e)))}loadNamespaces(e,t){const s=j();return this.options.ns?(g(e)&&(e=[e]),e.forEach(n=>{this.options.ns.indexOf(n)<0&&this.options.ns.push(n)}),this.loadResources(n=>{s.resolve(),t&&t(n)}),s):(t&&t(),Promise.resolve())}loadLanguages(e,t){const s=j();g(e)&&(e=[e]);const n=this.options.preload||[],i=e.filter(r=>n.indexOf(r)<0&&this.services.languageUtils.isSupportedCode(r));return i.length?(this.options.preload=n.concat(i),this.loadResources(r=>{s.resolve(),t&&t(r)}),s):(t&&t(),Promise.resolve())}dir(e){var n,i;if(e||(e=this.resolvedLanguage||(((n=this.languages)==null?void 0:n.length)>0?this.languages[0]:this.language)),!e)return"rtl";const t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],s=((i=this.services)==null?void 0:i.languageUtils)||new le(ce());return t.indexOf(s.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new V(e,t)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Y;const s=e.forkResourceStore;s&&delete e.forkResourceStore;const n={...this.options,...e,isClone:!0},i=new V(n);if((e.debug!==void 0||e.prefix!==void 0)&&(i.logger=i.logger.clone(e)),["store","services","language"].forEach(l=>{i[l]=this[l]}),i.services={...this.services},i.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},s){const l=Object.keys(this.store.data).reduce((o,u)=>(o[u]={...this.store.data[u]},Object.keys(o[u]).reduce((h,c)=>(h[c]={...o[u][c]},h),{})),{});i.store=new ie(l,n),i.services.resourceStore=i.store}return i.translator=new G(i.services,n),i.translator.on("*",function(l){for(var o=arguments.length,u=new Array(o>1?o-1:0),h=1;h<o;h++)u[h-1]=arguments[h];i.emit(l,...u)}),i.init(n,t),i.translator.options=n,i.translator.backendConnector.services.utils={hasLoadedNamespace:i.hasLoadedNamespace.bind(i)},i}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const x=V.createInstance();x.createInstance=V.createInstance,x.createInstance,x.dir,x.init,x.loadResources,x.reloadResources,x.use,x.changeLanguage,x.getFixedT,x.t,x.exists,x.setDefaultNamespace,x.hasLoadedNamespace,x.loadNamespaces,x.loadLanguages;const De=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Ve={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"©","&#169;":"©","&reg;":"®","&#174;":"®","&hellip;":"…","&#8230;":"…","&#x2F;":"/","&#47;":"/"},Ke=a=>Ve[a];let ge={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:a=>a.replace(De,Ke)};const Me=(a={})=>{ge={...ge,...a}},Ue={type:"3rdParty",init(a){Me(a.options.react)}},He={translation:{个人中心:"个人中心",退出登录:"退出登录",通知提醒:"通知提醒",是否确定退出登录:"是否确定退出登录",确定:"确定",取消:"取消",退出登录成功:"退出登录成功",有效期:"有效期",版本信息:"版本信息",店铺信息:"店铺信息",切换店铺:"切换店铺",类型:"类型",错误:"错误",信息:"信息",时间:"时间",内容:"内容",运行日志:"运行日志",停止任务:"停止任务",登录提醒:"登录提醒",请先登录您的达秘账号:"请先登录您的达秘账号",去登录:"去登录",跨境:"跨境",本土:"本土","请完整阅读《达秘店铺授权协议》，阅读完成后继续。":"请完整阅读《达秘店铺授权协议》，阅读完成后继续。",我已阅读并同意店铺授权协议:"我已阅读并同意店铺授权协议","输入内容不一致，请检查后重试。":"输入内容不一致，请检查后重试。",请确认选择站点与TK店铺站点一致:"请确认选择站点与TK店铺站点一致","当前登录的店铺与主账号授权的店铺不一致，请联系主账号管理员处理":"当前登录的店铺与主账号授权的店铺不一致，请联系主账号管理员处理",添加成功:"添加成功",获取店铺信息异常:"获取店铺信息异常",请先打开店铺的达人广场页面:"请先打开店铺的达人广场页面",请刷新店铺达人广场界面后重试:"请刷新店铺达人广场界面后重试",提示:"提示",请选择区域:"请选择区域",请选择站点:"请选择站点",请先联系客服开通站点权限:"请先联系客服开通站点权限",网络响应不正常:"网络响应不正常","请先阅读并同意《达秘店铺授权协议》":"请先阅读并同意《达秘店铺授权协议》","请输入【我已阅读并同意店铺授权协议】以确认同意店铺授权协议":"请输入【我已阅读并同意店铺授权协议】以确认同意店铺授权协议",不同意:"不同意",我已阅读并同意:"我已阅读并同意",请选择语言:"请选择语言","检测到店铺/站点不一致，请切换店铺":"检测到店铺/站点不一致，请切换店铺",主要功能:"主要功能",任务记录:"任务记录",基础配置:"基础配置",批量邀约:"批量邀约",批量私信:"批量私信",群发邮件:"群发邮件","极速私信(美区)":"极速私信(美区)",极速私信功能仅限于美区店铺使用:"极速私信功能仅限于美区店铺使用",批量私信订单买家:"批量私信订单买家",清理无效定向合作:"清理无效定向合作",样品达人:"样品达人",邀约记录:"邀约记录",私信记录:"私信记录",极速私信记录:"极速私信记录",邮件发信记录:"邮件发信记录",私信买家记录:"私信买家记录",清理无效计划记录:"清理无效计划记录",审核样品记录:"审核样品记录",邀约模版:"邀约模版",私信模版:"私信模版",授权店铺管理:"授权店铺管理",子账号管理:"子账号管理",黑名单管理:"黑名单管理",至尊版:"至尊版","任务初始化中，请耐心等待...":"任务初始化中，请耐心等待...",发信邮箱配置:"发信邮箱配置",群发邮件模版:"群发邮件模版",补充计划达人:"补充计划达人",邀约计划私信:"邀约计划私信",达秘AI回复:"达秘AI回复",达秘AI回复辅助:"达秘AI回复辅助",总结聊天内容:"总结聊天内容",总结达人聊天界面内容:"总结达人聊天界面内容","获取达人聊天界面消息并总结，有助于理解达人意图后续生成回复文案更精准":"获取达人聊天界面消息并总结，有助于理解达人意图后续生成回复文案更精准",生成回复文案:"生成回复文案","增加部分文案描述，可使AI回复更加精准":"增加部分文案描述，可使AI回复更加精准",话术语言:"话术语言",请选择话术语言:"请选择话术语言",复制:"复制",生成结果:"生成结果",英语:"英语",泰语:"泰语",越南语:"越南语",印尼语:"印尼语",马来西亚语:"马来西亚语",菲律宾语:"菲律宾语",西班牙语:"西班牙语",请选择模式:"请选择模式",店铺模式:"店铺模式",TAP模式:"TAP模式",请选择类型:"请选择类型",美区:"美区",其它:"其它",获取TAP站点:"获取TAP站点",请选择TAP站点:"请选择TAP站点",TAP邀约:"TAP邀约",TAP私信:"TAP私信","您的会员权益不足，无法使用TAP机构模式":"您会员权益不足，无法使用TAP机构模式",欧盟:"欧盟","清除缓存成功，即将自动关闭窗口，请稍后重新打开":"清除缓存成功，即将自动关闭窗口，请稍后重新打开",请先登录:"请先登录",未运行:"未运行",运行中:"运行中",运行结束:"运行结束",启动中:"启动中",登录店铺:"登录店铺",任务状态:"任务状态",站点:"站点",开始时间:"开始时间",任务进度:"任务进度",运行进度:"运行进度",达人邀约:"达人邀约",达人私信:"达人私信",极速私信:"极速私信",清理无效计划:"清理无效计划","样品达人-自动审核":"样品达人-自动审核",旧计划补充达人:"旧计划补充达人",TAP私信:"TAP私信",TAP邀约:"TAP邀约",删除定向商品:"删除定向商品",增加定向商品:"增加定向商品",调整计划日期:"调整计划日期",定向计划分享:"定向计划分享",计划邀约达人数:"计划邀约达人数",成功邀约达人数:"成功邀约达人数",已过滤重复邀约达人数:"已过滤重复邀约达人数",计划私信达人数:"计划私信达人数",成功私信达人数:"成功私信达人数",已过滤重复私信达人数:"已过滤重复私信达人数",计划极速私信人数:"计划极速私信人数",已跳过重复私信达人数:"已跳过重复私信达人数",目标计划数:"目标计划数",已校验计划数:"已校验计划数",取消邀约计划数:"取消邀约计划数",清理无效达人数:"清理无效达人数",已审核样品数:"已审核样品数",已同意数:"已同意数",成功补充达人数:"成功补充达人数",计划TAP私信达人数:"计划TAP私信达人数",已跳过重复私信达人数:"已跳过重复私信达人数",计划TAP邀约达人数:"计划TAP邀约达人数",成功删除商品计划数:"成功删除商品计划数",成功增加商品计划数:"成功增加商品计划数",成功调整日期计划数:"成功调整日期计划数",读取的定向计划数:"读取的定向计划数",成功分享定向计划数:"成功分享定向计划数",关闭任务:"关闭任务"}},ze={translation:{个人中心:"Personal Center",退出登录:"Log out",通知提醒:"Notification reminder",是否确定退出登录:"Are you sure to log out?",确定:"Sure",取消:"Cancel",退出登录成功:"Log out successfully",有效期:"Validity period",版本信息:"Version information",店铺信息:"Shop information",切换店铺:"Switch shop",类型:"type",错误:"mistake",信息:"information",时间:"time",内容:"content",运行日志:"Run log",停止任务:"Stop the task",登录提醒:"Login reminder",请先登录您的达秘账号:"Please log in to your Dali account first",去登录:"Go to log in",跨境:"Cross-border",本土:"Local","请完整阅读《达秘店铺授权协议》，阅读完成后继续。":'Please read the "Dami Store Authorization Agreement" in full and continue after reading.',我已阅读并同意店铺授权协议:"I have read and agreed to the store authorization agreement","输入内容不一致，请检查后重试。":"The input content is inconsistent, please check and try again.",请确认选择站点与TK店铺站点一致:"Please make sure that the site is selected and the TK store site is consistent","当前登录的店铺与主账号授权的店铺不一致，请联系主账号管理员处理":"The currently logged in store is inconsistent with the store authorized by the main account. Please contact the main account administrator to handle it.",添加成功:"Added successfully",获取店铺信息异常:"Abnormal access to store information",请先打开店铺的达人广场页面:"Please open the store interface first",请刷新店铺达人广场界面后重试:"Please refresh the store interface and try again",提示:"hint",请选择区域:"Please select a region",请选择站点:"Please select a site",请先联系客服开通站点权限:"Please contact customer service first to enable site permissions",网络响应不正常:"The network response is not normal","请先阅读并同意《达秘店铺授权协议》":'Please read and agree to the "Dami Store Authorization Agreement" first',"请输入【我已阅读并同意店铺授权协议】以确认同意店铺授权协议":"Please enter [I have read and agreed to the store authorization agreement] to confirm that I agree to the store authorization agreement",不同意:"disagree",我已阅读并同意:"I have read and agree",请选择语言:"Please select a language","检测到店铺/站点不一致，请切换店铺":"The store/site is inconsistent, please switch the store",主要功能:"Main functions",任务记录:"Task Record",基础配置:"Basic configuration",批量邀约:"Batch invitation",批量私信:"Batch message",群发邮件:"Mass email","极速私信(美区)":"Fast message (US)",极速私信功能仅限于美区店铺使用:"The fast private message function is only available in US stores",批量私信订单买家:"Batch message buyers",清理无效定向合作:"Clean invalid plan",样品达人:"Sample expert",邀约记录:"Invitation record",私信记录:"Private message record",极速私信记录:"Speedy private message record",邮件发信记录:"Email message history",私信买家记录:"Private message buyer record",清理无效计划记录:"Clean up invalid plan records",审核样品记录:"Review sample records",邀约模版:"Invitation template",私信模版:"Private message template",授权店铺管理:"Authorized store management",子账号管理:"Sub-account management",黑名单管理:"Blacklist Management",至尊版:"Supreme Edition","任务初始化中，请耐心等待...":"The task is being initialized, please wait patiently...",发信邮箱配置:"Send email configuration",群发邮件模版:"Mass email template",补充计划达人:"Supple plan expert",邀约计划私信:"Invitation plan chat",达秘AI回复:"Dami AI Reply",达秘AI回复辅助:"Dami AI Reply Assistant",总结聊天内容:"Summary of the chat content",总结达人聊天界面内容:"Summary of the content of the expert chat interface","获取达人聊天界面消息并总结，有助于理解达人意图后续生成回复文案更精准":"Get information and summary of the expert chat interface, which helps to understand the experts intentions and generate replies more accurately.",生成回复文案:"Generate a reply document","增加部分文案描述，可使AI回复更加精准":"Adding some copy descriptions can make AI reply more accurate",话术语言:"Speech language",请选择话术语言:"Please select the language",复制:"copy",生成结果:"Generate results",英语:"English",泰语:"Thai",越南语:"Vietnamese",印尼语:"Indonesian",马来西亚语:"Malaysian",菲律宾语:"Filipino",西班牙语:"Spanish",请选择模式:"Please select the mode",店铺模式:"Store mode",TAP模式:"Tap mode",请选择类型:"Please select a type",美区:"American District",其它:"other",获取TAP站点:"Get tap site",请选择TAP站点:"Please select the tap site",TAP邀约:"Tap Invitation",TAP私信:"Tap private message","您的会员权益不足，无法使用TAP机构模式":"Your membership rights are insufficient and you cannot use the TAP organization model",欧盟:"European Union","清除缓存成功，即将重载插件":"Clear cache successfully, reloading the plugin",未运行:"Not running",运行中:"Running",运行结束:"Running ends",启动中:"Starting",登录店铺:"Log in to the store",任务状态:"Task status",站点:"Site",开始时间:"Start time",任务进度:"Task progress",运行进度:"Operation progress",达人邀约:"Creator Invitation",达人私信:"Creator Private Message",极速私信:"Speedy Private Message",清理无效计划:"Clean Invalid Plan","样品达人-自动审核":"Sample Creator - Auto Review",旧计划补充达人:"Old Plan Supplement Creator",TAP私信:"TAP Private Message",TAP邀约:"TAP Invitation",删除定向商品:"Delete Targeted Product",增加定向商品:"Add Targeted Product",调整计划日期:"Adjust Plan Date",定向计划分享:"Share Targeted Plan",计划邀约达人数:"Planned Invited Creators",成功邀约达人数:"Successfully Invited Creators",已过滤重复邀约达人数:"Filtered Duplicate Invited Creators",计划私信达人数:"Planned Private Messaged Creators",成功私信达人数:"Successfully Private Messaged Creators",已过滤重复私信达人数:"Filtered Duplicate Private Messaged Creators",计划极速私信人数:"Planned Speedy Private Message Count",已跳过重复私信达人数:"Skipped Duplicate Private Messaged Creators",目标计划数:"Target Plan Count",已校验计划数:"Verified Plan Count",取消邀约计划数:"Cancelled Invitation Plan Count",清理无效达人数:"Cleaned Invalid Creators",已审核样品数:"Reviewed Sample Count",已同意数:"Agreed Count",成功补充达人数:"Successfully Supplemented Creators",计划TAP私信达人数:"Planned TAP Private Messaged Creators",已跳过重复私信达人数:"Skipped Duplicate TAP Private Messaged Creators",计划TAP邀约达人数:"Planned TAP Invited Creators",成功删除商品计划数:"Successfully Deleted Product Plan Count",成功增加商品计划数:"Successfully Added Product Plan Count",成功调整日期计划数:"Successfully Adjusted Date Plan Count",读取的定向计划数:"Read Targeted Plan Count",成功分享定向计划数:"Successfully Shared Targeted Plan Count","清除缓存成功，即将自动关闭窗口，请稍后重新打开":"Clear cache successfully, reloading the plugin",请先登录:"Please log in first",关闭任务:"Close Task"}};x.use(Ue).init({resources:{en:ze,zh:He},fallbackLng:"zh",interpolation:{escapeValue:!1}});function We(){}function Q(a,...e){}const Be={debug:(...a)=>Q(console.debug,...a),log:(...a)=>Q(console.log,...a),warn:(...a)=>Q(console.warn,...a),error:(...a)=>Q(console.error,...a)};return(async()=>{try{return await x.main()}catch(a){throw Be.error('The unlisted script "i18n" crashed on startup!',a),a}})()}();
i18n;
