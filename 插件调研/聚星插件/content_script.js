// 注入inject.js文件的内容
var script = document.createElement('script')
script.src = chrome.runtime.getURL('injected.js')
script.onload = function () {
  this.remove()
}
;(document.head || document.documentElement).appendChild(script)
// 使用Worker编写setTimeout使setTimeout准时执行
const blobURL = URL.createObjectURL(
  new Blob(
    [
      '(',

      function () {
        const intervalIds = {}
        const timeoutIds = {}

        // 监听message 开始执行定时器或者销毁
        self.onmessage = function onMsgFunc(e) {
          switch (e.data.command) {
            case 'interval:start': {
              // 开启定时器
              const intervalId = setInterval(function () {
                postMessage({
                  message: 'interval:tick',
                  id: e.data.id
                })
              }, e.data.interval)

              postMessage({
                message: 'interval:started',
                id: e.data.id
              })

              intervalIds[e.data.id] = intervalId
              break
            }
            case 'interval:clear': // 销毁
              clearInterval(intervalIds[e.data.id])

              postMessage({
                message: 'interval:cleared',
                id: e.data.id
              })

              delete intervalIds[e.data.id]
              break
            case 'timeout:start': {
              // 开启定时器
              const timeoutId = setTimeout(function () {
                postMessage({
                  message: 'timeout:tick',
                  id: e.data.id
                })
              }, e.data.timeout)

              postMessage({
                message: 'timeout:started',
                id: e.data.id
              })

              timeoutIds[e.data.id] = timeoutId
              break
            }
            case 'timeout:clear': // 销毁
              clearTimeout(timeoutIds[e.data.id])

              postMessage({
                message: 'timeout:cleared',
                id: e.data.id
              })

              delete timeoutIds[e.data.id]
              break
          }
        }
      }.toString(),

      ')()'
    ],
    { type: 'application/javascript' }
  )
)

const worker = new Worker(blobURL)

URL.revokeObjectURL(blobURL)

const workerTimer = {
  id: 0,
  callbacks: {},
  setInterval: function (cb, interval, context) {
    this.id++
    const id = this.id
    this.callbacks[id] = { fn: cb, context: context }
    worker.postMessage({
      command: 'interval:start',
      interval: interval,
      id: id
    })
    return id
  },
  setTimeout: function (cb, timeout, context) {
    this.id++
    const id = this.id
    this.callbacks[id] = { fn: cb, context: context }
    worker.postMessage({ command: 'timeout:start', timeout: timeout, id: id })
    return id
  },

  // 监听worker 里面的定时器发送的message 然后执行回调函数
  onMessage: function (e) {
    switch (e.data.message) {
      case 'interval:tick':
      case 'timeout:tick': {
        const callbackItem = this.callbacks[e.data.id]
        if (callbackItem && callbackItem.fn)
          callbackItem.fn.apply(callbackItem.context)
        break
      }

      case 'interval:cleared':
      case 'timeout:cleared':
        delete this.callbacks[e.data.id]
        break
    }
  },

  // 往worker里面发送销毁指令
  clearInterval: function (id) {
    worker.postMessage({ command: 'interval:clear', id: id })
  },
  clearTimeout: function (id) {
    worker.postMessage({ command: 'timeout:clear', id: id })
  }
}

worker.onmessage = workerTimer.onMessage.bind(workerTimer)

/* global chrome */
// 公共变量
let controller = new AbortController() // stop事件
const operateGap = 1000 // 操作间隔
// eslint-disable-next-line no-unused-vars
let shopId = ''
// eslint-disable-next-line no-unused-vars
let sendInviteStatus = false // 记录是否发送成功，当前没有使用这个变量判断
let shopRegion = 'US'
let tokenQuery = ''
// 分别记录邀约和私信的运行状态，只私信或者邀约页面起作用
let inviteRunning = true
let letterRunning = true
let manageRunning = true
// 加载letter页面 letterPageLoading
let letterPageLoading = false

// 全局监听
// eslint-disable-next-line no-unused-vars

window.addEventListener('beforeunload', () => {
  // 向背景脚本发送请求，以获取标签页 ID
  chrome.runtime.sendMessage({ action: 'getTabId' }, function (response) {
    console.log('Current Tab ID:', response.tabId)
    chrome.runtime.sendMessage(
      {
        action: 'getTabRunning',
        mainTabId: response.tabId
      },
      function (res) {
        const { windowsTabRunning } = res
        if (windowsTabRunning)
          chrome.runtime.sendMessage({
            action: 'updateStoreValue',
            mainTabId: response.tabId,
            keyWord: 'beforeUrl',
            value: window.location.pathname
          })
      }
    )
  })
})
// 页面刷新就停止运行了逻辑
window.addEventListener('load', () => {
  // 向背景脚本发送请求，以获取标签页 ID
  chrome.runtime.sendMessage({ action: 'getTabId' }, function (tabResponse) {
    console.log('Current Tab ID:', tabResponse.tabId)
    chrome.runtime.sendMessage(
      {
        action: 'getStoreValue',
        mainTabId: tabResponse.tabId,
        keyWord: 'beforeUrl'
      },
      function (storeResponse) {
        const { storeValue } = storeResponse
        console.log('storeValue===', storeValue)
        if (storeValue) {
          chrome.runtime.sendMessage({
            action: 'stopTask',
            mainTabId: tabResponse.tabId
          })
          chrome.runtime.sendMessage({
            action: 'updateStoreValue',
            mainTabId: tabResponse.tabId,
            keyWord: 'beforeUrl',
            value: null
          })
        }
      }
    )
  })
})

// 获取shopId
window.addEventListener('load', () => {
  const tabUrl = window.location.origin
  const queryList = getQueryParams(window.location.href)
  shopRegion = queryList['shop_region'] || 'US'
  // 获取shopId
  getRequest(
    `${tabUrl}/api/v1/affiliate/account/info?account_type=1&shop_region=${shopRegion}`
  ).then(json => {
    if (json.shop_info && json.shop_info.length) {
      shopId = json.shop_info[0].shop_id
    } else {
      shopId = json['binding_seller_id']
    }
  })
})

// ---------------------------------复选框---------------------------------------
async function pageSelectedValueTip() {
  const storeValue = await getStoreSignalVar('hasSelectCheckout')

  const selectTip = document.querySelector('#selectTip')
  if (selectTip) {
    const noxCreatorName = document.getElementById('noxCreatorName')
    noxCreatorName.innerText = storeValue ? `${storeValue}` : '未选择'
  } else {
    const tipContainer = document.createElement('div')
    tipContainer.id = 'selectTip'
    tipContainer.style.marginTop = '5px'
    tipContainer.style.marginBottom = '5px'
    tipContainer.style.color = '#323b4b'
    tipContainer.style.backgroundColor = 'rgba(0, 0, 0, .05)'
    tipContainer.style.padding = '5px'
    tipContainer.style.borderRadius = '5px'
    tipContainer.style.fontSize = '14px'

    const headerContainer = document.createElement('div')
    headerContainer.style.display = 'flex'
    headerContainer.style.justifyContent = 'space-between'
    const textTip = document.createElement('div')
    textTip.innerText = '页面已选网红：'
    const sliderBtn = document.createElement('div')
    sliderBtn.innerText = '收起'
    sliderBtn.style.cursor = 'pointer'
    sliderBtn.addEventListener('click', e => {
      if (e.target.textContent === '收起') {
        const noxCreatorName = document.getElementById('noxCreatorName')
        noxCreatorName && (noxCreatorName.style.height = 0)
        e.target.innerText = '展开'
      } else if (e.target.textContent === '展开') {
        const noxCreatorName = document.getElementById('noxCreatorName')
        noxCreatorName && (noxCreatorName.style.height = 'unset')
        e.target.innerText = '收起'
      }
    })
    headerContainer.appendChild(textTip)
    headerContainer.appendChild(sliderBtn)

    const creatorName = document.createElement('div')
    creatorName.id = 'noxCreatorName'
    creatorName.innerText = storeValue ? `${storeValue}` : '未选择'
    creatorName.style.overflow = `hidden`

    tipContainer.appendChild(headerContainer)
    tipContainer.appendChild(creatorName)

    // 插入
    let noxBtnContainer = null
    let pageTimer = 0
    await new Promise((resolve, reject) => {
      let getTableIconTimers = 0
      pageTimer = workerTimer.setInterval(async () => {
        noxBtnContainer = document.querySelector('#noxBtnContainer')
        if (noxBtnContainer) {
          workerTimer.clearInterval(pageTimer)
          resolve('success')
        }
        if (getTableIconTimers >= 20) {
          workerTimer.clearInterval(pageTimer)
          reject('no found noxBtnContainer') // 抛出异常
        }
        getTableIconTimers++
      }, 1000)
    })
    if (noxBtnContainer) {
      noxBtnContainer.insertAdjacentElement('afterend', tipContainer)
    }
  }
}

async function getTableList() {
  let trList = []
  let pageTimer = 0
  try {
    // 获取网红列表
    await new Promise((resolve, reject) => {
      let getTableIconTimers = 0
      pageTimer = workerTimer.setInterval(async () => {
        // 获取页面的header
        trList = document.querySelector('.arco-table-content-inner')
          ? document
              .querySelector('.arco-table-content-inner')
              .querySelectorAll('tr')
          : null
        if (trList && trList.length) {
          workerTimer.clearInterval(pageTimer)
          resolve('success')
        }
        if (getTableIconTimers >= 20) {
          workerTimer.clearInterval(pageTimer)
          reject('no found tableIcon') // 抛出异常
        }
        getTableIconTimers++
      }, 1000)
    })
    return trList
  } catch (e) {
    workerTimer.clearInterval(pageTimer)
  }
}

async function initCheckout(callback) {
  const currentPathName = window.location.pathname
  const allowCheckoutPathList = ['/connection/creator']
  if (!allowCheckoutPathList.includes(currentPathName)) return
  console.log('初始化复选框啦啦啦====', currentPathName)
  // 非运行状态才渲染
  const trList = await getTableList()
  try {
    // 添加复选框，页面元素插入
    const hadSelectedNameList = await getStoreSignalVar('hasSelectCheckout')
    for (let i = 0; i < trList.length; i++) {
      const trCreatorName = trList[i].querySelector(
        '.text-body-m-medium.text-neutral-text1.text-overflow-single'
      )
        ? trList[i].querySelector(
            '.text-body-m-medium.text-neutral-text1.text-overflow-single'
          ).textContent
        : null

      if (!trCreatorName) continue
      // 复选框
      const checkbox = document.createElement('input')
      checkbox.type = 'checkbox'
      checkbox.id = `dynamicCheckbox${i}`
      checkbox.style.width = `18px`
      checkbox.style.height = `18px`
      checkbox.style.accentColor = '#fa6300'
      checkbox.style.cursor = 'pointer'
      // 初始化选中状态
      if (hadSelectedNameList && hadSelectedNameList.includes(trCreatorName))
        checkbox.checked = true
      // 点击逻辑
      checkbox.addEventListener('click', async e => {
        e.stopPropagation()
        e.target.disabled = true
        const currentCreator =
          e.target.parentNode.nextSibling.nextSibling.querySelector(
            '.text-body-m-medium.text-neutral-text1.text-overflow-single'
          ).textContent
        const storeValue = await getStoreSignalVar('hasSelectCheckout')
        console.log('storeValue===', storeValue)
        // 不允许直接以数组格式存储，使用string方式存储
        if (e.target.checked) {
          await setStoreSignalVar(
            'hasSelectCheckout',
            storeValue
              ? `${storeValue}${currentCreator},`
              : `${currentCreator},`
          )
          e.target.disabled = false
        } else {
          const startIndex = storeValue.indexOf(currentCreator)
          if (!(startIndex < 0)) {
            const filterValue =
              storeValue.substring(0, startIndex) +
              storeValue.substring(startIndex + currentCreator.length + 1)
            console.log('删除后的值', filterValue)
            await setStoreSignalVar('hasSelectCheckout', filterValue)
            e.target.disabled = false
          }
        }
        pageSelectedValueTip()
      })

      // 添加外层容器
      const inputContainer = document.createElement('div')
      inputContainer.style.marginRight = '8px'
      inputContainer.style.height = '56px'
      inputContainer.style.display = 'flex'
      inputContainer.style.alignItems = 'center'
      inputContainer.style.justifyContent = 'start'
      inputContainer.style.cursor = 'auto'
      inputContainer.addEventListener('click', e => {
        e.stopPropagation()
      })
      inputContainer.appendChild(checkbox)

      // 插入元素
      const avatarContainer = trList[i].querySelector(
        '.flex.flex-row.items-start.text-overflow-single'
      )
      // 获取所有子元素
      const avatarChildren = Array.from(avatarContainer.children)
      // 如果子元素数量大于2，则移除除了最后两个子元素之外的所有子元素
      if (avatarChildren.length > 2) {
        avatarChildren.slice(0, -2).forEach(child => {
          avatarContainer.removeChild(child)
        })
      }
      avatarContainer &&
        avatarContainer.insertBefore(
          inputContainer,
          avatarContainer.childNodes[0]
        )
    }
    // 插入页面已选tip
    pageSelectedValueTip()
    if (callback) callback()
  } catch (e) {
    console.log('e==', e)
  }
}
const initCheckoutClearBtn = async () => {
  const currentPathName = window.location.pathname
  const allowCheckoutPathList = ['/connection/creator']
  if (!allowCheckoutPathList.includes(currentPathName)) return

  let pageTimer = null
  let tableContainer = document.querySelector('.arco-table-content-inner')
  // 获取header
  await new Promise((resolve, reject) => {
    let getHeaderTimers = 0
    pageTimer = workerTimer.setInterval(async () => {
      // 获取页面的header
      tableContainer = document.querySelector('.arco-table-content-inner')
      if (tableContainer) {
        workerTimer.clearInterval(pageTimer)
        resolve('success')
      }
      if (getHeaderTimers >= 20) {
        workerTimer.clearInterval(pageTimer)
        reject('no found tableHeader') // 抛出异常
      }
      getHeaderTimers++
    }, 1000)
  })
  const tableHeader = tableContainer.querySelector('.arco-table-header')
  // 添加清除按钮
  const clearBtn = document.createElement('div')
  clearBtn.textContent = '清除所有'
  clearBtn.style.padding = '5px 10px'
  clearBtn.style.border = '1px solid #fa6300'
  clearBtn.style.borderRadius = '5px'
  clearBtn.style.fontSize = '14px'
  clearBtn.style.marginTop = '5px'
  clearBtn.style.cursor = 'pointer'
  clearBtn.style.display = 'inline-block'
  clearBtn.style.background = '#fa6300'
  clearBtn.style.color = '#ffffff'
  clearBtn.style.fontWeight = '500'
  clearBtn.addEventListener('mouseenter', e => {
    e.target.style.opacity = 0.8
  })
  clearBtn.addEventListener('mouseout', e => {
    e.target.style.opacity = 1
  })
  clearBtn.addEventListener('click', async e => {
    e.target.style.cursor = 'not-allowed'
    e.target.style.opacity = 0.5
    // 清空
    await setStoreSignalVar('hasSelectCheckout', null)
    initCheckout(() => {
      e.target.style.opacity = 1
      e.target.style.cursor = 'pointer'
    })
  })

  // 添加选择全部按钮
  const selectAllBtn = document.createElement('div')
  selectAllBtn.textContent = '选择当前页面已加载网红'
  selectAllBtn.style.padding = '5px 10px'
  selectAllBtn.style.border = '1px solid #fa6300'
  selectAllBtn.style.borderRadius = '5px'
  selectAllBtn.style.fontSize = '14px'
  selectAllBtn.style.marginTop = '5px'
  selectAllBtn.style.marginBottom = '5px'
  selectAllBtn.style.marginLeft = '10px'
  selectAllBtn.style.cursor = 'pointer'
  selectAllBtn.style.display = 'inline-block'
  selectAllBtn.style.background = '#fa6300'
  selectAllBtn.style.color = '#ffffff'
  selectAllBtn.style.fontWeight = '500'
  selectAllBtn.addEventListener('mouseenter', e => {
    e.target.style.opacity = 0.8
  })
  selectAllBtn.addEventListener('mouseout', e => {
    e.target.style.opacity = 1
  })
  selectAllBtn.addEventListener('click', async e => {
    e.target.style.cursor = 'not-allowed'
    e.target.style.opacity = 0.5
    // 获取当前页面已加载的所有网红
    const trList = await getTableList()
    const trCreatorNameList = []
    for (let i = 0; i < trList.length; i++) {
      const trCreatorName = trList[i].querySelector(
        '.text-body-m-medium.text-neutral-text1.text-overflow-single'
      )
        ? trList[i].querySelector(
            '.text-body-m-medium.text-neutral-text1.text-overflow-single'
          ).textContent
        : null
      if (trCreatorName) {
        trCreatorNameList.push(trCreatorName)
      }
    }
    // 存储网红
    const storeValue = await getStoreSignalVar('hasSelectCheckout')
    // 去重
    const selectedCreatorName = storeValue
      ? storeValue.split(',').filter(item => item)
      : []
    let targetCreatorName = [...trCreatorNameList, ...selectedCreatorName]
    targetCreatorName = [...new Set(targetCreatorName)]

    await setStoreSignalVar('hasSelectCheckout', targetCreatorName.join(','))
    initCheckout(() => {
      e.target.style.opacity = 1
      e.target.style.cursor = 'pointer'
    })
  })

  const noxBtnContainer = document.createElement('div')
  noxBtnContainer.id = 'noxBtnContainer'
  noxBtnContainer.append(clearBtn)
  noxBtnContainer.append(selectAllBtn)

  // 插入
  const noxBtnContainerPage = tableHeader.querySelector('#noxBtnContainer')
  if (noxBtnContainerPage) {
    tableHeader.remove(noxBtnContainerPage)
  }
  tableHeader.insertBefore(noxBtnContainer, tableHeader.childNodes[0])
}

// 消息监听
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // 注意： 个个消息事件在各自的处理模块中处理并调用sendResponse返回结果
  const { action, params, backgroundParams } = message
  // tabId && (currentTabId = tabId)
  if (action === 'selectEnglish') {
    selectEnglish(controller.signal, sendResponse)
  } else if (action === 'fillInFilterLabel') {
    const { filtersParamsLabel } = message
    fillInFilterLabel(controller.signal, filtersParamsLabel, sendResponse)
  } else if (action === 'stopTask') {
    stopRunningTask(sendResponse)
  } else if (action === 'getCreatorList') {
    if (message.shopRegion) shopRegion = message.shopRegion
    if (message.shopId) shopId = message.shopId
    getCreatorList(
      params,
      message.mainTabId,
      backgroundParams,
      controller.signal
    )
  } else if (action === 'newInviteTabToInvite') {
    // 页面加载完再执行
    if (document.readyState === 'complete') {
      newTabToInvite(params, backgroundParams, controller.signal, sendResponse)
    } else {
      window.onload = function () {
        newTabToInvite(
          params,
          backgroundParams,
          controller.signal,
          sendResponse
        )
      }
    }
  } else if (action === 'newLetterTabToInvite') {
    // 页面加载完再执行
    if (document.readyState === 'complete') {
      newTabToLetter(params, backgroundParams, controller.signal, sendResponse)
    } else {
      window.onload = function () {
        newTabToLetter(
          params,
          backgroundParams,
          controller.signal,
          sendResponse
        )
      }
    }
  } else if (action === 'searchGetCreatorList') {
    searchGetCreatorList(params, controller.signal, sendResponse)
  } else if (action === 'letterTabNoCreators') {
    // 为了适配如果某一个网红没抓取到不影响下一个网红的私信
    chrome.runtime.sendMessage({
      domainUrl: window.location.hostname,
      action: 'newTabToLetterResult',
      result: {
        code: 201,
        message: 'letter continuous'
      },
      backgroundParams: backgroundParams
    })
  } else if (action === 'addCreatorCheckLoading') {
    const { creatorItemName } = message
    addCreatorCheckLoading(creatorItemName)
  } else if (action === 'addProcessLoadingTest') {
    const { string } = message
    addProcessLoadingTest(string)
  } else if (action === 'clearCreatorLoadingContainer') {
    clearCreatorLoadingContainer()
  } else if (action === 'fillInManageFilterLabel') {
    // manage相关操作
    const { filtersLabel } = message
    fillInManageFilterLabel(controller.signal, filtersLabel, sendResponse)
  } else if (action === 'getManageCreatorList') {
    if (message.shopRegion) shopRegion = message.shopRegion
    if (message.shopId) shopId = message.shopId
    getManageCreatorList(
      params,
      message.mainTabId,
      backgroundParams,
      controller.signal
    )
  } else if (action === 'newTabToManageLetter') {
    // 页面加载完再执行
    if (document.readyState === 'complete') {
      newTabToManageLetter(
        params,
        backgroundParams,
        controller.signal,
        sendResponse
      )
    } else {
      window.onload = function () {
        newTabToManageLetter(
          params,
          backgroundParams,
          controller.signal,
          sendResponse
        )
      }
    }
  }
  // 返回 true 来保持消息监听端口打开
  return true
})
// --------------------------action直接调用的方法-------------------------
// 开始执行
async function selectEnglish(signal, sendResponse) {
  // 确定语言，选择英语
  await selectLanguage(signal, 'US English', 'English')
    .then(() => {
      sendResponse({ code: 200, message: 'select language success' })
    })
    .catch(e => {
      console.log('select language error', e)
      sendResponse({
        code: e.toString().includes('Task close') ? 505 : 500,
        message: e.toString()
      })
    })
}

// 填充页面筛选项
async function fillInFilterLabel(signal, filtersParamsLabel, sendResponse) {
  console.log('filtersParamsLabel', filtersParamsLabel)
  const queryParamsLabel = filtersParamsLabel.query
    ? filtersParamsLabel.query.value
    : ''
  if (queryParamsLabel) {
    const input = document
      .querySelectorAll(
        '.arco-input-group-wrapper.arco-input-group-wrapper-default.arco-input-has-suffix.arco-input-search.m4b-input-search'
      )[0]
      .querySelector('input')
    await simulateInput(signal, 'number', input, queryParamsLabel)
  }

  delete filtersParamsLabel.query
  const filterContainer = document.querySelectorAll(
    '.arco-spin.m4b-loading.sc-gEvEer.bfnzTC.w-full.h-full'
  )[0]

  const filterLabelDiv = document.createElement('div')
  filterLabelDiv.style.display = 'flex'
  filterLabelDiv.style.flexWrap = 'wrap'

  const filterSelectSpan = document.createElement('div')
  filterSelectSpan.innerHTML = 'Selected:  '
  filterSelectSpan.style.marginRight = '5px'
  filterSelectSpan.style.marginTop = '5px'
  filterLabelDiv.appendChild(filterSelectSpan)

  Object.keys(filtersParamsLabel).forEach(item => {
    const filterItem = filtersParamsLabel[item]
    const filterItemDiv = document.createElement('div')
    filterItemDiv.innerHTML = `${filterItem.label}${filterItem.value}`
    filterItemDiv.style.padding = '2px 5px'
    // filterItemDiv.style.border = '1px solid #fa6300'
    filterItemDiv.style.borderRadius = '5px'
    filterItemDiv.style.fontSize = '14px'
    filterItemDiv.style.marginRight = '5px'
    filterItemDiv.style.marginTop = '5px'
    filterItemDiv.style.color = '#323b4b'
    filterItemDiv.style.backgroundColor = 'rgba(0, 0, 0, .05)'
    filterLabelDiv.appendChild(filterItemDiv)
  })

  filterLabelDiv.style.marginTop = '10px'

  filterContainer.appendChild(filterLabelDiv)

  sendResponse({ code: 200, message: 'select language success' })

  signal.addEventListener('abort', async () => {
    inviteRunning = false
    letterRunning = false
    // 恢复原样
    sendResponse({
      code: 505,
      message: 'Task close'
    })
  })
}

// 停止执行
function stopRunningTask(sendResponse) {
  try {
    controller.abort()
    sendResponse({ code: 200, message: 'Task cancelled' })
    // 更新controller
    controller = new AbortController()
  } catch (e) {
    sendResponse({ code: 500, message: e })
  }
}
// 获取页面上创建者的列表
async function getCreatorList(params, mainTabId, backgroundParams, signal) {
  const {
    num = 3,
    blackCreator = [],
    filters = null,
    currentPage = 0,
    offset = 0,
    page = '',
    productIdList = [],
    skipHasReplied = false,
    reqGap = 2
  } = params
  const creatorListInfo = []
  const otherParams = {
    currentPage: currentPage || 0,
    offset: offset || 0,
    getAllData: false,
    firstPage: deepClone(currentPage || 0),
    firstOffset: deepClone(offset || 0),
    page,
    productIdList,
    mainTabId,
    skipHasReplied,
    reqGap
  }

  await getEnoughCreator(
    signal,
    num,
    blackCreator,
    creatorListInfo,
    filters,
    otherParams
  ).catch(e => {
    console.log('get creatorList error', e.toString())
    chrome.runtime.sendMessage({
      action: 'getCreatorListResult',
      result: {
        code: e.toString().includes('Task close') ? 505 : 500,
        message: `${e.toString()} - get creator error`
      },
      backgroundParams: backgroundParams
    })
  })
  addProcessLoadingTest('成功获取页面数据-c')
  workerTimer.setTimeout(() => {
    chrome.runtime.sendMessage({
      action: 'getCreatorListResult',
      result: {
        code: 200,
        message: 'get creator success',
        data: {
          creatorListInfo: creatorListInfo,
          currentPage: otherParams.currentPage,
          offset: otherParams.offset || 0,
          getAllData: otherParams.getAllData
        }
      },
      backgroundParams: backgroundParams
    })
  }, 2000)
}

async function isInviteEnglish(signal) {
  console.log('判断是否是英语')
  const containerList = document.querySelectorAll(
    '.arco-collapse-item-header.arco-collapse-item-header-right'
  )
  const isEnglish =
    containerList &&
    containerList.length &&
    containerList[0].textContent.includes('invitation')
  if (!isEnglish) {
    await selectLanguage(signal, 'US English', 'English')
  }
}

// 新页面执行邀请操作
function newTabToInvite(params, backgroundParams, signal, sendResponse) {
  const { invite } = params
  // 填写表单
  let intervalTimer = 0
  // 查找creatorInput，查找到creatorInput说明页面加载完成继续后续操作
  intervalTimer = workerTimer.setInterval(async () => {
    try {
      const creatorInput = document.querySelector(
        '.arco-input.arco-input-size-default'
      )
      if (creatorInput) {
        workerTimer.clearInterval(intervalTimer)
        let {
          products = [],
          sample = { able: false, type: '' },
          conflict = 2
        } = invite
        // 填写invite基本信息
        // await getOuterContainerList(signal, 'Create invitation', sendResponse)
        // await getOuterContainerList(signal, 'Complete invitation', sendResponse)
        inviteRunning &&
          (await fillInInvitationInfo(signal, invite, sendResponse))

        // 填写产品信息
        inviteRunning &&
          (await getOuterContainerList(signal, 'Choose products', sendResponse))
        inviteRunning &&
          (await getOuterContainerList(signal, 'Select products', sendResponse))
        inviteRunning && (await fillInProduct(signal, products, sendResponse))

        // 填写是否邮寄样例
        inviteRunning &&
          (await getOuterContainerList(
            signal,
            'Set up free samples',
            sendResponse
          ))
        inviteRunning &&
          (await getOuterContainerList(
            signal,
            'Set up free sample',
            sendResponse
          ))
        inviteRunning && (await fillInFreeSamples(signal, sample, sendResponse))

        // 填写网红
        inviteRunning &&
          (await getOuterContainerList(signal, 'Choose creators', sendResponse))
        inviteRunning &&
          (await getOuterContainerList(signal, 'Select creators', sendResponse))
        inviteRunning &&
          (await fillInCreatorList(
            signal,
            params,
            backgroundParams,
            sendResponse
          ))
        // 清一下信息
        addProcessLoadingTest(``)
        // sleep 2s
        await sleepFun(2000)
        // 点击邀约按钮
        inviteRunning && (await sendInvite(signal, conflict))

        workerTimer.setTimeout(async () => {
          // 判断是否邀请成功
          await isInviteEnglish(signal)
          const successContainer = document.querySelector(
            '.text-neutral-text1.text-head-l.mb-8'
          )
          const successFlag =
            successContainer &&
            successContainer.textContent
              .toLocaleLowerCase()
              .includes('congratulations')
          const successContainer2 = document.querySelector(
            '.arco-btn.arco-btn-text.arco-btn-size-default.arco-btn-shape-square.cursor-pointer.arco-btn-primary-text'
          )
          const successFlag2 =
            successContainer2 &&
            successContainer2.textContent
              .toLocaleLowerCase()
              .includes('view invitations')
          // if (successFlag || sendInviteStatus) {
          console.log('是否邀约成功', successFlag || successFlag2)
          if (successFlag || successFlag2) {
            // 邀约成功之后先回到邀约页面
            const backBtn = document.querySelector(
              '.arco-breadcrumb-item.m4b-breadcrumb-item'
            )
            backBtn && (await simulateClick(signal, backBtn))

            chrome.runtime.sendMessage({
              domainUrl: window.location.hostname,
              action: 'newTabToInviteResult',
              result: {
                code: 200,
                message: 'invite success'
              },
              backgroundParams: backgroundParams
            })
          } else {
            // 没有邀约成功继续向其他创作者发送邀约
            chrome.runtime.sendMessage({
              domainUrl: window.location.hostname,
              action: 'newTabToInviteResult',
              result: {
                code: 201,
                message: 'invite continuous'
              },
              backgroundParams: backgroundParams
            })
          }
        }, 2000)
      }
    } catch (e) {
      console.log('是否可以捕获到错误', e, toString())
      workerTimer.clearInterval(intervalTimer)
      chrome.runtime.sendMessage({
        domainUrl: window.location.hostname,
        action: 'newTabToInviteResult',
        result: {
          code: 500,
          message: e.toString()
        },
        backgroundParams: backgroundParams
      })
    }
  }, 1000)

  // 停止任务检测
  if (signal) {
    signal.addEventListener('abort', () => {
      workerTimer.clearInterval(intervalTimer)
      inviteRunning = false
    })
  }
}

// 新页面执行私信操作
async function newTabToLetter(params, backgroundParams, signal, sendResponse) {
  const {
    letterParams: {
      letter: { message, imageList, productList, collaborationsList }
    },
    creator
  } = params
  letterPageLoading = true
  let intervalTimer = 0
  let times = 0
  try {
    // 判断当前网红是否可以聊，不可以的话直接跳过
    intervalTimer = workerTimer.setInterval(async () => {
      if (times > 30) {
        workerTimer.clearInterval(intervalTimer)
        console.log('这里是没有找到任何标志性事务，所以停止')
        // 停止任务
        sendResponse({
          code: 500,
          message: 'Task close'
        })
        return
      }
      times++
      // 输入框
      let inputContainer = document.querySelector('#im_sdk_chat_input')
      // 弹窗
      const tipMask = document.querySelector(
        '.arco-modal.m4b-modal.m4b-modal-size-small.m4b-modal-with-icon.zoomModal-appear-done.zoomModal-enter-done'
      )
      const messageLimit = document.querySelector(
        '.p-16.text-center.bg-white.rounded-8.m-8.text-neutral-text4.text-body-m-regular'
      )
      // 达到本周限制 + 粉丝数限制
      let messageLimit2 =
        (document.querySelector(
          '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
        ) &&
          document
            .querySelector(
              '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
            )
            .textContent.includes('reached the weekly limit')) ||
        (document.querySelector(
          '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
        ) &&
          document
            .querySelector(
              '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
            )
            .textContent.includes('Cannot send messages'))
      // 由于页面会先加载成被限制的页面，所以这里做二次校验
      if (messageLimit2) {
        let checkMessageLimit2Num = 0
        while (letterPageLoading) {
          checkMessageLimit2Num++
          // 如果 8s还没加载出聊天框,认定为不可用
          if (checkMessageLimit2Num > 8) {
            break
          }
          await sleepFun(1000)
          messageLimit2 =
            document.querySelector(
              '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
            ) &&
            document
              .querySelector(
                '.py-12.px-16.bg-white.rounded-8.flex.items-center.justify-center.flex-col'
              )
              .textContent.includes('reached the weekly limit')
          inputContainer = document.querySelector('#im_sdk_chat_input')
          if (!messageLimit2 && inputContainer) {
            break
          }
        }
      }
      // 关闭提示界面
      let messageLimit3 =
        document.querySelector(
          '.arco-modal-wrapper.arco-modal-wrapper-align-center'
        ) &&
        document
          .querySelector('.arco-modal-wrapper.arco-modal-wrapper-align-center')
          .textContent.includes('Weekly outreach limits')

      if (inputContainer || tipMask || messageLimit || messageLimit2) {
        workerTimer.clearInterval(intervalTimer)
        if (tipMask || messageLimit || messageLimit2) {
          // 有提示弹窗直接跳过
          // workerTimer.setTimeout(() => {
          //   sendResponse({
          //     code: 201,
          //     message: ' can not message creators'
          //   })
          // }, 3000)
          chrome.runtime.sendMessage({
            domainUrl: window.location.hostname,
            action: 'newTabToLetterResult',
            result: {
              code: 201,
              message: 'letter continuous',
              creator
            },
            backgroundParams: backgroundParams
          })
        } else {
          // 关闭提示界面
          if (messageLimit3) {
            // arco-btn arco-btn-secondary arco-btn-size-large arco-btn-shape-square
            const cancelBtn = document
              .querySelector(
                '.arco-modal-wrapper.arco-modal-wrapper-align-center'
              )
              .querySelector(
                '.arco-btn.arco-btn-secondary.arco-btn-size-large.arco-btn-shape-square'
              )
            letterRunning && (await openDebuggerToClick(cancelBtn))
          }
          // 填写私信
          letterRunning &&
            message &&
            (await fillInLetterMessage(
              signal,
              creator.name,
              message,
              sendResponse
            ))
          // 点击发送按钮
          await sendLetter(signal, sendResponse)
          // 填写照片
          letterRunning &&
            imageList &&
            imageList.length &&
            (await fillInLetterImage(signal, imageList, sendResponse))

          // 填写商品卡
          const realProductList = productList.filter(item => item.id)
          letterRunning &&
            realProductList &&
            realProductList.length &&
            (await fillInProductLink(signal, realProductList, sendResponse))

          // 发送定向合作卡片
          const realCollaborationsList = collaborationsList.filter(
            item => item.id
          )
          letterRunning &&
            realCollaborationsList &&
            realCollaborationsList.length &&
            (await fillInCollaborationsList(
              signal,
              realCollaborationsList,
              sendResponse
            ))

          await sleepFun(1000)

          chrome.runtime.sendMessage({
            domainUrl: window.location.hostname,
            action: 'newTabToLetterResult',
            result: {
              code: 200,
              message: 'letter success',
              creator
            },
            backgroundParams: backgroundParams
          })
        }
      }
    }, 2000)
  } catch (e) {
    workerTimer.clearInterval(intervalTimer)
    // 没有私信成功继续向其他创作者发送邀约
    console.log('e=====', e)
    chrome.runtime.sendMessage({
      domainUrl: window.location.hostname,
      action: 'newTabToLetterResult',
      result: {
        code: 201,
        message: 'letter continuous',
        creator
      },
      backgroundParams: backgroundParams
    })
  }

  // 停止任务检测
  if (signal) {
    signal.addEventListener('abort', () => {
      letterRunning = false
      workerTimer.clearInterval(intervalTimer)
    })
  }
}

// 搜索获取createList
async function searchGetCreatorList(params, signal, sendResponse) {
  let intervalTimer = 0
  try {
    const { keyWord } = params
    // 获取搜索框
    const searchContainer = document.querySelector(
      '.arco-input-inner-wrapper.arco-input-inner-wrapper-default.arco-input-clear-wrapper'
    )
    const searchInput = searchContainer.querySelector(
      '.arco-input.arco-input-size-default'
    )
    await simulateInput(signal, 'number', searchInput, keyWord)
    await simulateKeyPress(signal, 'Enter', 13, searchInput)

    await new Promise(resolve => {
      intervalTimer = workerTimer.setInterval(() => {
        const tableContainer = document.querySelector('.arco-table-body')
        if (tableContainer) {
          workerTimer.clearInterval(intervalTimer)
          resolve('search success')
        }
      }, 1000)
    }).catch(() => {
      workerTimer.clearInterval(intervalTimer)
    })
    sendResponse({
      code: 200,
      message: 'search success'
    })
  } catch (e) {
    sendResponse({
      code: 500,
      message: e
    })
  }

  // 停止任务检测
  if (signal) {
    workerTimer.clearInterval(intervalTimer)
    signal.addEventListener('abort', () => {
      sendResponse({
        code: 500,
        message: 'Task close'
      })
    })
  }
}

// --------------------------公共处理相关函数-----------------------------
// 选择语言
async function selectLanguage(signal, firstLanguage, secondLanguage) {
  let pageTimer = 0
  let tableTimer = 0
  try {
    // find页面（私信和邀约操作）
    let tableIcon = document.querySelector(
      '.alliance-icon.alliance-icon-List_view.w-16.h-16.cursor-pointer'
    )
    // manage页面
    let manageIcon = document.querySelector(
      '.bg-neutral-bg4.pt-24.pb-16.px-24.rounded-8'
    )

    // 邀约页面
    let inviteIcon = document.querySelector('.m4b-page-header-title-text')
    await new Promise((resolve, reject) => {
      let getTableIconTimers = 0
      pageTimer = workerTimer.setInterval(async () => {
        // 获取页面的header
        tableIcon = document.querySelector(
          '.alliance-icon.alliance-icon-List_view.w-16.h-16.cursor-pointer'
        )
        manageIcon = document.querySelector(
          '.bg-neutral-bg4.pt-24.pb-16.px-24.rounded-8'
        )
        inviteIcon = document.querySelector('.m4b-page-header-title-text')
        if (tableIcon || manageIcon || inviteIcon) {
          workerTimer.clearInterval(pageTimer)
          resolve('success')
        }
        if (getTableIconTimers >= 10) {
          workerTimer.clearInterval(pageTimer)
          reject('no found tableIcon or manageIcon') // 抛出异常
          throw new Error('no found tableIcon or manageIcon')
        }
        getTableIconTimers++
      }, 1000)
    })

    // 选择英语
    let userMenu = document.querySelector(
      '.px-16.py-2.ml-8.cursor-pointer.h-full'
    )
    await simulateClick(signal, userMenu)
    // 判断是否是打开状态
    let userMenuOpen = document.querySelector(
      '.arco-popover-content.arco-popover-content-br'
    )

    while (!userMenuOpen && (inviteRunning || letterRunning)) {
      userMenu = document.querySelector(
        '.px-16.py-2.ml-8.cursor-pointer.h-full'
      )
      await simulateFocusOut(signal, userMenu)
      await simulateClickEvent(signal, userMenu)
      await simulateFocus(signal, userMenu)
      userMenuOpen = document.querySelector(
        '.arco-popover-content.arco-popover-content-br'
      )
    }

    let popMenuItemList = document.querySelectorAll(
      '.arco-menu-pop.arco-menu-pop-header.arco-menu-selected.m4b-menu-submenu'
    )

    let languageBtn =
      popMenuItemList && popMenuItemList[popMenuItemList.length - 1]

    await simulateHover(signal, languageBtn)

    let languageContainer = document.querySelector(
      '.arco-dropdown-menu.arco-dropdown-menu-light.arco-dropdown-menu-vertical.arco-dropdown-menu-collapse.arco-dropdown-menu-pop'
    )
    while (!languageContainer && (inviteRunning || letterRunning)) {
      await simulateHover(signal, languageBtn)
      languageContainer = document.querySelector(
        '.arco-dropdown-menu.arco-dropdown-menu-light.arco-dropdown-menu-vertical.arco-dropdown-menu-collapse.arco-dropdown-menu-pop'
      )
    }
    const languageList = languageContainer.getElementsByClassName(
      'arco-dropdown-menu-item'
    )
    let EnglishItem = ''
    if (languageList)
      for (let i = 0; i < languageList.length; i++) {
        if (
          languageList[i].innerHTML.includes(firstLanguage) ||
          languageList[i].innerHTML.includes(secondLanguage)
        ) {
          EnglishItem = languageList[i]
          break
        }
      }
    await simulateClick(signal, EnglishItem)

    // 移开焦点
    await simulateClick(signal, userMenu)

    console.log('按钮时', tableIcon, manageIcon)
    // 选择table
    tableIcon && (await simulateClickEvent(signal, tableIcon))
    manageIcon && (await simulateClickEvent(signal, manageIcon))

    tableTimer = workerTimer.setInterval(() => {
      // 等待table加载完成在返回结果
      const tableList = document.querySelector('#scroll-container')
      if (tableList) {
        workerTimer.clearInterval(tableTimer)
      }
    }, 1000)
  } catch (e) {
    workerTimer.clearInterval(pageTimer)
    workerTimer.clearInterval(tableTimer)
    throw new Error(e || 'running err') // 抛出异常
  }

  signal &&
    signal.addEventListener('abort', async () => {
      inviteRunning = false
      letterRunning = false
      workerTimer.clearInterval(pageTimer)
      // 恢复原样
      const userMenu = document.querySelector(
        '.px-16.py-2.ml-8.cursor-pointer.h-full'
      )
      let languageBtn = document.querySelector(
        '.arco-menu-pop.arco-menu-pop-header.arco-menu-selected.m4b-menu-submenu'
      )
      if (languageBtn) await simulateClick(null, userMenu)
      workerTimer.clearInterval(pageTimer)
      workerTimer.clearInterval(tableTimer)
      throw new Error('Task close') // 抛出异常
    })
}
// 滚动获取create列表
async function getEnoughCreator(
  signal,
  num,
  blackCreator = [],
  creatorListInfo = [],
  filters = null,
  otherParams = {
    currentPage: 0,
    offset: 0,
    getAllData: false,
    reqGap: 2
  }
) {
  try {
    // currentPage是当前请求的网红页码，可变； firstPage是当次获取网红的第一个页码，不可变
    const {
      firstPage = 0,
      firstOffset = 0,
      // productIdList,
      mainTabId = 0,
      skipHasReplied = false,
      reqGap = 2
    } = otherParams

    if (num > creatorListInfo.length) {
      // 调接口获取用户数据（scroll不起作用）
      const tabUrl = window.location.origin
      let findUrl = `${tabUrl}/api/v1/oec/affiliate/creator/marketplace/find?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`
      const query = filters ? filters.query : ''
      const filter_params = filters ? deepClone(filters) : null
      filter_params && delete filter_params.query

      let params = {
        pagination: { size: 12, page: otherParams.currentPage },
        query_type: 1,
        algorithm: 26, // 按粉丝量排序
        query: query || ``,
        filter_params: filter_params || {}
      }

      let originCreatorListInfo = []
      let nextPageRes = null
      let targetCreatorList = null
      if (!(inviteRunning || letterRunning)) {
        return
      }

      const sendFindReq = async () => {
        console.log('请求gap是==', reqGap)
        await sleepFun(getRandomNumber(reqGap, reqGap + 1) * 1000)
        return postRequest(findUrl, { ...params }, 5, {
          successCode: 0,
          codeKey: 'code'
        }).then(data => {
          if (data.code === 0) {
            // const result = tabUrl.includes('affiliate.tiktok.com')
            //   ? data.data
            //   : data
            const result = data
            targetCreatorList = result.creator_profile_list
            // 用来判断是否被限制
            const connectInfoCreatorList =
              result.creator_connect_info_list || null

            if (targetCreatorList) {
              if (connectInfoCreatorList && connectInfoCreatorList.length) {
                targetCreatorList.forEach((creatorItem, index) => {
                  if (
                    !(
                      connectInfoCreatorList[index] &&
                      connectInfoCreatorList[index].can_not_connect_type &&
                      connectInfoCreatorList[index].can_not_connect_type.length
                    )
                  ) {
                    const name = creatorItem.handle.value
                    const id = creatorItem.creator_oecuid.value
                    originCreatorListInfo.push({
                      name,
                      id
                    })
                  }
                })
              } else {
                targetCreatorList.forEach(creatorItem => {
                  const name = creatorItem.handle.value
                  const id = creatorItem.creator_oecuid.value
                  originCreatorListInfo.push({
                    name,
                    id
                  })
                })
              }
            }
            otherParams.currentPage = otherParams.currentPage + 1
            otherParams.offset = 0
            if (result.next_pagination) {
              // otherParams.currentPage = result.next_pagination.next_page
              nextPageRes = result.next_pagination
            }
          } else {
            return new Promise((resolve, reject) => {
              reject('task stop (search)')
            })
          }
        })
      }

      addProcessLoadingTest('页面查找网红')
      // 获取上次存储的数据如果page一样从存储数据中获取数据
      await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          {
            action: 'getStoreValue',
            mainTabId: mainTabId,
            keyWord: 'beforeFindData'
          },
          async function (storeResponse) {
            try {
              const { storeValue } = storeResponse
              console.log('获取store存储数据======beforeFindData', storeValue)
              if (storeValue) {
                const { currentPage } = storeValue
                if (currentPage === otherParams.currentPage) {
                  originCreatorListInfo = storeValue.originCreatorListInfo
                  nextPageRes = storeValue.nextPageRes
                  targetCreatorList = storeValue.targetCreatorList
                  otherParams.currentPage = otherParams.currentPage + 1
                  otherParams.offset = 0
                } else {
                  await sendFindReq()
                }
              } else {
                await sendFindReq()
              }
              resolve()
            } catch (e) {
              reject(e)
            }
          }
        )
      })

      console.log(
        '当前获取的创作者列表数------',
        originCreatorListInfo,
        otherParams
      )

      // 网红入组
      const pushCreatorListInfo = async creatorItem => {
        // 如果需要，跳过网红已回复但商家未进一步回复的网红
        let hasReplied = false

        if (otherParams.page === 'letter' && skipHasReplied) {
          hasReplied = await checkoutByConversationSearch(creatorItem)
          await sleepFun(getRandomNumber(reqGap, reqGap + 1) * 1000)
        }

        if (
          otherParams.page === 'invite' ||
          (otherParams.page === 'letter' && !hasReplied)
        ) {
          creatorListInfo.push(creatorItem)
          addProcessLoadingTest(
            `获取网红${creatorItem.name}，获取网红数目：${creatorListInfo.length}`
          )
          await sleepFun(500)
        }
      }

      for (let index = 0; index < originCreatorListInfo.length; index++) {
        if (!inviteRunning && !letterRunning) return
        const creatorItem = originCreatorListInfo[index]

        // 更新creatorList，offset,currentPage
        const creatorListName = creatorListInfo.map(item => item.name)
        if (
          creatorListInfo.length < num &&
          !creatorListName.includes(creatorItem.name) &&
          !blackCreator.includes(creatorItem.name)
        ) {
          if (otherParams.currentPage - 1 === firstPage) {
            if (index > firstOffset || index === firstOffset) {
              await pushCreatorListInfo(creatorItem)
            }
          } else {
            await pushCreatorListInfo(creatorItem)
          }
        } else if (creatorListInfo.length === num) {
          addProcessLoadingTest('已获取目标个数网红')
          otherParams.offset = index
          otherParams.currentPage = otherParams.currentPage - 1

          // 获取成功，存储数据
          chrome.runtime.sendMessage({
            action: 'updateStoreValue',
            mainTabId: mainTabId,
            keyWord: 'beforeFindData',
            value: {
              currentPage: otherParams.currentPage,
              originCreatorListInfo: originCreatorListInfo,
              targetCreatorList: targetCreatorList,
              nextPageRes: nextPageRes
            }
          })
          break
        }
      }
      // otherParams.offset = 0 说明当前接口获取的网红都已被获取到,此时如果has_more为false说明已经获取到所有的网红
      if (!nextPageRes.has_more && otherParams.offset === 0)
        otherParams.getAllData = true
      else if (!targetCreatorList && (!nextPageRes || !nextPageRes.has_more))
        otherParams.getAllData = true
      else otherParams.getAllData = false
    }

    console.log('otherParams.getAllData===', otherParams.getAllData)
    // 更新参数
    if (otherParams.page === 'invite') {
      // invite从页面获取,直接更新即可
      otherParams.firstPage = otherParams.currentPage
      otherParams.firstOffset = otherParams.offset
      chrome.runtime.sendMessage({
        action: 'updateStoreValue',
        mainTabId: mainTabId,
        keyWord: 'currentInviteNextPage',
        value: {
          currentPage: otherParams.currentPage,
          offset: otherParams.offset
        }
      })
    } else if (otherParams.page === 'letter') {
      console.log('更新页面数据啦', otherParams.currentPage, otherParams.offset)
      chrome.runtime.sendMessage({
        action: 'updateStoreValue',
        mainTabId: mainTabId,
        keyWord: 'currentLetterNextPage',
        value: {
          currentPage: otherParams.currentPage,
          offset: otherParams.offset
        }
      })
    }

    if (!otherParams.getAllData && num > creatorListInfo.length) {
      // await simulateScrollByScrollTop(signal, 600, scrollConainer)
      await getEnoughCreator(
        signal,
        num,
        blackCreator,
        creatorListInfo,
        filters,
        otherParams
      ).catch(e => {
        throw new Error(e) // 抛出异常
      })
    }
  } catch (e) {
    console.log('获取创作者捕获到的错误', e)
    throw new Error(e || 'get creator err') // 抛出异常
  }

  signal.addEventListener('abort', () => {
    inviteRunning = false
    letterRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// --------------------------invite相关函数-----------------------------
// invite填充创建者列表
async function fillInCreatorList(
  signal,
  params,
  backgroundParams,
  sendResponse
) {
  console.log('===========填写创作者===========')
  try {
    const {
      // eslint-disable-next-line no-unused-vars
      invite: { timeGap = 5, products = [] },
      type,
      blackCreator,
      skipDay,
      filters,
      currentPage,
      offset,
      productIdList,
      reqGap,
      mainTabId,
      creatorTimes,
      ableInviteCreator
    } = params

    const otherParams = {
      currentPage: currentPage,
      offset: offset,
      getAllData: false,
      firstPage: deepClone(currentPage || 0),
      firstOffset: deepClone(offset || 0),
      page: 'invite',
      productIdList,
      mainTabId,
      reqGap
    }
    let inviteCreator = []
    let currentCreatorInfo = []
    if (type === 'noCreator') {
      // 初始化
      await getEnoughCreator(
        signal,
        1,
        blackCreator,
        currentCreatorInfo,
        filters,
        otherParams
      ).catch(e => {
        console.log('get creatorList error- fill in', e.toString())
        throw new Error(e || 'get creatorList error- fill in') // 抛出异常
      })
      inviteCreator = currentCreatorInfo.map(item => item.name)
      backgroundParams.getAllData = otherParams.getAllData
    } else {
      // 已填写邀约网红的处理
      inviteCreator = ableInviteCreator
    }
    // 获取creator
    // 有网红才能继续操作
    if (!(inviteCreator && inviteCreator.length)) {
      throw new Error('no creator') // 抛出异常
    }
    const creatorInput =
      document.querySelector(
        'input[placeholder="Search creators by username or user ID"]'
      ) ||
      document.querySelector(
        'input[placeholder="Add creators by searching username or user ID"]'
      )

    let lastIndex = -1
    // 填写网红
    for (let j = 0; j < inviteCreator.length; j++) {
      if (!inviteRunning) return
      // 邀约前先进行一下验证操作
      // 1. 验证网红是否存在
      if (type === 'hasCreator') {
        addCreatorCheckLoading(inviteCreator[j])
        const tabUrl = window.location.origin
        const res = await postRequest(
          `${tabUrl}/api/v1/oec/affiliate/seller/invitation_group/search/creator${tokenQuery}`,
          {
            query: inviteCreator[j],
            size: 20,
            offset: 0,
            hasMore: true,
            need_live_permission: true
          },
          5,
          {
            successCode: 0,
            codeKey: 'code'
          }
        )
        // 需要查找这个网红是否真的存在
        if (res && res.code === 0) {
          const targetCreatorList = res.data.creators
          if (targetCreatorList) {
            let searchTrue = false
            for (let index = 0; index < targetCreatorList.length; index++) {
              let creatorItem = targetCreatorList[index]
              if (creatorItem.user_name === inviteCreator[j]) {
                searchTrue = true
                const name = creatorItem.user_name
                const id = creatorItem.creator_oec_id
                currentCreatorInfo = [
                  {
                    name,
                    id
                  }
                ]
              }
            }
            if (!searchTrue) {
              continue
            }
          } else {
            continue
          }
        } else {
          continue
        }
      }
      console.log('currentCreatorInfo===', currentCreatorInfo)

      // 2. 跳过已联系(所以先在这里进行后端验证吧)
      const res = await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          {
            action: 'filterCreatorByServerFromPage',
            mainTabId: mainTabId,
            params: {
              creatorList: [inviteCreator[j]],
              currentCreatorInfo: currentCreatorInfo,
              source: {
                page: 'invite',
                skipDay
              }
            }
          },
          response => {
            if (chrome.runtime.lastError) {
              reject(new Error(chrome.runtime.lastError))
            } else {
              resolve(response)
            }
          }
        )
      })
      if (res && res.code !== 200) {
        throw new Error(res.message)
      } else {
        if (
          !res.data.currentCreatorInfo ||
          !res.data.currentCreatorInfo.length
        ) {
          if (type === 'noCreator' && j === inviteCreator.length - 1) {
            // 页面获取创作者，并且是最后一个创作者，并且没有达到目标数据，就继续获取创作者
            currentCreatorInfo = []
            console.log('otherParams===', deepClone(otherParams))
            // 获取一个邀约一个
            await getEnoughCreator(
              signal,
              1,
              blackCreator,
              currentCreatorInfo,
              filters,
              otherParams
            ).catch(e => {
              console.log('get creatorList error - fill in', e.toString())
            })
            console.log('当前获取到的网红', currentCreatorInfo)
            currentCreatorInfo.forEach(item => {
              inviteCreator.push(item.name)
            })
            backgroundParams.getAllData = otherParams.getAllData
          }
          await sleepFun(timeGap * 1000)
          continue
        }
      }

      //  3. 验证是否冲突(还是不能用)
      // const conflict = await checkoutCreatorAble(
      //   products.map(item => item.id),
      //   currentCreatorInfo
      // )
      // if (conflict) continue

      addProcessLoadingTest(`正在邀约...`)

      const inviteItem = inviteCreator[j]
      await simulateFocus(signal, creatorInput)
      await simulateClickEvent(signal, creatorInput)
      await simulateInput(signal, 'number', creatorInput, inviteItem)

      // 网红的搜索结果的外层容器
      await isInviteEnglish(signal)
      let creatorContainer = document.querySelector(
        '.shadow-downL.border-neutral-line1.bg-white.rounded-4'
      )
      let noCreator =
        creatorContainer &&
        creatorContainer.textContent.includes('Creator not found')
      let searchList = document.querySelectorAll('.arco-list-item.rounded-4')

      while (
        !noCreator &&
        !(searchList && searchList.length) &&
        inviteRunning
      ) {
        await simulateFocusOut(signal, creatorInput)
        await simulateFocus(signal, creatorInput)
        await simulateClickEvent(signal, creatorInput)
        await isInviteEnglish(signal)
        creatorContainer = document.querySelector(
          '.shadow-downL.border-neutral-line1.bg-white.rounded-4'
        )
        noCreator =
          creatorContainer &&
          creatorContainer.textContent.includes('Creator not found')
        searchList = document.querySelectorAll('.arco-list-item.rounded-4')
      }

      let targetCreator = null
      if (searchList && searchList.length) {
        for (let i = 0; i < searchList.length; i++) {
          const nameContainer = searchList[i]
            ? searchList[i].querySelector(
                '.items-center.text-neutral-text1.text-body-m-regular'
              )
            : ''
          const name = nameContainer ? nameContainer.textContent.slice(1) : ''
          if (name === inviteItem) {
            targetCreator = searchList[i]
          }
        }
      }
      inviteCreator &&
        targetCreator &&
        (await simulateClickEvent(signal, targetCreator))

      // 判断是否有重复任务的弹窗，如果有就关闭（所以要先填商品，再填creator）
      // eslint-disable-next-line no-unused-vars
      await new Promise(resolve => {
        workerTimer.setTimeout(async () => {
          let repeatContainer = document.querySelector(
            '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
          )
          while (repeatContainer && inviteRunning) {
            // 直接点击ok按钮，看看可不可行（点击ok关闭页面，自动删除）
            await isInviteEnglish(signal)
            const btnList = repeatContainer.querySelectorAll(
              '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
            )
            let okBtn = btnList[btnList.length - 1]
            for (let i = 0; i < btnList.length; i++) {
              if (btnList[i].textContent.includes('OK')) okBtn = btnList[i]
            }
            inviteRunning && (await simulateClick(signal, okBtn))
            repeatContainer = document.querySelector(
              '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
            )
          }
          resolve()
        }, 1000)
      })

      // 判断是否达到了目标数目
      let numberList = document.querySelectorAll(
        '.text-body-m-medium.text-brand-normal'
      )
      let num =
        numberList && numberList.length >= 1
          ? Number(numberList[numberList.length - 1].innerText.split('/')[0])
          : null

      if (!num) {
        // 适配欧盟国家
        numberList = document.querySelectorAll('.arco-pagination-total-text')
        num =
          numberList && numberList.length >= 1
            ? Number(numberList[numberList.length - 1].innerText.split('/')[1])
            : null
      }
      if (!num) {
        // 适配欧盟国家
        numberList = document.querySelectorAll('.arco-pagination-total-text')
        num =
          numberList && numberList.length >= 1
            ? Number(numberList[numberList.length - 1].innerText.split('of')[1])
            : null
      }

      console.log('当前获取的网红个数是===', num, creatorTimes)

      // 如果num获取不到，以50为标准
      // if (num === null) {
      //   console.log('j===', j)
      //   if (j >= 50) break
      // }

      // 判断是否达到目标
      // if (j === creatorTimes - 1) break
      if (num && num >= creatorTimes) {
        // 记录当前邀请到哪里了
        lastIndex = j
        break
      }
      if (type === 'noCreator' && j === inviteCreator.length - 1) {
        // 页面获取创作者，并且是最后一个创作者，并且没有达到目标数据，就继续获取创作者
        currentCreatorInfo = []
        // 获取一个邀约一个
        await getEnoughCreator(
          signal,
          1,
          blackCreator,
          currentCreatorInfo,
          filters,
          otherParams
        ).catch(e => {
          console.log('get creatorList error - fill in', e.toString())
        })
        console.log('当前获取到的网红', currentCreatorInfo)
        currentCreatorInfo.forEach(item => {
          inviteCreator.push(item.name)
        })
        backgroundParams.getAllData = otherParams.getAllData
      }

      await sleepFun(timeGap * 1000)

      signal.addEventListener('abort', () => {
        inviteRunning = false
        throw new Error('Task close') // 抛出异常
      })
      if (!inviteRunning) {
        break
      }
    }
    // 记录当前邀请到哪里了
    if (type === 'hasCreator') {
      if (lastIndex > -1) {
        backgroundParams.params.inviteParams.lastInvitedCreator =
          inviteCreator[lastIndex]
      } else {
        // 说明都邀约完了
        backgroundParams.params.inviteParams.lastInvitedCreator =
          inviteCreator[inviteCreator.length - 1]
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    inviteRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// invite 获取外层点击的container
async function getOuterContainerList(signal, innerText, sendResponse) {
  try {
    await isInviteEnglish(signal)
    const containerList = document.querySelectorAll(
      '.arco-collapse-item-header.arco-collapse-item-header-right'
    )
    for (const containerItem of containerList) {
      const title = containerItem.querySelector('.arco-typography').textContent
      if (title === innerText) {
        inviteRunning && (await simulateClick(signal, containerItem))
        console.log(`点击${innerText}`)
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'get out container err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    inviteRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// invite填充商品列表
async function fillInProduct(signal, products, sendResponse) {
  let intervalTimer = 0
  try {
    if (!(products && products.length)) {
      throw new Error('no products') // 抛出异常
    }
    for (let index = 0; index < products.length; index++) {
      const productItem = products[index]
      // 添加按钮
      let productAddBtn = null
      while (!productAddBtn && inviteRunning) {
        await isInviteEnglish(signal)
        const productBtnList = document.querySelectorAll(
          '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square'
        )
        for (const productBtnItem of productBtnList) {
          if (productBtnItem.textContent.includes('Add products')) {
            productAddBtn = productBtnItem
          }
        }
        await sleepFun(1000)
      }
      inviteRunning &&
        productAddBtn &&
        (await simulateClick(signal, productAddBtn))

      // 输入框
      const inputContainer = document
        .querySelector('.flex.items-center.space-x-12.mb-16')
        .querySelector('.m4b-input-group-select')
      const typeSelect = inputContainer.querySelector(
        '.arco-select.arco-select-single.arco-select-size-default.m4b-select.m4b-new-select'
      )
      const inputEle = inputContainer
        .querySelector('.arco-input-inner-wrapper')
        .querySelector('.arco-input.arco-input-size-default')
      // const searchBtn = inputContainer.querySelector('.arco-input-group-suffix')
      // 选择ProductId
      inviteRunning && (await simulateClick(signal, typeSelect))
      let typeSelectContainer = document.querySelector(
        '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
      )
      let typeSelectNumber = 0
      while (!typeSelectContainer) {
        if (typeSelectNumber >= 10) {
          throw new Error('find typeSelectContainer error')
        }
        await sleepFun(1000)
        inviteRunning && (await simulateClick(signal, typeSelect))
        typeSelectContainer = document.querySelector(
          '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
        )
        typeSelectNumber++
        console.log('typeSelectContainer==', typeSelectContainer)
      }
      await isInviteEnglish(signal)
      const typeList = typeSelectContainer.querySelectorAll(
        '.arco-select-option.m4b-select-option'
      )
      if (typeList && typeList.length) {
        for (const typeItem of typeList) {
          if (typeItem.textContent.includes('Product ID')) {
            inviteRunning && (await simulateClick(signal, typeItem))
          }
        }
      }

      // 填写id并搜索
      const { id, percent, shopAd = false, shopAdPercent = null } = productItem
      while (!inputEle.value && inviteRunning) {
        await simulateFocus(signal, inputEle)
        await simulateInput(signal, 'number', inputEle, id)
        await simulateKeyPress(signal, 'Enter', 13, inputEle)
      }

      // 选择产品
      let drawContainer = document.querySelector(
        '.arco-drawer.m4b-drawer.slideRight-enter-done'
      )
      const getTableList = () => {
        return new Promise(resolve => {
          intervalTimer = workerTimer.setInterval(() => {
            drawContainer = document.querySelector(
              '.arco-drawer.m4b-drawer.slideRight-enter-done'
            )
            const tableList =
              drawContainer && drawContainer.querySelector('.arco-table-tr')
            if (tableList) {
              workerTimer.clearInterval(intervalTimer)
              resolve(tableList)
            }
          }, 1000)
        }).catch(() => {
          workerTimer.clearInterval(intervalTimer)
        })
      }
      await getTableList()
      let tableContainer = drawContainer.querySelector('.arco-table-container')
      const tableList = tableContainer
        .querySelector('tbody')
        .querySelectorAll('.arco-table-tr')
      // 没有匹配到
      const emptyTableList = tableContainer.querySelector(
        '.arco-table-tr.arco-table-empty-row'
      )
      let hasNot =
        !tableList || (tableList && tableList.length === 0) || emptyTableList
      // 匹配到了不能用
      let hasButDisable = false
      for (const tableItem of tableList) {
        const productId = tableItem.querySelector(
          '.text-neutral-text3.text-body-s-regular'
        )
          ? tableItem.querySelector('.text-neutral-text3.text-body-s-regular')
              .textContent
          : ''

        if (productId && productId.includes(id)) {
          // 有可能匹配到产品但是产品是禁用状态
          const disableSelectBtn = tableItem.querySelector(
            '.arco-checkbox.arco-checkbox-disabled'
          )
          if (disableSelectBtn) {
            hasButDisable = true
            break
          }
          const selectBtn = tableItem.querySelector('.arco-checkbox')
          inviteRunning && (await simulateClick(signal, selectBtn))
        }
      }
      if (!hasNot && !hasButDisable) {
        // 点击add按钮
        let addBtn = null
        while (!addBtn && inviteRunning) {
          await isInviteEnglish(signal)
          const btnList = document.querySelectorAll(
            '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
          )
          for (const btnItem of btnList) {
            if (btnItem.textContent.includes('Add')) {
              addBtn = btnItem
            }
          }
          await sleepFun(1000)
        }
        inviteRunning && (await simulateClick(signal, addBtn))

        // 填写佣金百分比
        const productTable = document.querySelector('.arco-table-body')
        const productList = productTable
          .querySelector('tbody')
          .querySelectorAll('.arco-table-tr')
        if (productList && productList.length) {
          // 填写标准佣金
          const percentInput = productList[0].querySelector(
            '.arco-input.arco-input-size-default'
          )
          while (percentInput && !percentInput.value && inviteRunning) {
            await simulateFocus(signal, percentInput)
            await simulateInput(
              signal,
              'number',
              percentInput,
              Number(percent).toFixed(2)
            )
            // inviteRunning && (await simulateClick(signal, percentInput))
            await simulateKeyPress(signal, 'Enter', 13, percentInput)
            await simulateFocusOut(signal, percentInput)

            // 触发一下校验规则，防止即使输入了值，提交的时候还是验证不通过
            await simulateChange(signal, percentInput)
            await simulateBlur(signal, percentInput)
          }
          // 判断店铺佣金是否需要选中
          const switchBtn = productList[0].querySelector(
            '.arco-switch.arco-switch-type-circle'
          )
          const switchStatus =
            switchBtn && switchBtn.getAttribute('aria-checked')
          if (switchBtn && shopAd.toString() !== switchStatus) {
            inviteRunning && (await simulateClick(signal, switchBtn))
          }
          // 如果选中选广告佣金,填充概念广告佣金
          if (shopAd) {
            const shopAdPercentInput = productList[0].querySelectorAll(
              '.arco-input.arco-input-size-default'
            )[1]
            while (
              shopAdPercentInput &&
              !shopAdPercentInput.value &&
              inviteRunning
            ) {
              await simulateFocus(signal, shopAdPercentInput)
              await simulateInput(
                signal,
                'number',
                shopAdPercentInput,
                Number(shopAdPercent).toFixed(2)
              )
              // inviteRunning && (await simulateClick(signal, percentInput))
              await simulateKeyPress(signal, 'Enter', 13, shopAdPercentInput)
              await simulateFocusOut(signal, shopAdPercentInput)

              // 触发一下校验规则，防止即使输入了值，提交的时候还是验证不通过
              await simulateChange(signal, shopAdPercentInput)
              await simulateBlur(signal, shopAdPercentInput)
            }
          }
        }

        // 是否开启店铺广告佣金
      } else {
        // 如果没有搜索结果点击cancel按钮
        await isInviteEnglish(signal)
        const cancelbtnList = document.querySelectorAll(
          '.arco-btn.arco-btn-secondary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
        )
        let cancelBtn = null
        for (const btnItem of cancelbtnList) {
          if (btnItem.textContent.includes('Cancel')) {
            cancelBtn = btnItem
          }
        }
        inviteRunning && (await simulateClick(signal, cancelBtn))
      }
    }
  } catch (e) {
    workerTimer.clearInterval(intervalTimer)
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }

  signal.addEventListener('abort', () => {
    workerTimer.clearInterval(intervalTimer)
    inviteRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// invite填充样例
async function fillInFreeSamples(signal, sample, sendResponse) {
  try {
    const { able, type } = sample
    const switchBtnList = document.querySelectorAll(
      '.arco-switch.arco-switch-type-circle.m4b-switch'
    )
    const switchBtn = switchBtnList[switchBtnList.length - 1]
    const switchStatus = switchBtn.getAttribute('aria-checked')
    if (able.toString() !== switchStatus) {
      inviteRunning && (await simulateClick(signal, switchBtn))
    }
    // 如果选中就选择类型
    if (able) {
      await isInviteEnglish(signal)
      const selectOptions = document.querySelectorAll('.arco-radio.m4b-radio')
      let autoOption = null
      let manuallyOption = null
      for (const optionItem of selectOptions) {
        const content = optionItem.textContent
        if (content.includes('Auto')) {
          autoOption = optionItem
        }
        if (content.includes('Manually')) {
          manuallyOption = optionItem
        }
      }
      if (type === 'auto') {
        inviteRunning && (await simulateClick(signal, autoOption))
      } else {
        inviteRunning && (await simulateClick(signal, manuallyOption))
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    inviteRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// invite填充基本邀请信息
async function fillInInvitationInfo(signal, invite, sendResponse) {
  const { country, name, stopTime, email, message, zaloCode, zalo } = invite
  try {
    // 获取输入框
    let nameInput = null
    let dateInput = null
    let emailInput = null
    let messageInput = null
    const inputList = document.querySelectorAll(
      '.arco-input.arco-input-size-default'
    )
    while (!nameInput && inviteRunning) {
      await isInviteEnglish(signal)
      for (const inputItem of inputList) {
        const placeholder = inputItem.getAttribute('placeholder')
        if (placeholder === 'Invitation name') {
          nameInput = inputItem
        }
      }
      await sleepFun(1000)
    }

    dateInput = document.querySelector('.arco-picker-start-time')
    messageInput = document.querySelector(
      '#target_complete_details_message_input'
    )
    // 填写邀约名称
    while (nameInput && !nameInput.value && inviteRunning) {
      await simulateFocus(signal, nameInput)
      await simulateInput(signal, 'number', nameInput, name || 'invite')
      await simulateKeyPress(signal, 'Enter', 13, nameInput)
      await simulateFocusOut(signal, nameInput)
    }

    // 填写截止时间
    while (!dateInput.value && inviteRunning) {
      const formatStopTime = formatDate(new Date(stopTime))
      await simulateFocus(signal, dateInput)
      await simulateInput(signal, 'number', dateInput, formatStopTime)
      await simulateKeyPress(signal, 'Enter', 13, dateInput)
      await simulateFocusOut(signal, dateInput)
    }
    // const datePicker = document.querySelector('.arco-picker-container')
    // const currentSelectDate = datePicker.querySelector(
    //   '.arco-picker-cell.arco-picker-cell-selected'
    // )
    // await simulateClick(signal, currentSelectDate)

    if (['vn', 'th', 'my'].includes(country)) {
      // 填写电话
      const inputCodeContainer = document.querySelectorAll(
        '.arco-input.arco-input-size-default.arco-select-view-input'
      )[0]
      inputCodeContainer && (await simulateClick(signal, inputCodeContainer))
      inputCodeContainer && (await simulateFocus(signal, inputCodeContainer))
      inputCodeContainer &&
        (await simulateInput(signal, 'number', inputCodeContainer, zaloCode))
      // 选择
      const codeContainer = document.querySelector(
        '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
      )
      await isInviteEnglish(signal)
      const codeList = codeContainer.querySelectorAll(
        '.arco-select-option.m4b-select-option'
      )
      for (const codeItem of codeList) {
        const textContent = codeItem.textContent
        if (textContent.includes(`+${zaloCode} `)) {
          await simulateClick(signal, codeItem)
          break
        }
      }
      let targetPlaceholder = 'Please enter a Line account number'
      switch (country) {
        case 'vn':
          targetPlaceholder = 'Enter the Zalo account phone number'
          break
        case 'th':
          targetPlaceholder = 'Please enter a Line account number'
          break
        case 'my':
          targetPlaceholder = 'Please enter a WhatsApp account number'
          break
      }
      let zaloInput = null
      let findInputNums = 0
      while (!zaloInput && inviteRunning) {
        if (findInputNums > 20) {
          throw new Error('no find account input')
        }
        for (const inputItem of inputList) {
          const placeholder = inputItem.getAttribute('placeholder')
          if (placeholder === targetPlaceholder) {
            zaloInput = inputItem
          }
        }
        findInputNums++
        await sleepFun(1000)
      }
      // 填写邮箱
      await simulateFocus(signal, zaloInput)
      await simulateInput(signal, 'number', zaloInput, zalo)
      await simulateKeyPress(signal, 'Enter', 13, zaloInput)
      await simulateFocusOut(signal, zaloInput)
    } else {
      let findInputNums = 0
      while (!emailInput && inviteRunning) {
        if (findInputNums > 20) {
          throw new Error('no find email input')
        }
        await isInviteEnglish(signal)
        let targetPlaceholder = ''
        switch (country) {
          case 'ph':
            targetPlaceholder = 'Please enter an Facebook account'
            break
          default: // en, eu
            targetPlaceholder = 'Please enter an email address'
            break
        }
        for (const inputItem of inputList) {
          const placeholder = inputItem.getAttribute('placeholder')
          if (
            placeholder === targetPlaceholder ||
            (placeholder && placeholder.includes('email'))
            // (placeholder && placeholder.includes('account'))
          ) {
            emailInput = inputItem
          }
        }
        findInputNums++
        await sleepFun(1000)
      }
      // 填写邮箱
      await simulateFocus(signal, emailInput)
      await simulateInput(signal, 'number', emailInput, email)
      await simulateKeyPress(signal, 'Enter', 13, emailInput)
      await simulateFocusOut(signal, emailInput)
    }

    // 填写邀约信息
    await simulateFocus(signal, messageInput)
    await simulateInput(signal, 'number', messageInput, message)
    await simulateKeyPress(signal, 'Enter', 13, messageInput)
    await simulateFocusOut(signal, messageInput)
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    throw new Error('Task close') // 抛出异常
  })
}

// invite发送
async function sendInvite(signal, conflict) {
  console.log('是否有冲突提示框=======', conflict)
  try {
    // 是否有冲突
    let conflictTip = document.querySelector('.m4b-alert.m4b-alert-error')
    while (conflictTip) {
      const conflictBtn = conflictTip.querySelector(
        '.arco-btn.arco-btn-secondary'
      )
      await simulateClick(signal, conflictBtn)
      let conflictModal = document.querySelector(
        '.arco-modal-wrapper.arco-modal-wrapper-align-center'
      )
      while (!conflictModal) {
        await simulateClick(signal, conflictBtn)
        conflictModal = document.querySelector(
          '.arco-modal-wrapper.arco-modal-wrapper-align-center'
        )
        await sleepFun(1000)
      }
      const selectListContainer = conflictModal.querySelector(
        '.arco-radio-group.arco-radio-size-default.arco-radio-mode-outline.m4b-radio-group.m4b-radio-group-gap-size-default'
      )
      const selectList = selectListContainer.querySelectorAll(
        '.arco-radio.m4b-radio'
      )
      for (let i = 0; i < selectList.length; i++) {
        if (
          (selectList[i].innerText.includes('Move creators to') &&
            conflict === 1) ||
          (selectList[i].innerText.includes('Remove creators from') &&
            conflict === 2)
        ) {
          await simulateClick(signal, selectList[i])
        }
      }
      // 点击resolve
      const resolveBtn = conflictModal.querySelector(
        '.arco-btn.arco-btn-primary.arco-btn-size-large.arco-btn-shape-square'
      )
      await simulateClick(signal, resolveBtn)

      await sleepFun(2000)
      // 可能一次关不了
      conflictTip = document.querySelector('.m4b-alert.m4b-alert-error')
    }
    // send按钮
    await isInviteEnglish(signal)
    const btnList = document.querySelectorAll(
      '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
    )
    let sendBtn = null
    for (const btnItem of btnList) {
      if (
        btnItem.textContent.includes('Send') ||
        btnItem.textContent.includes('Submit')
      ) {
        sendBtn = btnItem
      }
    }
    if (sendBtn) {
      console.log('点击发送按钮啦==')
      await simulateClick(signal, sendBtn)
      // 判断是否有重复任务的弹窗，如果有就关闭(只能一个个删除)
      await new Promise(resolve => {
        workerTimer.setTimeout(async () => {
          const repeatContainer = document.querySelector(
            '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
          )
          if (repeatContainer) {
            // 获取提示词
            const creatorNameContainer = repeatContainer.querySelectorAll(
              '.arco-table-cell-wrap-value'
            )
            const creatorName =
              (creatorNameContainer[1] &&
                creatorNameContainer[1]
                  .querySelector('.arco-typography')
                  .textContent.slice(1, 16)) ||
              ''
            inviteRunning &&
              (await simulateKeyPress(signal, 'Esc', 27, repeatContainer))
            let repeat = document.querySelector(
              '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
            )
            while (repeat && inviteRunning) {
              inviteRunning &&
                (await simulateKeyPress(signal, 'Esc', 27, repeatContainer))
              repeat = document.querySelector(
                '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
              )
            }
            // 删除已插入的creator
            const tableList = document.querySelector(
              '.arco-table.arco-table-size-default.arco-table-border.arco-table-hover.arco-table-layout-fixed.m4b-table.m4b-table-vertical-center.mt-16.invitation-select-creator-table'
            )
            const selectCreatorList =
              (tableList &&
                tableList
                  .querySelector('tbody')
                  .querySelectorAll('.arco-table-tr')) ||
              []
            // 删除第一个
            let delIcon = null
            if (!selectCreatorList || !selectCreatorList.length) {
              return resolve()
            } else {
              for (let i = 0; i < selectCreatorList.length; i++) {
                const currentName =
                  selectCreatorList[i].querySelector(
                    '.text-neutral-text1.text-body-m-medium.cursor-pointer'
                  ) &&
                  selectCreatorList[i].querySelector(
                    '.text-neutral-text1.text-body-m-medium.cursor-pointer'
                  ).textContent
                if (currentName && currentName.includes(creatorName)) {
                  delIcon = selectCreatorList[i].querySelector(
                    '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button.m4b-button-link'
                  )
                  break
                }
              }
            }

            // 找不到的话直接跳出
            inviteRunning && delIcon && (await simulateClick(signal, delIcon))
            await sleepFun(2000)
            // 再次点击邀请按钮
            inviteRunning && (await sendInvite(signal))
            resolve()
          } else {
            resolve()
          }
        }, 1000)
      })
    } else {
      throw new Error('no find send btn')
    }
  } catch (e) {
    console.log('点击发送按钮出错', e)
  }
  signal.addEventListener('abort', () => {
    throw new Error('Task close') // 抛出异常
  })
}

// invite检测当前网红是否可用
// eslint-disable-next-line no-unused-vars
async function checkoutCreatorAble(productIdList, creatorItem) {
  let productList = productIdList.map(id => ({ product_id: id }))
  // 过滤空
  productList = productList.filter(item => item)

  const tabUrl = window.location.origin
  let findUrl = `${tabUrl}/api/v1/oec/affiliate/seller/invitation_group/conflict_check${tokenQuery}`
  return postRequest(
    findUrl,
    {
      invitation: {
        product_list: productList,
        creator_id_list: [
          {
            base_info: {
              user_name: creatorItem.name,
              creator_oec_id: creatorItem.id
            },
            status: 2
          }
        ]
      }
    },
    5,
    { successCode: 0, codeKey: 'code' }
  ).then(data => {
    if (data.code === 0 && data.data) {
      if (data.data.conflict_list && data.data.conflict_list.length > 0) {
        return true
      } else {
        return false
      }
    } else {
      return new Promise((resolve, reject) => {
        reject('task stop (conflict_check)')
      })
    }
  })
}

// letter私信跳过网红已回复但商家未进一步回复的网红
function checkoutByConversationSearch(creatorItem) {
  const tabUrl = window.location.origin
  let conversationSearchUrl = `${tabUrl}/api/v1/im/shop_creator/shop/conversation/search?uname=${creatorItem.name}&shop_region=${shopRegion}&user_language=en&biz=shop_creator&role=shop&cursor=0&app_name=i18n_ecom_alliance&device_id=0&device_platform=web&cookie_enabled=true&browser_name=Mozilla&browser_online=true`
  return getRequest(conversationSearchUrl, 5, {
    successCode: 0,
    codeKey: 'code'
  }).then(async data => {
    if (data.code === 0 && data.data) {
      const conversationsList = data.data.conversations
      if (conversationsList && conversationsList.length) {
        const members = conversationsList[0].members
        const creatorImId = members.filter(
          item => item.role_name === 'creator'
        )[0].im_id
        const lastMessage =
          conversationsList[0] && conversationsList[0].last_message
            ? conversationsList[0].last_message
            : null
        const senderId = lastMessage ? lastMessage.sender_id : null
        const messageType =
          lastMessage && lastMessage.ext ? lastMessage.ext.type : null
        if (
          lastMessage &&
          senderId === creatorImId &&
          messageType !== 'notification'
        ) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } else {
      console.error('task stop (letter)')
      // 没有返回有效值等一下再接着请求
      await sleepFun(getRandomNumber(2, 3) * 1000)
      return true
    }
  })
}

// --------------------------私信相关函数-------------------------
// letter填充私信信息
async function fillInLetterMessage(signal, creator, message, sendResponse) {
  try {
    // 获取输入框
    const messageInputContainer = document.querySelector('#im_sdk_chat_input')
    const messageInput =
      messageInputContainer && messageInputContainer.querySelector('textarea')
    const sendMessage = message.replace('[[InfluencerName]]', creator)
    letterRunning &&
      (await simulateInput(signal, 'number', messageInput, sendMessage, 2000))
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'fill in letter message error') // 抛出异常
  }

  signal.addEventListener('abort', () => {
    throw new Error('Task close') // 抛出异常
  })
}

// letter填充图片信息
async function fillInLetterImage(signal, imageList, sendResponse) {
  try {
    for (let i = 0; i < imageList.length; i++) {
      const base64String = imageList[i].image
      const blobImage = base64ToBlob(base64String)
      const file = new File([blobImage], 'current-image.png', {
        type: blobImage.type
      })

      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)

      const inputDom = document.querySelector('input[type=file]')
      inputDom.files = dataTransfer.files
      letterRunning && (await simulateChange(signal, inputDom))

      let imageModal = document.querySelector(
        '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
      )

      while (imageModal && letterRunning) {
        const okBtn = document.querySelector(
          '.arco-btn.arco-btn-primary.arco-btn-size-large.arco-btn-shape-square'
        )
        letterRunning && (await openDebuggerToClick(okBtn))
        imageModal = document.querySelector(
          '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
        )
        if (!letterRunning) {
          break
        }
      }

      // 等图片发送成功再进行下一步
      // 给消息框一个加载的时间
      await sleepFun(2000)
      let bodyList = document.querySelectorAll('.chatd-message-body')
      while (bodyList && bodyList.length) {
        bodyList = document.querySelectorAll('.chatd-message-body')
        const spin = bodyList[bodyList.length - 1].querySelectorAll(
          '.chatd-icon--spinning'
        )
        if (!spin || spin.length === 0) {
          break
        }
        await sleepFun(1000)
      }

      inputDom.value = ''
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'fill in letter image error') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    letterRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// letter发送商品图片
async function fillInProductLink(signal, productList, sendResponse) {
  try {
    // 点击商品卡片
    const tabList = document.querySelectorAll('.arco-tabs-header-title')
    for (let i = 0; i < tabList.length; i++) {
      const tabItem = tabList[i].innerText
      if (tabItem.includes('Products')) {
        await openDebuggerToClick(tabList[i])
        break
      }
    }

    for (let index = 0; index < productList.length; index++) {
      const productId = productList[index].id

      if (productId) {
        // 输入框
        let inputContainer = document.querySelector('.m4b-input-group-select')
        while (!inputContainer && letterRunning) {
          await sleepFun(1000)
          // 点击商品卡片
          const tabList = document.querySelectorAll('.arco-tabs-header-title')
          for (let i = 0; i < tabList.length; i++) {
            const tabItem = tabList[i].innerText
            if (tabItem.includes('Products')) {
              await openDebuggerToClick(tabList[i])
              break
            }
          }
          inputContainer = document.querySelector('.m4b-input-group-select')
          if (!letterRunning) {
            break
          }
        }
        const typeSelect = inputContainer.querySelector(
          '.arco-select.arco-select-single.arco-select-size-default.m4b-select.m4b-new-select'
        )
        const inputEle = inputContainer
          .querySelector('.arco-input-inner-wrapper')
          .querySelector('.arco-input.arco-input-size-default')
        // const searchBtn = inputContainer.querySelector('.arco-input-group-suffix')
        // 选择ProductId
        await openDebuggerToClick(typeSelect)
        let typeSelectContainer = document.querySelector(
          '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
        )
        while (!typeSelectContainer && letterRunning) {
          await sleepFun(1000)
          letterRunning && (await openDebuggerToClick(typeSelect))
          typeSelectContainer = document.querySelector(
            '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
          )
          if (!letterRunning) {
            break
          }
        }
        const typeList = typeSelectContainer.querySelectorAll(
          '.arco-select-option.m4b-select-option'
        )
        if (typeList && typeList.length) {
          for (const typeItem of typeList) {
            if (typeItem.textContent.includes('ID')) {
              letterRunning && (await simulateFocus(signal, typeItem))
              letterRunning && (await openDebuggerToClick(typeItem))
              letterRunning && (await simulateFocusOut(signal, typeItem))
            }
          }
        }

        // 填写id并搜索
        while (!inputEle.value && letterRunning) {
          letterRunning && (await simulateFocus(signal, inputEle))
          letterRunning &&
            (await simulateInput(signal, 'number', inputEle, productId))
          letterRunning &&
            (await simulateKeyPress(signal, 'Enter', 13, inputEle))
          if (!letterRunning) {
            break
          }
        }

        // 点击搜索
        const searchBtn = inputContainer.querySelector(
          '.arco-icon.arco-icon-search'
        )
        await openDebuggerToClick(searchBtn)

        await sleepFun(2000)

        let drawContainer = document.querySelector(
          '.arco-tabs.arco-tabs-horizontal.arco-tabs-line.arco-tabs-top.arco-tabs-size-default.m4b-tabs'
        )
        let intervalTimer = null
        let times = 0
        const getTableList = () => {
          return new Promise((resolve, reject) => {
            intervalTimer = workerTimer.setInterval(() => {
              if (times > 20) {
                workerTimer.clearInterval(intervalTimer)
                reject('no find draw')
              }
              drawContainer = document.querySelector(
                '.arco-tabs.arco-tabs-horizontal.arco-tabs-line.arco-tabs-top.arco-tabs-size-default.m4b-tabs'
              )
              const tableList = drawContainer.querySelector(
                '.arco-spin-children'
              )
              if (tableList) {
                workerTimer.clearInterval(intervalTimer)
                resolve(tableList)
              }
              times++
            }, 1000)
          }).catch(() => {
            workerTimer.clearInterval(intervalTimer)
          })
        }
        await getTableList()
        // 点击商品卡
        let tableContainer = document.querySelector(
          '.arco-tabs.arco-tabs-horizontal.arco-tabs-line.arco-tabs-top.arco-tabs-size-default.m4b-tabs'
        )
        let tableList =
          tableContainer &&
          tableContainer.querySelectorAll(
            '.cursor-pointer.bg-white.flex.items-start.relative'
          )

        for (let i = 0; i < tableList.length; i++) {
          const id = tableList[i]
            .querySelector('.text-neutral-text4.mb-2')
            .innerHTML.slice(3)
          if (id === productId) {
            await simulateFocus(signal, tableList[i])
            letterRunning && (await simulateHover(signal, tableList[i]))
            await simulateFocusOut(signal, tableList[i])
            // send按钮
            // const sendBtn = tableList[i].querySelector(
            //   '.arco-btn.arco-btn-text.arco-btn-size-small.index-module__sendButton--Qq1NZ.arco-btn-primary-text'
            // )
            const sendBtn = tableList[i].querySelector(
              '.arco-btn.arco-btn-text.arco-btn-size-small.arco-btn-primary-text'
            )
            sendBtn.style.opacity = 1
            await simulateFocus(signal, sendBtn)
            letterRunning && (await openDebuggerToClick(sendBtn))
            await simulateFocusOut(signal, sendBtn)
            break
          }
        }
        letterRunning && (await simulateInput(signal, 'number', inputEle, ''))
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'fill in product link error') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    throw new Error('Task close') // 抛出异常
  })
}

// letter 发送定向合作
async function fillInCollaborationsList(
  signal,
  collaborationsList,
  sendResponse
) {
  try {
    // 点击定向合作
    const tabList = document.querySelectorAll('.arco-tabs-header-title')
    for (let i = 0; i < tabList.length; i++) {
      const tabItem = tabList[i].innerText
      if (tabItem.includes('Target collaborations')) {
        await openDebuggerToClick(tabList[i])
        break
      }
    }

    for (let index = 0; index < collaborationsList.length; index++) {
      const collaborationId = collaborationsList[index].id

      if (collaborationId) {
        // 是否定位到Target collaborations页面
        let tabContainer = document.querySelector(
          '.arco-tabs-content-item.arco-tabs-content-item-active'
        ).textContent
        while (
          !tabContainer.includes('invitations to collaborate') &&
          letterRunning
        ) {
          await sleepFun(1000)
          // 点击商品卡片
          const tabList = document.querySelectorAll('.arco-tabs-header-title')
          for (let i = 0; i < tabList.length; i++) {
            const tabItem = tabList[i].innerText
            if (tabItem.includes('Target collaborations')) {
              await openDebuggerToClick(tabList[i])
              break
            }
          }
          tabContainer = document.querySelector(
            '.arco-tabs-content-item.arco-tabs-content-item-active'
          ).textContent
          if (!letterRunning) {
            break
          }
        }
        const inputContainer = document
          .querySelector(
            '.arco-tabs-content-item.arco-tabs-content-item-active'
          )
          .querySelector('.m4b-input-group-select')
        const typeSelect = inputContainer.querySelector(
          '.arco-select.arco-select-single.arco-select-size-default.m4b-select.m4b-new-select'
        )
        const inputEle = inputContainer
          .querySelector('.arco-input-inner-wrapper')
          .querySelector('.arco-input.arco-input-size-default')
        // 选择Id
        await openDebuggerToClick(typeSelect)
        let typeSelectContainer = document.querySelector(
          '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
        )
        while (!typeSelectContainer && letterRunning) {
          await sleepFun(1000)
          letterRunning && (await openDebuggerToClick(typeSelect))
          typeSelectContainer = document.querySelector(
            '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
          )
          if (!letterRunning) {
            break
          }
        }
        const typeList = typeSelectContainer.querySelectorAll(
          '.arco-select-option.m4b-select-option'
        )
        if (typeList && typeList.length) {
          for (const typeItem of typeList) {
            if (typeItem.textContent.includes('ID')) {
              letterRunning && (await simulateFocus(signal, typeItem))
              letterRunning && (await openDebuggerToClick(typeItem))
              letterRunning && (await simulateFocusOut(signal, typeItem))
            }
          }
        }

        //  填写id并搜索
        while (!inputEle.value && letterRunning) {
          letterRunning && (await simulateFocus(signal, inputEle))
          letterRunning &&
            (await simulateInput(signal, 'number', inputEle, collaborationId))
          letterRunning &&
            (await simulateKeyPress(signal, 'Enter', 13, inputEle))
          if (!letterRunning) {
            break
          }
        }

        // 点击搜索
        const searchBtn = inputContainer.querySelector(
          '.arco-icon.arco-icon-search'
        )
        await openDebuggerToClick(searchBtn)

        await sleepFun(2000)

        let drawContainer = document.querySelector(
          '.arco-tabs-content-item.arco-tabs-content-item-active'
        )
        let intervalTimer = null
        let times = 0
        const getTableList = () => {
          return new Promise((resolve, reject) => {
            intervalTimer = workerTimer.setInterval(() => {
              if (times > 20) {
                workerTimer.clearInterval(intervalTimer)
                reject('no find draw')
              }
              drawContainer = document.querySelector(
                '.arco-tabs-content-item.arco-tabs-content-item-active'
              )
              const tableList = drawContainer.querySelector(
                '.arco-spin-children'
              )
              if (tableList) {
                workerTimer.clearInterval(intervalTimer)
                resolve(tableList)
              }
              times++
            }, 1000)
          }).catch(() => {
            workerTimer.clearInterval(intervalTimer)
          })
        }
        await getTableList()
        // 点击合作
        let tableContainer = document.querySelector(
          '.arco-tabs-content-item.arco-tabs-content-item-active'
        )
        let tableList =
          tableContainer &&
          tableContainer.querySelectorAll(
            '.rounded-4.border-neutral-line1.bg-white.text-body-m-regular'
          )

        for (let i = 0; i < tableList.length; i++) {
          const id = tableList[i]
            .querySelector('.flex.items-center.text-neutral-text4.mb-12')
            .firstElementChild.innerHTML.slice(3)
          if (id === collaborationId) {
            // send按钮
            const sendBtn = tableList[i].querySelector(
              '.arco-btn.arco-btn-outline.arco-btn-size-mini.arco-btn-shape-square.m4b-button'
            )
            await simulateFocus(signal, sendBtn)
            letterRunning && (await openDebuggerToClick(sendBtn))
            await simulateFocusOut(signal, sendBtn)
            break
          }
        }
        letterRunning && (await simulateInput(signal, 'number', inputEle, ''))
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'fill in product link error') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    throw new Error('Task close') // 抛出异常
  })
}

// letter发送私信
async function sendLetter(signal, sendResponse) {
  try {
    // 滚动到最右侧
    const scrollContainer = document.querySelector('.imContainer-r6x8Da')
    await new Promise(resolve => {
      requestAnimationFrame(() => {
        resolve(scrollContainer.scrollWidth)
      })
    })
    if (scrollContainer.scrollWidth > scrollContainer.clientWidth) {
      while (scrollContainer.scrollLeft === 0 && letterRunning) {
        scrollContainer.scrollLeft =
          scrollContainer.scrollWidth - scrollContainer.clientWidth
      }
    }
    // 点击发送按钮
    const messageInputContainer = document.querySelector('#im_sdk_chat_input')
    const messageInput = messageInputContainer
      ? messageInputContainer.querySelector('textarea')
      : null
    if (!messageInput) return
    let running = true
    // workbench-trigger-button
    // 判断是否发送成功如果没有发送成功再次触发
    while (messageInput.value && running && letterRunning) {
      const sendBtn = document.querySelector(
        '.arco-btn.arco-btn-primary.arco-btn-size-small.arco-btn-shape-square.m4b-button'
      )
      // 先聚焦再点击
      await simulateFocus(signal, sendBtn)
      letterRunning && (await openDebuggerToClick(sendBtn))
      signal.addEventListener('abort', () => {
        running = false
        letterRunning = false
        throw new Error('Task close') // 抛出异常
      })
      if (!letterRunning) {
        break
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'click send error') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    letterRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

//  ---------------------------manage相关操作-----------------------------
async function fillInManageFilterLabel(signal, filtersLabel, sendResponse) {
  const filterContainer = document.querySelectorAll(
    '.bg-neutral-bg4.pt-24.pb-16.px-24.rounded-8'
  )[0]

  const filterLabelDiv = document.createElement('div')
  filterLabelDiv.style.display = 'flex'
  filterLabelDiv.style.flexWrap = 'wrap'
  filterLabelDiv.style.marginTop = '10px'

  const filterSelectSpan = document.createElement('div')
  filterSelectSpan.innerHTML = 'Selected:  '
  filterSelectSpan.style.marginRight = '5px'
  filterSelectSpan.style.marginTop = '5px'
  filterLabelDiv.appendChild(filterSelectSpan)

  Object.keys(filtersLabel).forEach(item => {
    const filterItem = filtersLabel[item]
    const filterItemDiv = document.createElement('div')
    filterItemDiv.innerHTML = `${filterItem.label}: ${filterItem.value}`
    filterItemDiv.style.padding = '2px 5px'
    // filterItemDiv.style.border = '1px solid #fa6300'
    filterItemDiv.style.borderRadius = '5px'
    filterItemDiv.style.fontSize = '14px'
    filterItemDiv.style.marginRight = '5px'
    filterItemDiv.style.marginTop = '5px'
    filterItemDiv.style.color = '#323b4b'
    filterItemDiv.style.backgroundColor = 'rgba(0, 0, 0, .05)'
    filterLabelDiv.appendChild(filterItemDiv)
  })

  filterLabelDiv.style.marginTop = '10px'

  filterContainer.appendChild(filterLabelDiv)

  sendResponse({ code: 200, message: 'select manage filter success' })

  signal.addEventListener('abort', async () => {
    inviteRunning = false
    letterRunning = false
    // 恢复原样
    sendResponse({
      code: 505,
      message: 'Task close'
    })
  })
}
// 获取Manage页面上的网红列表
async function getManageCreatorList(
  params,
  mainTabId,
  backgroundParams,
  signal
) {
  const {
    num = 3,
    filters = null,
    currentPage = 1,
    offset = 0,
    page = '',
    reqGap = 2
  } = params
  const creatorListInfo = []
  const otherParams = {
    currentPage: currentPage || 1,
    offset: offset || 0,
    getAllData: false,
    firstPage: deepClone(currentPage || 1),
    firstOffset: deepClone(offset || 0),
    page,
    mainTabId,
    reqGap
  }

  await getEnoughManageCreator(
    signal,
    num,
    creatorListInfo,
    filters,
    otherParams
  ).catch(e => {
    console.log('get manage creatorList error', e.toString())
    chrome.runtime.sendMessage({
      action: 'getManageCreatorListResult',
      result: {
        code: e.toString().includes('Task close') ? 505 : 500,
        message: `${e.toString()} - get creator error`
      },
      backgroundParams: backgroundParams
    })
  })
  console.log('获取的manage页面的网红是', creatorListInfo)
  addProcessLoadingTest('成功获取页面数据')
  workerTimer.setTimeout(() => {
    chrome.runtime.sendMessage({
      action: 'getManageCreatorListResult',
      result: {
        code: 200,
        message: 'get manage creator success',
        data: {
          creatorListInfo: creatorListInfo,
          currentPage: otherParams.currentPage,
          offset: otherParams.offset || 0,
          getAllData: otherParams.getAllData
        }
      },
      backgroundParams: backgroundParams
    })
  }, 2000)
}

// 获取manage create列表
// creatorListInfo 用来存储获取网红的数据结果
async function getEnoughManageCreator(
  signal,
  num,
  creatorListInfo = [],
  filters = null,
  otherParams = {
    currentPage: 1,
    offset: 0,
    getAllData: false,
    reqGap: 2
  }
) {
  try {
    // currentPage是当前请求的网红页码，可变； firstPage是当次获取网红的第一个页码，不可变
    const {
      firstPage = 1,
      firstOffset = 0,
      mainTabId = 0,
      reqGap = 2
    } = otherParams

    if (num > creatorListInfo.length) {
      // 调接口获取用户数据
      const tabUrl = window.location.origin
      let findUrl = `${tabUrl}/api/v1/oec/affiliate/crm/creator/list?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`

      let params = {
        page_no: otherParams.currentPage,
        page_size: 20,
        sorter: {},
        filter: filters || {}
      }

      // 直接从接口获取到的网红
      let targetCreatorList = null
      // 格式化后的网红包含id和name
      let originCreatorListInfo = []
      // 下一页内容
      let nextPageRes = null
      if (!manageRunning) {
        return
      }

      const sendFindReq = async () => {
        await sleepFun(getRandomNumber(reqGap, reqGap + 1) * 1000)
        return postRequest(findUrl, { ...params }, 5, {
          successCode: 0,
          codeKey: 'code'
        }).then(data => {
          if (data.code === 0) {
            const result = data.data
            targetCreatorList = result.creators

            if (targetCreatorList && targetCreatorList.length) {
              targetCreatorList.forEach(creatorItem => {
                // able不可用标记
                const name = creatorItem.base.handle_name
                const id = creatorItem.base.oec_id
                originCreatorListInfo.push({
                  name,
                  id,
                  able: creatorItem.ec_info.with_ec_permission
                })
              })
            }

            otherParams.currentPage = otherParams.currentPage + 1
            otherParams.offset = 0
            if (params.page_size * params.page_no < result.total) {
              nextPageRes = {
                page_no: params.page_no + 1,
                page_size: 20
              }
            } else {
              nextPageRes = null
            }
          } else {
            return new Promise((resolve, reject) => {
              reject('task stop (manage search)')
            })
          }
        })
      }

      addProcessLoadingTest('查找网红...')
      // 获取上次存储的数据如果page一样从存储数据中获取数据
      await new Promise((resolve, reject) => {
        chrome.runtime.sendMessage(
          {
            action: 'getStoreValue',
            mainTabId: mainTabId,
            keyWord: 'beforeFindManageData'
          },
          async function (storeResponse) {
            try {
              const { storeValue } = storeResponse
              console.log(
                '获取store存储数据======beforeFindManageData',
                storeValue,
                otherParams
              )
              if (storeValue) {
                const { currentPage } = storeValue
                if (currentPage === otherParams.currentPage) {
                  originCreatorListInfo = storeValue.originCreatorListInfo
                  nextPageRes = storeValue.nextPageRes
                  targetCreatorList = storeValue.targetCreatorList
                  otherParams.currentPage = otherParams.currentPage + 1
                  otherParams.offset = 0
                } else {
                  await sendFindReq()
                }
              } else {
                await sendFindReq()
              }
              resolve()
            } catch (e) {
              reject(e)
            }
          }
        )
      })

      for (let index = 0; index < originCreatorListInfo.length; index++) {
        if (!inviteRunning && !letterRunning) return
        const creatorItem = originCreatorListInfo[index]

        // 更新creatorList，offset,currentPage
        const creatorListName = creatorListInfo.map(item => item.name)
        if (
          creatorListInfo.length < num &&
          !creatorListName.includes(creatorItem.name)
        ) {
          if (otherParams.currentPage - 1 === firstPage) {
            if (index > firstOffset || index === firstOffset) {
              creatorItem.able && creatorListInfo.push(creatorItem)
            }
          } else {
            creatorItem.able && creatorListInfo.push(creatorItem)
          }
        } else if (creatorListInfo.length === num) {
          addProcessLoadingTest('已获取目标个数网红')
          otherParams.offset = index
          otherParams.currentPage = otherParams.currentPage - 1

          // 获取成功，存储数据
          chrome.runtime.sendMessage({
            action: 'updateStoreValue',
            mainTabId: mainTabId,
            keyWord: 'beforeFindManageData',
            value: {
              currentPage: otherParams.currentPage,
              originCreatorListInfo: originCreatorListInfo,
              targetCreatorList: targetCreatorList,
              nextPageRes: nextPageRes
            }
          })
          break
        }
      }
      // nextPageRes = null说明已经获取到最后一页数据，若此时otherParams.offset = 0说明最后一页数据也没有遗留，证明数据获取完了
      if (!nextPageRes && otherParams.offset === 0)
        otherParams.getAllData = true
      else if (!targetCreatorList && !nextPageRes) otherParams.getAllData = true
      else otherParams.getAllData = false
    }

    console.log(
      'otherParams.getAllData===',
      otherParams.getAllData,
      creatorListInfo
    )
    // 更新参数
    chrome.runtime.sendMessage({
      action: 'updateStoreValue',
      mainTabId: mainTabId,
      keyWord: 'currentManageNextPage',
      value: {
        currentPage: otherParams.currentPage,
        offset: otherParams.offset
      }
    })
    if (!otherParams.getAllData && num > creatorListInfo.length) {
      await getEnoughManageCreator(
        signal,
        num,
        creatorListInfo,
        filters,
        otherParams
      ).catch(e => {
        throw new Error(e) // 抛出异常
      })
    }
  } catch (e) {
    console.log('获取创作者捕获到的错误', e)
    throw new Error(e || 'get creator err') // 抛出异常
  }

  signal.addEventListener('abort', () => {
    manageRunning = false
    throw new Error('Task close') // 抛出异常
  })
}

// 去批量私信
async function newTabToManageLetter(
  params,
  backgroundParams,
  signal,
  sendResponse
) {
  try {
    const {
      manageParams: { manageLetter }
    } = params
    const { messageType, title, message, imageList, productCardList } =
      manageLetter
    // 寻找标识dom确保页面加载完成
    let selectContainer = document.querySelector(
      '.arco-row.arco-row-align-start.arco-row-justify-start.m4b-row'
    )
    await new Promise((resolve, reject) => {
      let timers = 0
      let selectTimer = workerTimer.setInterval(async () => {
        // 获取页面的header
        selectContainer = document.querySelector(
          '.arco-row.arco-row-align-start.arco-row-justify-start.m4b-row'
        )
        if (selectContainer) {
          workerTimer.clearInterval(selectTimer)
          resolve('success')
        }
        if (timers >= 20) {
          workerTimer.clearInterval(selectTimer)
          reject('no found manage letter selectContainer') // 抛出异常
        }
        timers++
      }, 1000)
    })

    // 填写消息类型
    const messageTypeList = selectContainer.querySelectorAll(
      '.arco-radio.w-full.m4b-radio'
    )
    const targetLabel = messageType === 'image' ? 'Image' : 'Product cards'
    for (let i = 0; i < messageTypeList.length; i++) {
      const selectItemLabel = messageTypeList[i].innerText
      if (selectItemLabel.includes(targetLabel)) {
        await simulateClick(signal, messageTypeList[i])
        break
      }
    }

    // 填写消息标题
    const messageTitleInput = document.querySelector(
      '.arco-input.arco-input-size-default'
    )
    manageRunning &&
      (await simulateInput(signal, 'number', messageTitleInput, title))
    manageRunning &&
      (await simulateKeyPress(signal, 'Enter', 13, messageTitleInput))

    // 填写消息内容
    const messageContentInput = document.querySelector(
      '.arco-textarea.m4b-input-textarea.m4b-input-textarea-large.m4b-input-textarea-word-limit'
    )
    manageRunning &&
      (await simulateInput(signal, 'number', messageContentInput, message))
    manageRunning &&
      (await simulateKeyPress(signal, 'Enter', 13, messageContentInput))

    // 填写图片或商品卡
    if (messageType === 'image') {
      manageRunning &&
        (await fillInManageLetterImageList(signal, imageList, sendResponse))
    } else {
      manageRunning &&
        (await fillInManageLetterProductCard(
          signal,
          productCardList,
          sendResponse
        ))
    }

    // 点击发送按钮
    let sendBtn = null
    const buttonList = document.querySelectorAll(
      '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
    )
    for (let i = 0; i < buttonList.length; i++) {
      const buttonItemLabel = buttonList[i].textContent
      if (buttonItemLabel.includes('Send message')) {
        sendBtn = buttonList[i]
      }
    }
    if (sendBtn.classList.contains('arco-btn-disabled')) {
      throw new Error('请完善私信信息')
    } else {
      manageRunning && (await simulateClick(signal, sendBtn))
    }

    // 有个二次确认的弹窗处理下
    let modal = document.querySelector(
      '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
    )
    while (!modal) {
      modal = document.querySelector(
        '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
      )
      await sleepFun(2000)
    }
    const okBtn = modal.querySelector(
      '.arco-btn.arco-btn-primary.arco-btn-size-large.arco-btn-shape-square'
    )
    manageRunning && okBtn && (await simulateClick(signal, okBtn))

    // 判断点击ok后是否还有弹窗如果有的话不关闭当前tab
    await sleepFun(2000)
    let modal2 = document.querySelector(
      '.arco-modal.m4b-modal.m4b-modal-size-default.zoomModal-appear-done.zoomModal-enter-done'
    )

    chrome.runtime.sendMessage({
      domainUrl: window.location.hostname,
      action: 'newTabToManageLetterResult',
      result: {
        code: modal2 ? 201 : 200,
        message: 'batch message success'
      },
      backgroundParams: backgroundParams
    })
  } catch (e) {
    manageRunning = false
    chrome.runtime.sendMessage({
      domainUrl: window.location.hostname,
      action: 'newTabToManageLetterResult',
      result: {
        code: 500,
        message: e.toString() || 'batch message error'
      },
      backgroundParams: backgroundParams
    })
  }

  // 停止任务检测
  if (signal) {
    signal.addEventListener('abort', () => {
      manageRunning = false
    })
  }
}

const fillInManageLetterProductCard = async (
  signal,
  productCardList,
  sendResponse
) => {
  try {
    // 点击添加按钮
    const buttonList = document.querySelectorAll(
      '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
    )
    for (let i = 0; i < buttonList.length; i++) {
      const buttonItemLabel = buttonList[i].textContent
      if (buttonItemLabel.includes('Add product')) {
        await simulateClick(signal, buttonList[i])
      }
    }
    // 输入框
    const inputContainer = document
      .querySelector('.flex.items-center.space-x-12.mb-16')
      .querySelector('.m4b-input-group-select')
    const typeSelect = inputContainer.querySelector(
      '.arco-select.arco-select-single.arco-select-size-default.m4b-select.m4b-new-select'
    )
    const inputEle = inputContainer
      .querySelector('.arco-input-inner-wrapper')
      .querySelector('.arco-input.arco-input-size-default')
    // const searchBtn = inputContainer.querySelector('.arco-input-group-suffix')
    // 选择ProductId
    inviteRunning && (await simulateClick(signal, typeSelect))
    let typeSelectContainer = document.querySelector(
      '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
    )
    let typeSelectNumber = 0
    while (!typeSelectContainer) {
      if (typeSelectNumber >= 10) {
        throw new Error('find manage typeSelectContainer error')
      }
      await sleepFun(1000)
      inviteRunning && (await simulateClick(signal, typeSelect))
      typeSelectContainer = document.querySelector(
        '.arco-select-popup.m4b-select-popup.m4b-new-select-popup'
      )
      typeSelectNumber++
    }
    const typeList = typeSelectContainer.querySelectorAll(
      '.arco-select-option.m4b-select-option'
    )
    if (typeList && typeList.length) {
      for (const typeItem of typeList) {
        if (typeItem.textContent.includes('Product ID')) {
          inviteRunning && (await simulateClick(signal, typeItem))
        }
      }
    }

    const productIdList = productCardList.map(item => item.id)
    for (let i = 0; i < productIdList.length; i++) {
      const id = productIdList[i]
      // 填写id并搜索
      while (!inputEle.value && inviteRunning) {
        await simulateFocus(signal, inputEle)
        await simulateInput(signal, 'number', inputEle, id)
        await simulateKeyPress(signal, 'Enter', 13, inputEle)
      }

      // 选择产品
      let drawContainer = document.querySelector(
        '.arco-drawer.m4b-drawer.slideRight-enter-done'
      )
      const getTableList = () => {
        let intervalTimer = null
        return new Promise(resolve => {
          intervalTimer = workerTimer.setInterval(() => {
            drawContainer = document.querySelector(
              '.arco-drawer.m4b-drawer.slideRight-enter-done'
            )
            const tableList =
              drawContainer && drawContainer.querySelector('.arco-table-tr')
            if (tableList) {
              workerTimer.clearInterval(intervalTimer)
              resolve(tableList)
            }
          }, 1000)
        }).catch(() => {
          workerTimer.clearInterval(intervalTimer)
        })
      }
      await getTableList()
      let tableContainer = drawContainer.querySelector('.arco-table-container')
      const tableList = tableContainer
        .querySelector('tbody')
        .querySelectorAll('.arco-table-tr')

      for (const tableItem of tableList) {
        const productId = tableItem.querySelector(
          '.text-neutral-text3.text-body-s-regular'
        )
          ? tableItem.querySelector('.text-neutral-text3.text-body-s-regular')
              .textContent
          : ''

        if (productId && productId.includes(id)) {
          const selectBtn = tableItem.querySelector('.arco-checkbox')
          manageRunning && (await simulateClick(signal, selectBtn))
        }
      }

      // 清空inputValue
      await simulateInput(signal, 'number', inputEle, '')
    }

    // 点击add按钮
    // 判断是否可以点击
    const selectNumber = Number(
      document
        .querySelector('.arco-drawer-footer')
        .querySelector('.text-neutral-text3.text-body-m-regular')
        .querySelector('.text-primary-normal.text-body-m-medium').textContent
    )
    if (selectNumber && selectNumber > 0) {
      let addBtn = null
      while (!addBtn && manageRunning) {
        const btnList = document.querySelectorAll(
          '.arco-btn.arco-btn-primary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
        )
        for (const btnItem of btnList) {
          if (btnItem.textContent.includes('Add')) {
            addBtn = btnItem
          }
        }
        await sleepFun(1000)
      }
      manageRunning && (await simulateClick(signal, addBtn))
    } else {
      // 如果add不能点击点击cancel按钮
      const cancelBtnList = document.querySelectorAll(
        '.arco-btn.arco-btn-secondary.arco-btn-size-default.arco-btn-shape-square.m4b-button'
      )
      let cancelBtn = null
      for (const btnItem of cancelBtnList) {
        if (btnItem.textContent.includes('Cancel')) {
          cancelBtn = btnItem
        }
      }
      manageRunning && (await simulateClick(signal, cancelBtn))
    }
  } catch (e) {
    console.log('e=========', e)
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    manageRunning = false
    throw new Error('Task close') // 抛出异常
  })
}
const fillInManageLetterImageList = async (signal, imageList, sendResponse) => {
  try {
    for (let i = 0; i < imageList.length; i++) {
      const base64String = imageList[i].image
      const blobImage = base64ToBlob(base64String)
      const file = new File([blobImage], 'current-image.png', {
        type: blobImage.type
      })

      const dataTransfer = new DataTransfer()
      dataTransfer.items.add(file)

      const inputDom = document.querySelector('input[type=file]')
      inputDom.files = dataTransfer.files
      manageRunning && (await simulateChange(signal, inputDom))

      // 确保图片加载成功
      let iconsEyes = document.querySelector(
        '.arco-icon.arco-icon-eye.m4b-upload__operation-icon'
      )
      while (!iconsEyes) {
        iconsEyes = document.querySelector(
          '.arco-icon.arco-icon-eye.m4b-upload__operation-icon'
        )
        await sleepFun(2000)
      }
    }
  } catch (e) {
    sendResponse({
      code: 500,
      message: e.toString()
    })
    throw new Error(e || 'running err') // 抛出异常
  }
  signal.addEventListener('abort', () => {
    manageRunning = false
    throw new Error('Task close') // 抛出异常
  })
}
// -------------------------------------创作者loading样式 ---------------------
function addProcessLoadingTest(string) {
  const loadingDiv = document.querySelector('#creator-loading-container')
  if (loadingDiv) {
    loadingDiv.textContent = `${string}`
  } else {
    const loadingContainer = document.createElement('div')
    loadingContainer.id = 'creator-loading-container'
    loadingContainer.textContent = `${string}`
    loadingContainer.style.background = '#fa6300'
    loadingContainer.style.borderRadius = '5px'
    loadingContainer.style.color = '#ffffff'
    loadingContainer.style.fontWeight = 600
    loadingContainer.style.position = 'absolute'
    loadingContainer.style.top = '80px'
    loadingContainer.style.right = '10px'
    loadingContainer.style.padding = '5px 10px'

    const mainContainer = document.querySelector('main')
    mainContainer.appendChild(loadingContainer)
  }
}
function addCreatorCheckLoading(name) {
  const loadingDiv = document.querySelector('#creator-loading-container')
  if (loadingDiv) {
    loadingDiv.textContent = `正在检索${name}...`
  } else {
    const loadingContainer = document.createElement('div')
    loadingContainer.id = 'creator-loading-container'
    loadingContainer.textContent = `正在检索${name}...`
    loadingContainer.style.background = '#fa6300'
    loadingContainer.style.borderRadius = '5px'
    loadingContainer.style.color = '#ffffff'
    loadingContainer.style.fontWeight = 600
    loadingContainer.style.position = 'absolute'
    loadingContainer.style.top = '80px'
    loadingContainer.style.right = '10px'
    loadingContainer.style.padding = '5px 10px'

    const mainContainer = document.querySelector('main')
    mainContainer.appendChild(loadingContainer)
  }
}
function clearCreatorLoadingContainer() {
  const loadingDiv = document.querySelector('#creator-loading-container')
  if (!loadingDiv) return
  const mainContainer = document.querySelector('main')
  mainContainer.removeChild(loadingDiv)
}

//  ------------------------------------tool函数-------------------------------
function formatDate(date) {
  // 获取月、日、年
  var month = String(date.getMonth() + 1).padStart(2, '0') // getMonth() 返回 0-11，需要加 1
  var day = String(date.getDate()).padStart(2, '0')
  var year = date.getFullYear()

  // 组合成 MM/DD/YYYY 格式
  return month + '/' + day + '/' + year
}
// 将Base64字符串转换为Blob对象
function base64ToBlob(base64) {
  const mimeType = base64.match(/data:(.*);base64,/)[1]
  const byteCharacters = atob(base64.split(',')[1])
  const byteNumbers = new Array(byteCharacters.length)
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i)
  }
  const byteArray = new Uint8Array(byteNumbers)
  return new Blob([byteArray], { type: mimeType })
}
// sleep函数
async function sleepFun(time) {
  await new Promise(resolve => {
    workerTimer.setTimeout(() => {
      resolve()
    }, time)
  })
}
// 深拷贝
function deepClone(obj) {
  if (!obj || typeof obj !== 'object') {
    return obj
  }
  let copy = Array.isArray(obj) ? [] : {}
  for (let key in obj) {
    // eslint-disable-next-line
    if (obj.hasOwnProperty && obj.hasOwnProperty(key)) {
      copy[key] = deepClone(obj[key]) // 递归拷贝每个属性
    }
  }
  return copy
}
// min-max的随机数
function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 获取url参数
function getQueryParams(url) {
  const queryPosition = url.indexOf('?')
  var queryString = url.slice(queryPosition + 1)
  var queryArray = queryString.split('&')
  const queryParams = {}

  queryArray.forEach(function (param) {
    var pair = param.split('=')
    var key = decodeURIComponent(pair[0])
    var value = decodeURIComponent(pair[1] || '')
    queryParams[key] = value
  })

  return queryParams
}

//  ----------------------------模拟事件-------------------------
// 点击动作 - click
async function simulateClick(signal, element) {
  // 模拟鼠标轨迹
  if (element) {
    const x = element.getBoundingClientRect().left
    const y = element.getBoundingClientRect().top
    await simulateMouseMove(document.body, 0, 0, x, y)
  }
  return new Promise((resolve, reject) => {
    // 激活页面
    // chrome.runtime.sendMessage(
    //   {
    //     action: 'activeCurrentTab',
    //     mainTabId: currentTabId
    //   },
    //   async function () {
    // 点击
    element && element.click()
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve()
      } else {
        reject(new Error('element not found - click'))
      }
    }, operateGap) // 模拟点击操作需要 operateGap 时间
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
    //   }
    // )
  })
}
// 点击事件 - click
function simulateClickEvent(signal, element) {
  return new Promise((resolve, reject) => {
    // 创建点击事件
    const clickEvent = new Event('click', {
      bubbles: true,
      cancelable: true
    })
    element && element.dispatchEvent(clickEvent)
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - click'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}
// input事件
function simulateInput(signal, type, element, value, time) {
  return new Promise((resolve, reject) => {
    let currentIndex = 0
    let inputRun = true
    function typeCharacter() {
      if (currentIndex < value.length) {
        element.value += value[currentIndex].toString()

        currentIndex++
        // 创建并触发 input 事件
        const inputEvent = new Event('input', {
          bubbles: true,
          cancelable: true, // 使事件可取消
          composed: true
        })
        element.dispatchEvent(inputEvent)

        const timeoutTimer = workerTimer.setTimeout(() => {
          workerTimer.clearTimeout(timeoutTimer)
          inputRun && typeCharacter()
        }, 100)
      } else {
        // 完成输入后触发 change 事件
        const changeEvent = new Event('change', {
          bubbles: true,
          cancelable: true, // 使事件可取消
          composed: true
        })
        element.dispatchEvent(changeEvent)
        resolve()
      }
    }

    let timer = null
    if (type === 'number') {
      element && (element.value = value)
      // 创建并触发 input 事件
      const inputEvent = new Event('input', {
        bubbles: true, // 使事件向上冒泡
        cancelable: true, // 使事件可取消
        composed: true
      })
      element.dispatchEvent(inputEvent)
      // 完成输入后触发 change 事件
      const changeEvent = new Event('change', {
        bubbles: true,
        cancelable: true, // 使事件可取消
        composed: true
      })
      element.dispatchEvent(changeEvent)
      timer = workerTimer.setTimeout(() => {
        if (element) {
          resolve()
        } else {
          reject(new Error('element not found - input'))
        }
      }, time || operateGap)
    } else {
      if (element) {
        element.value = ''
        // 开始逐字输入
        element && typeCharacter()
      } else {
        reject(new Error('element not found - input'))
      }
    }

    try {
      signal.addEventListener('abort', () => {
        workerTimer.clearTimeout(timer)
        inputRun = false
        reject('Task close')
      })
    } catch (e) {
      inputRun = false
      console.log('signal err', e)
      reject('signal err')
    }
  })
}
// 模拟 mouseover 事件
function simulateHover(signal, element) {
  return new Promise((resolve, reject) => {
    // 创建键盘事件
    const mouseOverEvent = new Event('mouseover', {
      bubbles: true,
      cancelable: true
    })
    element && element.dispatchEvent(mouseOverEvent)
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - hover'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}

// 模拟 blur 事件
function simulateBlur(signal, element) {
  return new Promise((resolve, reject) => {
    const blurEvent = new Event('blur', {
      bubbles: true,
      cancelable: true
    })
    element && element.dispatchEvent(blurEvent)
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - blur'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}

// 模拟 focus 事件
function simulateFocus(signal, element) {
  return new Promise((resolve, reject) => {
    const focusEvent = new Event('focus', {
      bubbles: true,
      cancelable: true
    })
    const focusInEvent = new Event('focusin', {
      bubbles: true,
      cancelable: true
    })
    element && element.dispatchEvent(focusEvent)
    element && element.dispatchEvent(focusInEvent)

    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - focus'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}
// 模拟 focusout 事件
function simulateFocusOut(signal, element) {
  return new Promise((resolve, reject) => {
    const focusOutEvent = new Event('focusout', {
      bubbles: true,
      cancelable: true
    })

    element && element.dispatchEvent(focusOutEvent)

    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - focusout'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}

// 模拟 mouseChange事件
function simulateChange(signal, element) {
  return new Promise((resolve, reject) => {
    // 创建Change事件
    const changeEvent = new Event('change', {
      bubbles: true,
      cancelable: true
    })
    element && element.dispatchEvent(changeEvent)
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found - change'))
      }
    }, operateGap)
    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}
// 模拟键盘按键
function simulateKeyPress(signal, key, keyCode, element = document) {
  return new Promise((resolve, reject) => {
    // 创建键盘事件
    const event = new KeyboardEvent('keydown', {
      key: key,
      keyCode: keyCode,
      code: key,
      which: keyCode,
      bubbles: true,
      cancelable: true
    })

    element && element.dispatchEvent(event)
    const timer = workerTimer.setTimeout(() => {
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found'))
      }
    }, operateGap)

    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}
// 鼠标滚动事件
// eslint-disable-next-line no-unused-vars
function simulateScroll(signal, scrollY, element = document) {
  return new Promise((resolve, reject) => {
    const timer = workerTimer.setTimeout(() => {
      // 创建滚动事件
      const scrollEvent = new WheelEvent('wheel', {
        bubbles: true,
        cancelable: true,
        deltaY: scrollY // 正值表示向下滚动，负值表示向上滚动
      })
      element && element.dispatchEvent(scrollEvent)
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found'))
      }
    }, operateGap)

    if (signal) {
      try {
        signal.addEventListener('abort', () => {
          workerTimer.clearTimeout(timer)
          reject('Task close')
        })
      } catch (e) {
        console.log('signal err', e)
        reject('signal err')
      }
    }
  })
}

// 鼠标滚动事件
// eslint-disable-next-line no-unused-vars
function simulateScrollByScrollTop(signal, scrollY, element = document) {
  return new Promise((resolve, reject) => {
    const timer = workerTimer.setTimeout(() => {
      // 在当前scrollTop的基础上加1500-2000
      // 创建滚动事件
      element.style.transition = 'transform 0.5s ease'
      element.scrollTop = element.scrollTop + scrollY
      element.dispatchEvent(
        new WheelEvent('wheel', { bubbles: true, deltaY: -scrollY })
      )
      if (element) {
        resolve('success')
      } else {
        reject(new Error('element not found'))
      }
    }, 1000)

    try {
      signal.addEventListener('abort', () => {
        workerTimer.clearTimeout(timer)
        reject('Task close')
      })
    } catch (e) {
      console.log('signal err', e)
      reject('signal err')
    }
  })
}

// 模拟鼠标路径
function simulateMouseMove(
  element = document.body,
  startX,
  startY,
  endX,
  endY,
  steps = 10,
  interval = 100
) {
  return new Promise(resolve => {
    let currentStep = 0
    function moveMouse() {
      if (currentStep <= steps) {
        const progress = currentStep / steps
        const x = startX + (endX - startX) * progress
        const y = startY + (endY - startY) * progress

        const event = new MouseEvent('mousemove', {
          bubbles: true,
          clientX: x,
          clientY: y,
          view: window
        })
        element.dispatchEvent(event)

        currentStep++
        const timer = workerTimer.setTimeout(() => {
          moveMouse()
          workerTimer.clearTimeout(timer)
        }, interval)
      } else {
        resolve()
      }
    }
    // 开始移动
    moveMouse()
  })
}

// -----------------------------injected.js文件注入------------------------------

// 获取从inject.js获取到的响应消息
window.addEventListener('message', function (message) {
  const {
    data: { type, data }
  } = message
  // 检测creator列表更新
  if (type === 'xhr-findcreator') {
    console.log('inject的响应结果是-xhr-findcreator:')
    // 初始化复选框 - 非运行状态下再执行
    initCheckoutClearBtn()
    initCheckout()
  } else if (type === 'xhr-accountinfo') {
    console.log('inject的响应结果是-xhr-accountinfo:', data)
    shopId = data && data.shopId
  } else if (type === 'xhr-createinvite') {
    if (data.code === 0) {
      if (data.data.conflict_list && data.data.conflict_list.length > 0) {
        sendInviteStatus = false
      } else {
        sendInviteStatus = true
      }
    } else {
      sendInviteStatus = false
    }
  } else if (type === 'xhr-search') {
    const resData = JSON.parse(data)
    console.log('inject的响应结果是-search:', resData)
  } else if (type === 'xhr-authprofiles') {
    letterPageLoading = false
    console.log('letter页面加载成功')
  } else if (type === 'xhr-invitation-group-search') {
    const queryData = data.url.slice(data.url.indexOf('?'))
    tokenQuery = queryData
  }
})

// ----- isTrusted测试----------------------

function openDebuggerToClick(element) {
  // 通过谷歌浏览器bug进行添加信任事件
  // window.addEventListener('mousedown', onDownEvent, true)
  // window.addEventListener('mouseup', onUpEvent, true)

  return new Promise((resolve, reject) => {
    const x = element.getBoundingClientRect().left + 5
    const y = element.getBoundingClientRect().top + 5

    const mouseDownFun = e => {
      if (!e.isTrusted) {
        e.preventDefault()
        let obj = { x, y }
        chrome.runtime.sendMessage(
          {
            action: 'mousedownToClick',
            params: obj
          },
          function (response) {
            elementRemoveEventListener()
            if (response.code === 200) {
              resolve('click success')
            } else {
              reject('click fail')
            }
          }
        )
      }
    }
    const elementRemoveEventListener = () => {
      element.removeEventListener('mousedown', mouseDownFun, true)
    }

    // 根据按钮的mousedown事件来触发点击事件
    element.addEventListener('mousedown', mouseDownFun, true)

    // 激活页面
    // chrome.runtime.sendMessage(
    //   {
    //     action: 'activeCurrentTab',
    //     mainTabId: currentTabId
    //   },
    //   async function () {
    // 触发click
    element.dispatchEvent(
      new MouseEvent('mousedown', {
        bubbles: true,
        cancelable: true
      })
    )
    //   }
    // )
  })
}

//  ------------------------------请求封装-------------------------
// eslint-disable-next-line no-unused-vars
const postRequest = (url, data, retries = 5, others) => {
  // // tiktok请求统一添加oec_seller_id参数
  // if (url.includes('tiktok') && shopId) {
  //   if (url.includes('?')) url += `&oec_seller_id=${shopId}`
  //   else url += `?oec_seller_id=${shopId}`
  // }

  return fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(data)
  })
    .then(async response => {
      if (!response || !response.ok) {
        await sleepFun(getRandomNumber(2, 4) * 1000)
        if (retries > 1) {
          return postRequest(url, data, retries - 1)
        } else {
          throw new Error('net error') // 抛出异常
        }
      }
      if (others) {
        const { successCode, codeKey } = others
        const result = await response.json()
        if (
          !result ||
          (!result[codeKey] && result[codeKey] !== 0) ||
          result[codeKey] !== successCode
        ) {
          // 不是成功的值就再次请求
          if (retries > 1) {
            return postRequest(url, data, retries - 1)
          } else {
            return result
          }
        } else {
          return result
        }
      } else {
        return response.json()
      }
    })
    .catch(async () => {
      await sleepFun(1000)
      if (retries > 1) {
        return postRequest(url, data, retries - 1)
      } else {
        throw new Error('net error') // 抛出异常
      }
    })
}
// eslint-disable-next-line no-unused-vars
const getRequest = (url, retries = 5, others) => {
  // // tiktok请求统一添加oec_seller_id参数
  // if (url.includes('tiktok') && shopId) {
  //   if (url.includes('?')) url += `&oec_seller_id=${shopId}`
  //   else url += `?oec_seller_id=${shopId}`
  // }

  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    }
  })
    .then(async response => {
      if (!response || !response.ok) {
        await sleepFun(getRandomNumber(2, 4) * 1000)
        if (retries > 1) {
          return getRequest(url, retries - 1)
        } else {
          throw new Error('net error') // 抛出异常
        }
      }
      if (others) {
        const { successCode, codeKey } = others
        const result = await response.json()
        if (
          !result ||
          (!result[codeKey] && result[codeKey] !== 0) ||
          result[codeKey] !== successCode
        ) {
          // 不是成功的值就再次请求
          if (retries > 1) {
            return getRequest(url, retries - 1)
          } else {
            return result
          }
        } else {
          return result
        }
      } else {
        return response.json()
      }
    })
    .catch(async () => {
      await sleepFun(1000)
      if (retries > 1) {
        return getRequest(url, retries - 1)
      } else {
        throw new Error('net error') // 抛出异常
      }
    })
}

// ---------------------------store存储---------------------------------
// 存储任意变量
const setStoreSignalVar = (key, value) => {
  return chrome.storage.local
    .set({
      [key]: value
    })
    .catch(err => {
      console.log('storage err', err)
    })
}
// 获取任意变量
const getStoreSignalVar = key => {
  return chrome.storage.local
    .get([key])
    .then(store => {
      console.log('Store', store)
      if (store && store[key]) {
        return store[key]
      } else {
        return null
      }
    })
    .catch(err => {
      console.log('storage err', err)
    })
}
