/* global chrome */
// 测试
// const REQUEST_TARGET = 'https://cn--cy.noxinfluencer.com'
// 线上
const REQUEST_TARGET = 'https://www.noxinfluencer.com'

let uid = 0
let isChinese = navigator.language.includes('zh-CN')
let shopRegion = 'US'
let shopId = ''

// 持久化日志信息到 chrome.storage
// eslint-disable-next-line no-unused-vars
function logMessage(message) {
  const timestamp = new Date().toISOString()
  chrome.storage.local.get({ logs: [] }, result => {
    const logs = result.logs
    logs.push(`[${timestamp}] ${message}`)
    chrome.storage.local.set({ logs })
  })
}
// 查看日志 - 调试时可以使用
// chrome.storage.local.get({ logs: [] }, (result) => {
//   const logs = result.logs;
//   logs.forEach((log) => {
//     console.log(log);
//   });
// });

chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  const { domainUrl } = request
  const tabUrl = `https://${domainUrl}`
  // logMessage(`接收到请求了-background, ${request.action}===${new Date()}`)
  if (request.action === 'postData') {
    request.uid &&
      checkIsSameUid(request.uid, request.mainTabId) &&
      (uid = request.uid)
    postRequest(request.url, request.params)
      .then(json => sendResponse(json))
      .catch(err => sendResponse(err))
  } else if (request.action === 'getData') {
    request.uid &&
      checkIsSameUid(request.uid, request.mainTabId) &&
      (uid = request.uid)
    getRequest(request.url)
      .then(json => {
        sendResponse(json)
      })
      .catch(err => sendResponse(err))
  } else if (request.action === 'stopTask') {
    const { mainTabId } = request
    stopCurrentTask(mainTabId, sendResponse)
  } else if (request.action === 'startInvite') {
    const { mainTabId, windowId, params } = request
    console.log('邀约参数是=====', params)
    params.uid && (uid = params.uid)
    isChinese = params.language === 'zh'
    // 初始化变量
    startInvite(mainTabId, windowId, tabUrl, params, sendResponse, sender)
  } else if (request.action === 'startLetter') {
    const { mainTabId, windowId, params } = request
    params.uid && (uid = params.uid)
    isChinese = params.language === 'zh'
    startLetter(mainTabId, windowId, tabUrl, params, sendResponse, sender)
  } else if (request.action === 'startManage') {
    const { mainTabId, windowId, params } = request
    params.uid && (uid = params.uid)
    isChinese = params.language === 'zh'
    startManage(mainTabId, windowId, tabUrl, params, sendResponse)
  } else if (request.action === 'mousedownToClick') {
    const { params } = request
    impMousedownToClick(params, sender, sendResponse)
  } else if (request.action === 'activeCurrentTab') {
    const { mainTabId } = request
    chrome.tabs.update(mainTabId, { selected: true }, () => {
      sendResponse({ code: 200, message: '激活当前页面' })
    })
  } else if (request.action === 'newTabToInviteResult') {
    // 这样接受结果的原是如果太长时间停留在页面（content_script.js），background.js就处于不活跃状态，所以必须使用sendMessage的方式触发
    const { result, backgroundParams } = request
    inviteResultFun(result, backgroundParams)
  } else if (request.action === 'newTabToLetterResult') {
    const { result, backgroundParams } = request
    letterResultFun(result, backgroundParams)
  } else if (request.action === 'getCreatorListResult') {
    const { result, backgroundParams } = request
    getCreatorListResult(result, backgroundParams)
  } else if (request.action === 'checkoutFiltersAble') {
    // 检测当前筛选项是否可用
    const { filters, domainUrl, mainTabId } = request
    const tabUrl = `https://${domainUrl}`
    checkoutFiltersAble(mainTabId, tabUrl, filters, sendResponse)
  } else if (request.action === 'getTabId') {
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      if (tabs.length > 0) {
        sendResponse({ tabId: tabs[0].id })
      } else {
        sendResponse({ tabId: null })
      }
    })
  } else if (request.action === 'getTabRunning') {
    const { mainTabId } = request
    getTabRunning(mainTabId, sendResponse)
  } else if (request.action === 'getStoreValue') {
    const { mainTabId, keyWord } = request
    getStoreValue(mainTabId, keyWord, sendResponse)
  } else if (request.action === 'updateStoreValue') {
    const { mainTabId, keyWord, value } = request
    updateStoreValue(mainTabId, keyWord, value, sendResponse)
  } else if (request.action === 'getShopId') {
    // 获取shopId
    chrome.tabs.query({ active: true, currentWindow: true }, function (tabs) {
      if (tabs.length > 0) {
        const activeUrl = tabs[0].url
        const queryList = getQueryParams(activeUrl)
        shopRegion = queryList['shop_region'] || 'US'
        getRequest(
          `${tabUrl}/api/v1/affiliate/account/info?account_type=1&shop_region=${shopRegion}`
        ).then(json => {
          if (json.shop_info && json.shop_info.length) {
            shopId = json.shop_info[0].shop_id
          } else {
            shopId = json['binding_seller_id']
          }
          sendResponse({ code: 200, shopId: shopId, message: '成功获取shopId' })
        })
      }
    })
  } else if (request.action === 'filterCreatorByServerFromPage') {
    const { mainTabId, params } = request
    filterCreatorByServerFromPage(mainTabId, params, sendResponse)
  } else if (request.action === 'getManageCreatorListResult') {
    const { result, backgroundParams } = request
    getManageCreatorListResult(result, backgroundParams)
  } else if (request.action === 'newTabToManageLetterResult') {
    const { result, backgroundParams } = request
    manageLetterResultFun(result, backgroundParams)
  }
  return true
})
// -----------------------定时任务---------------------------
chrome.alarms.onAlarm.addListener(async alarm => {
  const noFindParamsTip = () => {
    let message = ''
    if (isChinese) {
      message = '未获取到参数，不能开始邀约'
    } else {
      message = 'Cannot initiate an offer without obtaining parameters'
    }
    notificationsFun(message)
  }

  // 邀约定时任务
  if (alarm.name.includes('inviteRunningAlarm')) {
    const windowTabIdFlag = alarm.name.split('|')[1]
    // 获取store值, 获取参数
    chrome.storage.local
      .get([`inviteRunningAlarmParams${windowTabIdFlag}`])
      .then(async store => {
        if (store && store[`inviteRunningAlarmParams${windowTabIdFlag}`]) {
          const { windowId, mainTabId, params, sender, uidFlag } =
            store[`inviteRunningAlarmParams${windowTabIdFlag}`]
          uid = uidFlag
          // logMessage(
          //   `参数是--${windowId}${mainTabId}${params}${sender},=== ${new Date()}`
          // )

          const sendResponseFun = sendParam => {
            chrome.runtime.sendMessage(sender.id, sendParam)
          }

          await updateRunningStatus(mainTabId || 0, true)
          let select = true
          await selectEnglish(mainTabId, sendResponseFun).catch(() => {
            select = false
          })

          // 如果有筛选项在页面上填写筛选项
          const {
            inviteParams = {},
            filtersParamsLabel,
            inviteCheckList = {}
          } = params
          const filters = inviteParams.filters || null
          select &&
            inviteCheckList.filters &&
            filters &&
            (await fillInFilterLabel(
              mainTabId,
              filtersParamsLabel,
              sendResponseFun
            ))
          select &&
            (await executeInviteOperation(
              mainTabId,
              windowId,
              params,
              sendResponseFun
            ))
          chrome.storage.local.remove(
            `inviteRunningAlarmParams${windowTabIdFlag}`
          )
          chrome.alarms.clear(`inviteRunningAlarm|${mainTabId}`)
        } else {
          noFindParamsTip()
        }
      })
      .catch(async err => {
        console.log('storage err', err)
        noFindParamsTip()
      })
  } else if (alarm.name.includes('letterRunningAlarm')) {
    // 私信定时任务
    const windowTabIdFlag = alarm.name.split('|')[1]
    // 获取store值, 获取参数
    chrome.storage.local
      .get([`letterRunningAlarmParams${windowTabIdFlag}`])
      .then(async store => {
        if (store && store[`letterRunningAlarmParams${windowTabIdFlag}`]) {
          const { windowId, mainTabId, params, sender, uidFlag } =
            store[`letterRunningAlarmParams${windowTabIdFlag}`]
          uid = uidFlag
          // logMessage(
          //   `参数是--${windowId}${mainTabId}${params}${sender},=== ${new Date()}`
          // )

          const sendResponseFun = sendParam => {
            chrome.runtime.sendMessage(sender.id, sendParam)
          }

          await updateRunningStatus(mainTabId || 0, true)
          let select = true
          await selectEnglish(mainTabId, sendResponseFun).catch(() => {
            select = false
          })
          // 如果有筛选项在页面上填写筛选项
          const {
            letterParams = {},
            letterCheckList = {},
            filtersParamsLabel
          } = params
          const filters = letterParams.filters || null
          select &&
            letterCheckList.filters &&
            filters &&
            (await fillInFilterLabel(
              mainTabId,
              filtersParamsLabel,
              sendResponseFun
            ))
          select &&
            (await executeLetterOperation(
              mainTabId,
              windowId,
              params,
              sendResponseFun
            ))

          chrome.storage.local.remove(
            `letterRunningAlarmParams${windowTabIdFlag}`
          )
          chrome.alarms.clear(`letterRunningAlarm|${mainTabId}`)
        } else {
          noFindParamsTip()
        }
      })
      .catch(async err => {
        console.log('storage err', err)
        noFindParamsTip()
      })
  } else if (alarm.name.includes('clearNotificationAlarm')) {
    // 私信定时任务
    const notificationId = alarm.name.split('|')[1]
    chrome.notifications.clear(notificationId)
  }
})

//  ------------------------------请求封装-------------------------
const postRequest = (url, data, retries = 5, others) => {
  // // tiktok请求统一添加oec_seller_id参数
  // if (url.includes('tiktok') && shopId) {
  //   if (url.includes('?')) url += `&oec_seller_id=${shopId}`
  //   else url += `?oec_seller_id=${shopId}`
  // }
  return fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      uid: uid
    },
    body: JSON.stringify(data)
  })
    .then(async response => {
      if (!response || !response.ok) {
        await sleepFun(getRandomNumber(2, 4) * 1000)
        if (retries > 1) {
          return postRequest(url, data, retries - 1)
        } else {
          throw new Error('net error') // 抛出异常
        }
      }
      if (others) {
        const { successCode, codeKey } = others
        const result = await response.json()
        if (
          !result ||
          (!result[codeKey] && result[codeKey] !== 0) ||
          result[codeKey] !== successCode
        ) {
          // 不是成功的值就再次请求
          if (retries > 1) {
            return postRequest(url, data, retries - 1)
          } else {
            return result
          }
        } else {
          return result
        }
      } else {
        return response.json()
      }
    })
    .catch(async () => {
      await sleepFun(1000)
      if (retries > 1) {
        return postRequest(url, data, retries - 1)
      } else {
        throw new Error('net error') // 抛出异常
      }
    })
}
const getRequest = (url, retries = 5, others) => {
  // // tiktok请求统一添加oec_seller_id参数
  // if (url.includes('tiktok') && shopId) {
  //   if (url.includes('?')) url += `&oec_seller_id=${shopId}`
  //   else url += `?oec_seller_id=${shopId}`
  // }
  return fetch(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      uid: uid
    }
  })
    .then(async response => {
      if (!response || !response.ok) {
        await sleepFun(getRandomNumber(2, 4) * 1000)
        if (retries > 1) {
          return getRequest(url, retries - 1)
        } else {
          throw new Error('net error') // 抛出异常
        }
      }
      if (others) {
        const { successCode, codeKey } = others
        const result = await response.json()
        if (
          !result ||
          (!result[codeKey] && result[codeKey] !== 0) ||
          result[codeKey] !== successCode
        ) {
          // 不是成功的值就再次请求
          if (retries > 1) {
            return getRequest(url, retries - 1)
          } else {
            return result
          }
        } else {
          return result
        }
      } else {
        return response.json()
      }
    })
    .catch(async () => {
      await sleepFun(1000)
      if (retries > 1) {
        return getRequest(url, retries - 1)
      } else {
        throw new Error('net error') // 抛出异常
      }
    })
}
// 检测uid是否更换-
const checkIsSameUid = async (newUid, mainTabId) => {
  if (uid !== newUid && uid && mainTabId) {
    const windowsTabRunning = await getRunningStatus(mainTabId || 0)
    if (windowsTabRunning) {
      // 更新uid,停止当前任务
      stopCurrentTask(mainTabId)
      let message = ''
      if (isChinese) {
        message = '检测到帐号已切换,停止执行当前任务'
      } else {
        message =
          'The current task is stopped when the account is switched. Procedure'
      }
      notificationsFun(message)
    }
  }
}

// 页面重定向
const reloadToTargetPage = async (mainTabId, pageUrl) => {
  const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')
  return new Promise(resolve => {
    chrome.tabs.update(
      mainTabId,
      {
        url: `${tabUrl}${pageUrl}`
      },
      function (tab) {
        chrome.webNavigation.onCompleted.addListener(() => {
          setTimeout(() => {
            // 获取shopRegion
            const queryList = getQueryParams(tab.url)
            shopRegion = queryList['shop_region'] || 'US'
            // 获取shopId
            if (!shopId)
              getRequest(
                `${tabUrl}/api/v1/affiliate/account/info?account_type=1&shop_region=${shopRegion}`
              ).then(json => {
                if (json.shop_info && json.shop_info.length) {
                  shopId = json.shop_info[0].shop_id
                } else {
                  shopId = json['binding_seller_id']
                }
              })
            resolve('update success')
          }, 2000)
        })
      }
    )
  })
}

// --------------------------------更新running状态到page ---------------
// 建立长连接,确保当popup.html是打开状态再发送消息
let popupPort = null
chrome.runtime.onConnect.addListener(function (port) {
  if (port.name === 'popup') {
    popupPort = port
    port.onDisconnect.addListener(function () {
      popupPort = null
    })
  }
})
const updateRunningStatusToPage = (mainTabId, running) => {
  if (popupPort) {
    try {
      popupPort.postMessage({
        type: 'UPDATERUNNING',
        data: {
          currentTabId: mainTabId,
          running
        }
      })
    } catch (e) {
      console.log('err:', e)
    }
  } else {
    console.log('popup is not open')
  }
}
//  ---------------------------------选择语言-----------------------------
// 选择语言
const selectEnglish = (mainTabId, sendResponse) => {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(
      mainTabId,
      {
        action: 'selectEnglish',
        tabId: mainTabId
      },
      async res => {
        if (chrome.runtime.lastError) {
          console.log('chrome err', mainTabId, chrome.runtime.lastError)
          chromeRunningErrorTip()
          await stopCurrentTask(mainTabId, sendResponse, 'chrome error')
          reject('chrome error')
        }
        if (res && res.code === 200) {
          resolve(res.message)
        } else if (res && res.code === 505) {
          reject(res.message)
        } else {
          await stopCurrentTask(
            mainTabId,
            sendResponse,
            res ? res.message : 'select language error'
          )
          reject(res ? res.message : 'select language error')
        }
      }
    )
  })
}

const fillInFilterLabel = (mainTabId, filtersParamsLabel, sendResponse) => {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(
      mainTabId,
      {
        action: 'fillInFilterLabel',
        tabId: mainTabId,
        filtersParamsLabel
      },
      async res => {
        if (chrome.runtime.lastError) {
          console.log('chrome err', mainTabId, chrome.runtime.lastError)
          chromeRunningErrorTip()
          await stopCurrentTask(mainTabId, sendResponse, 'chrome err')
          reject('chrome error')
        }
        if (res && res.code === 200) {
          resolve(res.message)
        } else if (res && res.code === 505) {
          reject(res.message)
        } else {
          await stopCurrentTask(
            mainTabId,
            sendResponse,
            res ? res.message : 'select language error'
          )
          reject(res ? res.message : 'select language error')
        }
      }
    )
  })
}

// ----------------------------------获取页面running状态------------------
const getTabRunning = async (mainTabId, sendResponse) => {
  const windowsTabRunning = await getRunningStatus(mainTabId || 0)
  sendResponse({ windowsTabRunning: windowsTabRunning })
}
// ---------------------------------storage存储------------------------------
const updateStoreValue = async (mainTabId, keyWord, value, sendResponse) => {
  await updateWindowTabValue(mainTabId || 0, keyWord, value)
  sendResponse({ code: 0 })
}
const getStoreValue = async (mainTabId, keyWord, sendResponse) => {
  const storeValue = await getWindowTabValue(mainTabId || 0, keyWord)
  sendResponse({ storeValue: storeValue })
}

//  ---------------------------------停止操作-----------------------------
// 停止执行
const stopCurrentTask = async (mainTabId, sendResponse, message) => {
  // 停止当前页面
  await stopTask(mainTabId, sendResponse, message)
  // 停止子页面
  const currentSubTab = await getWindowTabValue(mainTabId || 0, 'currentSubTab')
  currentSubTab && (await stopTask(currentSubTab, sendResponse, message))
  // 停止父页面
  const currentParentTab = await getWindowTabValue(
    mainTabId || 0,
    'currentParentTab'
  )
  currentParentTab && (await stopTask(currentParentTab, sendResponse, message))

  let realParentTabId = mainTabId
  if (currentParentTab) realParentTabId = currentParentTab

  // 清空存储的数据
  await updateWindowTabValue(realParentTabId || 0, 'currentInviteNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(realParentTabId || 0, 'currentInviteTimes', 0)
  await updateWindowTabValue(realParentTabId || 0, 'currentLetterNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(realParentTabId || 0, 'currentLetterTimes', 0)
  await updateWindowTabValue(realParentTabId || 0, 'currentManageNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(realParentTabId || 0, 'beforeFindData', null)
  await updateWindowTabValue(realParentTabId || 0, 'beforeFindManageData', null)
  // 清空页面插入元素
  chrome.tabs.sendMessage(realParentTabId, {
    action: 'clearCreatorLoadingContainer'
  })

  await sleepFun(1000)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
}
const stopTask = (mainTabId, sendResponse, messageReason) => {
  return new Promise((resolve, reject) => {
    const stopAction = async () => {
      // 如果停止之前是运行状态就弹窗
      const windowsTabRunning = await getRunningStatus(mainTabId || 0)
      if (windowsTabRunning) {
        let message = ''
        if (isChinese) {
          message = '任务被中断'
        } else {
          message = 'Task was interrupted'
        }
        const tipMessage = messageReason
          ? messageReason.toString().includes('[object')
            ? message
            : messageReason.toString()
          : message
        notificationsFun(tipMessage)
      }
      await updateRunningStatus(mainTabId || 0, false)
    }
    if (mainTabId) {
      chrome.tabs.sendMessage(
        mainTabId,
        {
          action: 'stopTask'
        },
        async res => {
          if (chrome.runtime.lastError) {
            console.log(
              'stop Task - chrome err',
              chrome.runtime.lastError.message
            )
            // await stopAction()
            // let message = ''
            // if (isChinese) {
            //   message = 'chrome运行错误请稍后再试'
            // } else {
            //   message = 'Chrome encountered an error. Please try again later.'
            // }
            // notificationsFun(message)
          } else {
            if (res.code === 200) {
              await stopAction()
            }
            sendResponse && sendResponse(res)
          }
          resolve('stop')
        }
      )
    } else {
      sendResponse &&
        sendResponse({
          code: 500,
          message: 'no find tag'
        })
      reject('no find tag')
    }
  })
}
// --------------------------------检测当前筛选项是否可用---------------------
async function checkoutFiltersAble(mainTabId, tabUrl, filters, sendResponse) {
  if (tabUrl) {
    await updateWindowTabValue(mainTabId, 'currentTabUrl', tabUrl)
  }
  // let findUrl = tabUrl.includes('affiliate.tiktok.com')
  //   ? `${tabUrl}/api/v1/oec/affiliate/creator/marketplace/search?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`
  //   : `${tabUrl}/api/v1/oec/affiliate/creator/marketplace/find?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`
  let findUrl = `${tabUrl}/api/v1/oec/affiliate/creator/marketplace/find?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`
  const query = filters ? filters.query : ''
  const filter_params = filters ? deepClone(filters) : null
  filter_params && delete filter_params.query
  // let params = tabUrl.includes('affiliate.tiktok.com')
  //   ? {
  //       request: {
  //         follower_genders: [],
  //         follower_age_groups: [],
  //         managed_by_agency: [],
  //         pagination: {
  //           size: 20,
  //           page: 0
  //         },
  //         creator_score_range: [],
  //         content_preference_range: [1],
  //         algorithm: 1, // 26按粉丝量排序, 1相关性
  //         query: query || ``,
  //         query_type: 1,
  //         filter_params: filter_params || {}
  //       }
  //     }
  //   : {
  //       pagination: { size: 12, page: 0 },
  //       query_type: 1,
  //       algorithm: 1, // 按粉丝量排序
  //       query: query || ``,
  //       filter_params: filter_params || {}
  //     }
  let params = {
    pagination: { size: 12, page: 0 },
    query_type: 1,
    algorithm: 1, // 按粉丝量排序
    query: query || ``,
    filter_params: filter_params || {}
  }
  const sendFindReq = () => {
    return postRequest(findUrl, { ...params }, 5, {
      successCode: 0,
      codeKey: 'code'
    }).then(data => {
      if (data.code === 0) {
        // const result = tabUrl.includes('affiliate.tiktok.com')
        //   ? data.data
        //   : data
        const result = data
        const targetCreatorList = result.creator_profile_list

        if (!targetCreatorList || targetCreatorList.length === 0) {
          sendResponse({
            code: 201,
            message: 'no creators'
          })
        } else {
          sendResponse({
            code: 200,
            message: 'has creators'
          })
        }
      } else {
        sendResponse({
          code: 500,
          message: 'server error'
        })
      }
    })
  }
  await sendFindReq()
}
//  ---------------------------------邀请操作-----------------------------
// 开始邀请
const startInvite = async (
  mainTabId,
  windowId,
  tabUrl,
  params,
  sendResponse,
  sender
) => {
  const { inviteParams = {}, inviteCheckList = {} } = params
  if (inviteCheckList.selectCreator) {
    inviteParams.creator = inviteParams.selectCreator
  }
  if (tabUrl) {
    await updateWindowTabValue(mainTabId, 'currentTabUrl', tabUrl)
  }
  // 更新为0
  await updateWindowTabValue(mainTabId || 0, 'currentInviteNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(mainTabId || 0, 'currentInviteTimes', 0)
  await updateWindowTabValue(mainTabId || 0, 'beforeFindData', null)
  await reloadToTargetPage(mainTabId, '/connection/creator')
  let message = ''
  if (isChinese) {
    message = '邀约任务开始执行，请不要关闭页面'
  } else {
    message = 'Invitation task started, please do not close the page'
  }
  notificationsFun(message, 3)
  // 开始邀约
  const {
    inviteParams: { timing }
  } = params

  let timeLayout = 0
  if (timing) {
    const nowTime = new Date().valueOf()
    timeLayout = timing - nowTime
    if (timeLayout < 0) {
      timeLayout = 0
      sendResponse({
        code: 500,
        message: 'set Time Error'
      })
      return
    }
  }

  // 定时任务
  if (timing) {
    // 存储参数值(不能存函数)
    chrome.storage.local
      .set({
        [`inviteRunningAlarmParams${mainTabId}`]: {
          windowId,
          mainTabId,
          params,
          sender: sender,
          uidFlag: uid
        }
      })
      .catch(async err => {
        console.log('storage err', err)
        await stopCurrentTask(mainTabId, sendResponse, 'storage err')
      })
    chrome.alarms.create(`inviteRunningAlarm|${mainTabId}`, {
      when: timing
    })
  } else {
    await updateRunningStatus(mainTabId || 0, true)
    let select = true
    await selectEnglish(mainTabId, sendResponse).catch(() => {
      select = false
    })

    // 如果有筛选项在页面上填写筛选项
    const {
      inviteParams = {},
      filtersParamsLabel,
      inviteCheckList = {}
    } = params
    const filters = inviteParams.filters || null
    select &&
      inviteCheckList.filters &&
      filters &&
      (await fillInFilterLabel(mainTabId, filtersParamsLabel, sendResponse))

    select &&
      (await executeInviteOperation(mainTabId, windowId, params, sendResponse))
  }
}

// 打开新的邀请页面
const executeInviteOperation = async (
  mainTabId,
  windowId,
  params,
  sendResponse
) => {
  try {
    const { inviteParams = {}, inviteCheckList = {} } = params
    let {
      onceCreator = 10,
      maxInviteNum = 50,
      products,
      reqGap = 2
    } = inviteParams.invite
    // maxInviteNum表示的是邀约人数转换成邀约次数
    const lastInviteNum = maxInviteNum % onceCreator
    const createInviteTimes =
      Math.floor(maxInviteNum / onceCreator) + (lastInviteNum ? 1 : 0)
    // eslint-disable-next-line no-unused-vars
    let getAllData = false

    let currentInviteTimes =
      (await getWindowTabValue(mainTabId, 'currentInviteTimes')) || 0

    console.log('当前邀约次数：', currentInviteTimes + 1)
    // 修改邀请名称，编号
    if (inviteParams.invite && inviteParams.invite.nameNumber) {
      // 名字长度最大是30
      let formatName = ''
      if (currentInviteTimes === 0) {
        formatName = `${inviteParams.invite.name || ''}_${currentInviteTimes + 1}`
      } else {
        const nameLength = inviteParams.invite.name.lastIndexOf('_')
        formatName = `${inviteParams.invite.name.slice(0, nameLength)}_${currentInviteTimes + 1}`
      }
      if (formatName.length < 30 || formatName.length === 30) {
        inviteParams.invite.name = formatName
      } else if (inviteParams.invite.name.length > 30) {
        inviteParams.invite.name = inviteParams.invite.name.slice(0, 30)
      }
    }

    if (!inviteCheckList.creator && !inviteCheckList.selectCreator) {
      let currentNextPage = await getWindowTabValue(
        mainTabId,
        'currentInviteNextPage'
      )
      // 没有选择网红时邀约合作创建不能超过createInviteTimes（邀请完再 ++，所以这里取值-1）
      if (currentInviteTimes > createInviteTimes - 1) {
        await inviteDone(mainTabId)
        return
      }

      const blackCreator = inviteParams.blackCreator || []
      // 单次邀约获取的人数,默认是onceCreator，如果是最后一次邀约需要计算获取
      let creatorTimes = onceCreator
      if (lastInviteNum && currentInviteTimes === createInviteTimes - 1) {
        creatorTimes = lastInviteNum
      }
      // 获取过滤项
      let filters = null
      if (inviteCheckList.filters) filters = inviteParams.filters || null
      // 逻辑有变：直接跳转到邀约，在邀约页面获取创作者
      await newTabToInvite(
        params,
        inviteParams,
        getAllData,
        mainTabId,
        windowId,
        sendResponse,
        {
          type: 'noCreator',
          currentPage: (currentNextPage && currentNextPage.currentPage) || 0,
          offset: (currentNextPage && currentNextPage.offset) || 0,
          productIdList: products.map(item => item.id),
          reqGap,
          filters,
          blackCreator,
          creatorTimes,
          mainTabId
        }
      )
    } else {
      // 过滤黑名单数据
      if (!inviteParams.ableInviteCreator) {
        // ableInviteCreator 赋初值
        inviteParams.ableInviteCreator = inviteParams.blackCreator
          ? inviteParams.creator
            ? inviteParams.creator.filter(
                item => !inviteParams.blackCreator.includes(item)
              )
            : null
          : inviteParams.creator
        inviteParams.lastInvitedCreator = null
      } else {
        // 更新ableInviteCreator
        if (inviteParams.lastInvitedCreator) {
          const index = inviteParams.ableInviteCreator.indexOf(
            inviteParams.lastInvitedCreator
          )
          if (index >= 0) {
            inviteParams.ableInviteCreator.splice(0, index + 1)
          }
        }
      }
      console.log(
        '上次邀约到哪里了？？？',
        inviteParams.lastInvitedCreator,
        inviteParams.ableInviteCreator
      )
      if (
        !inviteParams.ableInviteCreator ||
        !inviteParams.ableInviteCreator.length
      ) {
        await inviteDone(mainTabId)
        return
      }

      const windowsTabRunning = await getRunningStatus(mainTabId || 0)
      if (windowsTabRunning) {
        await newTabToInvite(
          params,
          inviteParams,
          getAllData,
          mainTabId,
          windowId,
          sendResponse,
          {
            type: 'hasCreator',
            creatorTimes: onceCreator,
            reqGap,
            mainTabId
          }
        )
      }
    }
  } catch (e) {
    console.log('e', e)
    await stopCurrentTask(mainTabId, sendResponse, e.toString())
  }
}

const newTabToInvite = async (
  params,
  inviteParams,
  getAllData,
  mainTabId,
  windowId,
  sendResponse,
  otherParams
) => {
  try {
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `正在邀约...`
    })
    await sleepFun(2000)

    let windowsTabRunning = await getRunningStatus(mainTabId || 0)
    const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')

    windowsTabRunning &&
      chrome.tabs.create(
        {
          active: true,
          url: `${tabUrl}/connection/target-invitation/create?enter_from=target_invitation_list&shop_region=${shopRegion}&language=en`,
          openerTabId: mainTabId,
          windowId: windowId
        },
        tab => {
          const onCompletedListener = async details => {
            let currentInviteTimes =
              (await getWindowTabValue(mainTabId, 'currentInviteTimes')) || 0
            currentInviteTimes++
            await updateWindowTabValue(
              mainTabId || 0,
              'currentInviteTimes',
              currentInviteTimes
            )
            if (!details || !tab) return
            if (details.tabId === tab.id) {
              // 子Tab的running和父Tab的running保持一致
              await updateRunningStatus(tab.id || 0, true)
              // 存储当前父Tab存储的子Tab的id
              await updateWindowTabValue(mainTabId, 'currentSubTab', tab.id)
              // 存储当前子页面对应的父页面的id
              await updateWindowTabValue(tab.id, 'currentParentTab', mainTabId)

              try {
                chrome.tabs.sendMessage(tab.id, {
                  action: 'newInviteTabToInvite',
                  params: {
                    ...inviteParams,
                    ...otherParams
                  },
                  backgroundParams: {
                    mainTabId,
                    windowId,
                    subTabId: tab.id,
                    params,
                    sendResponse,
                    getAllData
                  }
                })
              } catch (e) {
                await updateRunningStatus(tab.id || 0, false)
                await stopCurrentTask(mainTabId, sendResponse, e.toString())
                console.log('e', e.toString())
              }

              // 移除监听器
              chrome.webNavigation.onCompleted.removeListener(
                onCompletedListener
              )
            }
          }
          chrome.webNavigation.onCompleted.addListener(onCompletedListener)
        }
      )
  } catch (e) {
    await stopCurrentTask(mainTabId, sendResponse, e.toString())
  }
}

const inviteDone = async mainTabId => {
  await updateRunningStatus(mainTabId || 0, false)
  await updateWindowTabValue(mainTabId || 0, 'currentInviteNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(mainTabId || 0, 'currentInviteTimes', 0)
  await updateWindowTabValue(mainTabId || 0, 'beforeFindData', null)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
  let message = '邀约任务完成'
  if (isChinese) {
    message = '邀约任务完成'
  } else {
    message = 'Invitation task completed'
  }
  notificationsFun(message)

  await sleepFun(1000)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
}

// -------------------------------- 私信相关操作 ----------------------------
// 开始私信
const startLetter = async (
  mainTabId,
  windowId,
  tabUrl,
  params,
  sendResponse,
  sender
) => {
  const { letterParams = {}, letterCheckList = {} } = params
  if (letterCheckList.selectCreator) {
    letterParams.creator = letterParams.selectCreator
  }
  if (tabUrl) {
    await updateWindowTabValue(mainTabId, 'currentTabUrl', tabUrl)
  }
  await updateWindowTabValue(mainTabId || 0, 'currentLetterNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(mainTabId || 0, 'currentLetterTimes', 0)
  await updateWindowTabValue(mainTabId || 0, 'beforeFindData', null)
  await reloadToTargetPage(mainTabId, '/connection/creator')

  let message = ''
  if (isChinese) {
    message = '私信任务开始执行，请不要关闭页面'
  } else {
    message = 'Private message task started, please do not close the page'
  }
  notificationsFun(message, 3)

  const {
    letterParams: { timing }
  } = params
  let timeLayout = 0
  if (timing) {
    const nowTime = new Date().valueOf()
    timeLayout = timing - nowTime
    if (timeLayout < 0) {
      timeLayout = 0
      sendResponse({
        code: 500,
        message: 'set Time Error'
      })
      return
    }
  }

  // 定时任务
  if (timing) {
    // 存储参数值(不能存函数)
    chrome.storage.local
      .set({
        [`letterRunningAlarmParams${mainTabId}`]: {
          windowId,
          mainTabId,
          params,
          sender: sender,
          uidFlag: uid
        }
      })
      .catch(async err => {
        console.log('storage err', err)
        await stopCurrentTask(mainTabId, sendResponse, 'storage err')
      })
    // 设置定时操作
    chrome.alarms.create(`letterRunningAlarm|${mainTabId}`, {
      when: timing
    })
  } else {
    await updateRunningStatus(mainTabId || 0, true)
    let select = true
    await selectEnglish(mainTabId, sendResponse).catch(() => {
      select = false
    })
    // 如果有筛选项在页面上填写筛选项
    const {
      letterParams = {},
      letterCheckList = {},
      filtersParamsLabel
    } = params
    const filters = letterParams.filters || null
    select &&
      letterCheckList.filters &&
      filters &&
      (await fillInFilterLabel(mainTabId, filtersParamsLabel, sendResponse))
    select &&
      (await executeLetterOperation(mainTabId, windowId, params, sendResponse))
  }
}

// 执行私信操作
const executeLetterOperation = async (
  mainTabId,
  windowId,
  params,
  sendResponse
) => {
  try {
    const { letterParams = {}, letterCheckList = {} } = params
    const {
      creator,
      blackCreator,
      skipDay,
      letter: { maxLetterNum = 50, reqGap = 2 },
      filters = null,
      skipHasReplied = false
    } = letterParams
    let currentCreatorInfo = []
    let getAllData = false
    let currentNextPage = await getWindowTabValue(
      mainTabId,
      'currentLetterNextPage'
    )
    // 如果没有选择creator，需要从页面上获取下一个网红
    if (!letterCheckList.creator && !letterCheckList.selectCreator) {
      let currentLetterTimes =
        (await getWindowTabValue(mainTabId, 'currentLetterTimes')) || 0
      // 未选择的时候计数，不能超过maxLetterNum个(私信完之后再++,所以这里取-1)
      if (currentLetterTimes > maxLetterNum - 1) {
        await letterDone(mainTabId)
        return
      }

      const otherBackgroundParams = {
        callbackParams: {
          params,
          letterParams,
          letterCheckList,
          getAllData,
          mainTabId,
          windowId,
          sendResponse
        },
        callbackName: 'letterCallback'
      }
      // 去获取创作者
      await getPageCreateList(
        mainTabId,
        1,
        blackCreator,
        filters,
        {
          page: 'letter',
          skipDay,
          skipHasReplied,
          currentPage: (currentNextPage && currentNextPage.currentPage) || 0,
          offset: (currentNextPage && currentNextPage.offset) || 0,
          reqGap
        },
        otherBackgroundParams
      )
    } else {
      if (!letterParams.ableLetterCreator) {
        letterParams.ableLetterCreator = creator
          .filter(item => !blackCreator.includes(item))
          .filter(item => item.length)
      }

      if (
        !letterParams.ableLetterCreator ||
        letterParams.ableLetterCreator.length === 0
      ) {
        await letterDone(mainTabId)
        return
      }

      // 如果指定creator需要手动获取一下uid并向后端验证
      const filterData = await filterSelectCreator(
        mainTabId,
        letterParams.ableLetterCreator,
        { page: 'letter', onceCreator: 1, skipHasReplied, reqGap }
      )
      currentCreatorInfo = filterData && filterData.currentCreatorInfo

      letterParams.letterCreator = letterParams.ableLetterCreator.splice(
        0,
        currentCreatorInfo.length
      )
      if (
        (!letterParams.letterCreator || !letterParams.letterCreator) &&
        !letterParams.ableLetterCreator.length
      ) {
        await letterDone(mainTabId)
        return
      }

      const windowsTabRunning = await getRunningStatus(mainTabId || 0)
      if (windowsTabRunning) {
        const filterResult = await filterCreatorListByServer(
          letterParams.letterCreator,
          currentCreatorInfo,
          {
            page: 'letter',
            skipDay
          },
          mainTabId,
          sendResponse
        )
        if (!filterResult || filterResult.code !== 200) {
          await stopCurrentTask(mainTabId, sendResponse, filterResult.message)
        }
        // 开始私信==================
        await newTabToLetter(
          {
            currentCreator: letterParams.letterCreator[0],
            currentCreatorInfo: currentCreatorInfo[0],
            getAllData: false
          },
          mainTabId,
          windowId,
          params,
          letterParams,
          letterCheckList,
          sendResponse
        )
      }
    }
  } catch (e) {
    console.log('e', e)
    await stopCurrentTask(mainTabId, sendResponse, e.toString())
  }
}

const letterDone = async mainTabId => {
  await updateRunningStatus(mainTabId || 0, false)
  await updateWindowTabValue(mainTabId || 0, 'currentLetterNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(mainTabId || 0, 'currentLetterTimes', 0)
  await updateWindowTabValue(mainTabId || 0, 'beforeFindData', null)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
  // sendResponse({
  //   code: 200,
  //   message: 'letter done'
  // })
  let message = ''
  if (isChinese) {
    message = '私信任务完成'
  } else {
    message = 'Private message task completed'
  }
  notificationsFun(message)

  await sleepFun(1000)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
}

// 跳转页面去执行私信操作
const newTabToLetter = async (
  creatorParams,
  mainTabId,
  windowId,
  params,
  letterParams,
  letterCheckList,
  sendResponse
) => {
  const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')

  return new Promise((resolve, reject) => {
    const { currentCreator, currentCreatorInfo, getAllData } = creatorParams
    if (!currentCreator) {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'letterTabNoCreators',
        backgroundParams: {
          mainTabId,
          windowId,
          subTabId: null,
          params,
          sendResponse,
          getAllData
        }
      })
      resolve('no create')
      return
    }
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `正在私信...`
    })

    chrome.tabs.create(
      {
        active: true,
        url: `${tabUrl}/seller/im?shop_id=${shopId}&creator_id=${currentCreatorInfo.id}&enter_from=affiliate_creator_details&shop_region=${shopRegion}`,
        openerTabId: mainTabId,
        windowId: windowId
      },
      tab => {
        const onCompletedListener = async details => {
          if (!letterCheckList.creator && !letterCheckList.selectCreator) {
            let currentLetterTimes =
              (await getWindowTabValue(mainTabId, 'currentLetterTimes')) || 0
            currentLetterTimes++
            await updateWindowTabValue(
              mainTabId || 0,
              'currentLetterTimes',
              currentLetterTimes
            )
          }

          if (!details || !tab) return
          if (details.tabId === tab.id) {
            // 子Tab的running和父Tab的running保持一致
            await updateRunningStatus(tab.id || 0, true)
            // 存储当前父Tab存储的子Tab的id
            await updateWindowTabValue(mainTabId, 'currentSubTab', tab.id)
            // 存储当前子页面对应的父页面的id
            await updateWindowTabValue(tab.id, 'currentParentTab', mainTabId)

            try {
              chrome.tabs.sendMessage(tab.id, {
                action: 'newLetterTabToInvite',
                tabId: tab.id,
                params: {
                  letterParams: letterParams,
                  creator: currentCreatorInfo,
                  tabId: tab.id
                },
                backgroundParams: {
                  mainTabId,
                  windowId,
                  subTabId: tab.id,
                  params,
                  sendResponse,
                  getAllData
                }
              })
            } catch (e) {
              console.log('e', e)
              await updateRunningStatus(tab.id || 0, false)
              await stopCurrentTask(mainTabId, sendResponse, e.toString())
              reject(e)
            }

            // 移除监听器
            chrome.webNavigation.onCompleted.removeListener(onCompletedListener)
          }
        }
        chrome.webNavigation.onCompleted.addListener(onCompletedListener)
      }
    )
  })
}
// ----------------------------------私信和邀约公用操作-------------------------------
// 关闭打开的标签页
const closeNewTab = tabId => {
  console.log('关闭窗口的函数')
  chrome.tabs.remove(tabId, async () => {
    if (chrome.runtime.lastError) {
      console.log('chrome err')
    } else {
      console.log('tab close')
    }
  })
}

// 从页面获取creator (现在邀约操作从页面上一个个去过滤所以邀约先不用这个了)
const getPageCreateList = async (
  mainTabId,
  num,
  blackCreator,
  filters,
  source,
  otherBackgroundParams
) => {
  return new Promise((resolve, reject) => {
    // -------------------12.17---------------------
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `去页面抓取网红`
    })
    const backgroundParams = {
      otherBackgroundParams,
      creatorBackgroundParams: {
        mainTabId,
        num,
        blackCreator,
        filters,
        source
      }
    }

    // 需要向页面请求获取数据
    if (mainTabId) {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'getCreatorList',
        mainTabId: mainTabId || 0,
        shopRegion: shopRegion,
        shopId: shopId,
        params: {
          num,
          blackCreator,
          filters,
          currentPage: source.currentPage,
          offset: source.offset,
          page: source.page,
          productIdList: source.productIdList,
          skipHasReplied: source.skipHasReplied,
          reqGap: source.reqGap
        },
        backgroundParams
      })
    } else {
      console.log('no tab')
      reject(null)
    }
  })
}

// 过滤用户自己选择的creator
const filterSelectCreator = async (mainTabId, creatorList, source) => {
  const {
    page,
    // productIdList,
    onceCreator,
    skipHasReplied,
    reqGap = 2
  } = source
  if (!creatorList || creatorList.length === 0) return
  // 根据creator获取currentCreatorInfo
  const currentCreatorInfo = []
  for (let i = 0; i < creatorList.length; i++) {
    let windowsTabRunning = true
    if (i % 2 === 0) {
      windowsTabRunning = await getRunningStatus(mainTabId || 0)
    }
    if (!windowsTabRunning) {
      return
    }
    const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')
    let findUrl = `${tabUrl}/api/v1/oec/affiliate/creator/marketplace/find?user_language=en&shop_region=${shopRegion}&oec_seller_id=${shopId}`
    let params = {
      pagination: { size: 12, page: 0 },
      query_type: 1,
      filter_params: {},
      algorithm: 1,
      query: `${creatorList[i]}`
    }

    chrome.tabs.sendMessage(mainTabId, {
      action: 'addCreatorCheckLoading',
      creatorItemName: creatorList[i]
    })
    const sendFindReq = () => {
      return postRequest(findUrl, { ...params }, 5, {
        successCode: 0,
        codeKey: 'code'
      }).then(async data => {
        if (data.code === 0) {
          const targetCreatorList = data.creator_profile_list
          if (targetCreatorList) {
            let searchTrue = false
            for (let index = 0; index < targetCreatorList.length; index++) {
              let creatorItem = targetCreatorList[index]
              console.log(
                '搜索匹配结果===',
                creatorList[i],
                creatorItem.handle.value,
                creatorItem.handle.value === creatorList[i]
              )
              if (creatorItem.handle.value === creatorList[i]) {
                searchTrue = true
                const name = creatorItem.handle.value
                const id = creatorItem.creator_oecuid.value
                if (page === 'letter' && skipHasReplied) {
                  let hasReplied = false
                  hasReplied = await checkoutByConversationSearch(mainTabId, {
                    name,
                    id
                  })
                  // 没有回复才继续邀请
                  if (!hasReplied) {
                    currentCreatorInfo.push({
                      name,
                      id
                    })
                    chrome.tabs.sendMessage(mainTabId, {
                      action: 'addProcessLoadingTest',
                      string: `获取有效网红数目：${currentCreatorInfo.length}`
                    })
                  } else {
                    // 数据不可用，剔除
                    creatorList.splice(i, 1)
                    i--
                  }
                } else {
                  currentCreatorInfo.push({
                    name,
                    id
                  })
                  chrome.tabs.sendMessage(mainTabId, {
                    action: 'addProcessLoadingTest',
                    string: `获取有效网红数目：${currentCreatorInfo.length}`
                  })
                }

                break
              }
            }
            if (!searchTrue) {
              // 没有该数据剔除
              creatorList.splice(i, 1)
              i--
            }
          } else {
            // 没有该数据剔除
            creatorList.splice(i, 1)
            i--
          }
        } else {
          return new Promise((resolve, reject) => {
            reject('task stop (请求太频繁，请将请求间隔调大后再试 search-b)')
          })
        }
      })
    }
    await sendFindReq()
    // 停留半秒
    await sleepFun(getRandomNumber(reqGap, reqGap + 1) * 1000)

    if (currentCreatorInfo.length === onceCreator) {
      break
    }
  }
  return new Promise(resolve => {
    resolve({
      currentCreatorInfo
    })
  })
}
const filterCreatorByServerFromPage = async (
  mainTabId,
  params,
  sendResponse
) => {
  const { creatorList, currentCreatorInfo, source } = params
  const res = await filterCreatorListByServer(
    creatorList,
    currentCreatorInfo,
    source,
    mainTabId,
    sendResponse
  )
  sendResponse({
    ...res
  })
}
// 后端服务器过滤create
const filterCreatorListByServer = async (
  creatorList,
  currentCreatorInfo,
  source,
  mainTabId,
  sendResponse
) => {
  if (
    !currentCreatorInfo ||
    !currentCreatorInfo.length ||
    !creatorList ||
    !creatorList.length
  )
    return {
      code: 404,
      message: 'no create'
    }

  let errorFlag = false
  for (let i = 0; i < creatorList.length; i++) {
    let windowsTabRunning = true
    if (i % 2 === 0) {
      windowsTabRunning = await getRunningStatus(mainTabId || 0)
    }
    if (!windowsTabRunning) {
      errorFlag = true
      break
    }

    const queryParams = {
      ttsUid: currentCreatorInfo[i].id,
      alias: currentCreatorInfo[i].name,
      type: source.page === 'invite' ? '1' : '2' // 1邀约；2私信
    }
    if (source.page === 'manage') {
      ;(queryParams.note = source.note),
        (queryParams.type = source.note === 'batchInvite' ? '1' : '2') // 1邀约；2私信
    }
    if (source.skipDay) queryParams.skipDays = source.skipDay.toString()

    console.log('参数是===', queryParams)

    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `正在入库${currentCreatorInfo[i].name}，已入库${i}个网红`
    })
    await postRequest(`${REQUEST_TARGET}/ws/tts/plugin/inviteCollection`, {
      ...queryParams
    }).then(async data => {
      if (data.errorNum === 0) {
        // 成功
        // 获取收藏夹id
        if (source.page === 'invite') {
          const inviteFavId = data.retData
          setStoreSignalVar(`inviteFavId${uid}`, inviteFavId)
        } else {
          const letterFavId = data.retData
          setStoreSignalVar(`letterFavId${uid}`, letterFavId)
        }
      } else if (data.errorNum === 40016) {
        errorFlag = true

        let message = ''
        if (isChinese) {
          message = '权限不足, 任务已停止'
        } else {
          message = 'Insufficient Permissions'
        }
        chrome.notifications.create(
          {
            type: 'basic',
            iconUrl: 'noxinfluencer_128.png',
            title: '',
            message: message,
            priority: 2,
            requireInteraction: true
          },
          async function (notificationId) {
            if (chrome.runtime.lastError) {
              console.error(chrome.runtime.lastError.message)
            } else {
              // 设置一个定时器，在 2 秒后清除通知
              chrome.alarms.create(`clearNotificationAlarm|${notificationId}`, {
                when: Date.now() + 2000
              })

              await stopCurrentTask(mainTabId, sendResponse, '没有权限')
              // 达到限额，向页面发送提示信息
              sendResponse &&
                sendResponse({
                  code: 40016,
                  message: '没有权限'
                })
            }
          }
        )
      } else if (data.errorNum === 10029) {
        // n天内已联系，提剔除
        const targetIndex = creatorList.indexOf(currentCreatorInfo[i].name)
        creatorList.splice(targetIndex, 1)
        currentCreatorInfo.splice(i, 1)
        i--
      } else {
        // 剔除该用户继续向下执行
        const targetIndex = creatorList.indexOf(currentCreatorInfo[i].name)
        creatorList.splice(targetIndex, 1)
        currentCreatorInfo.splice(i, 1)
        i--
      }
    })
  }

  if (errorFlag) {
    return {
      code: 500,
      message: 'server error stop'
    }
  } else {
    return {
      code: 200,
      data: {
        currentCreatorInfo: currentCreatorInfo,
        creatorList: creatorList
      }
    }
  }
}

// letter私信跳过网红已回复但商家未进一步回复的网红
const checkoutByConversationSearch = async (mainTabId, creator) => {
  const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')
  let conversationSearchUrl = `${tabUrl}/api/v1/im/shop_creator/shop/conversation/search?uname=${creator.name}&shop_region=${shopRegion}&user_language=en&biz=shop_creator&role=shop&cursor=0&app_name=i18n_ecom_alliance&device_id=0&device_platform=web&cookie_enabled=true&browser_name=Mozilla&browser_online=true`
  return getRequest(conversationSearchUrl, 5, {
    successCode: 0,
    codeKey: 'code'
  }).then(async data => {
    if (data.code === 0 && data.data) {
      const conversationsList = data.data.conversations
      if (conversationsList && conversationsList.length) {
        const members = conversationsList[0].members
        const creatorImId = members.filter(
          item => item.role_name === 'creator'
        )[0].im_id
        const lastMessage =
          conversationsList[0] && conversationsList[0].last_message
            ? conversationsList[0].last_message
            : null
        const senderId = lastMessage ? lastMessage.sender_id : null
        const messageType =
          lastMessage && lastMessage.ext ? lastMessage.ext.type : null
        if (
          lastMessage &&
          senderId === creatorImId &&
          messageType !== 'notification'
        ) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    } else {
      console.error('task stop (letter)')
      // 没有返回有效值等一下再接着请求
      await sleepFun(getRandomNumber(2, 3) * 1000)
      return true
    }
  })
}

// ----------------------------------处理私信和邀约响应操作----------------------------
// 接收从background页面传过来的邀约结果
const inviteResultFun = async (res, backgroundParams) => {
  const { mainTabId, windowId, params, sendResponse, subTabId, getAllData } =
    backgroundParams
  console.log('邀约返回结果===', res, backgroundParams)

  if (getAllData) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前子tab
    // if (res.code === 200) await closeNewTab(subTabId || 0)
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await inviteDone(mainTabId)
    })
    return
  }
  if (res.code === 200) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前tab
    // await closeNewTab(subTabId || 0)
    // 调试阶段先关闭
    await nextNewInviteTabFun(mainTabId, windowId, params, sendResponse)
  } else if (res.code === 201) {
    await updateRunningStatus(subTabId || 0, false)
    // 发送邀约失败，但是仍然继续邀约
    await nextNewInviteTabFun(mainTabId, windowId, params, sendResponse)
  } else if (res.code === 505) {
    // 由stop过程触发的，无需再次执行stopTask
  } else {
    // 触发stop操作
    await stopCurrentTask(mainTabId, sendResponse, res.message)
  }
}
// 接收从background页面传过来的邀约结果
const letterResultFun = async (res, backgroundParams) => {
  const { mainTabId, windowId, params, sendResponse, subTabId, getAllData } =
    backgroundParams

  // let windowsTabRunning = await getRunningStatus(mainTabId || 0)
  // if (!windowsTabRunning) return
  if (getAllData) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前tab(私信不管是否成功都关闭)
    await closeNewTab(subTabId || 0)
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await letterDone(mainTabId)
    })
    return
  }
  if (res.code === 200 || res.code === 201) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前tab
    await closeNewTab(subTabId || 0)
    // 调试阶段先关闭
    await nextNewLetterTabFun(mainTabId, windowId, params, sendResponse)
  } else if (res.code === 505) {
    // 关闭当前tab
    await closeNewTab(subTabId || 0)
    // 由stop过程触发的，无需再次执行stopTask
  } else {
    await closeNewTab(subTabId || 0)
    // 触发stop操作
    await stopCurrentTask(mainTabId, sendResponse, res.message)
  }
}

// 邀约和私信从页面获取完网红的回调，由于函数没有办法作为参数再background和content_script.js中传递，这里只能设置映射执行
// 在这里更新background的各种值， callbackParams的值等
const getCreatorCallbackMap = {
  inviteCallback: async (data, callbackParams) => {
    let {
      params,
      inviteParams,
      getAllData,
      mainTabId,
      windowId,
      sendResponse
    } = callbackParams
    if (data.code === 200) {
      inviteParams.creator = data.creatorList
      getAllData = data.getAllData || false

      // await updateWindowTabValue(mainTabId || 0, 'currentInviteNextPage', {
      //   currentPage: data.currentPage,
      //   offset: data.offset
      // })
    } else if (data.code === 404) {
      // 404情况需要判断下网红是否都获取完
      getAllData = data.getAllData || false
      inviteParams.creator = null
    }
    // 从页面获取的creator无需记录，每次的creator都是最新获取的
    delete inviteParams.ableInviteCreator

    // 去空
    if (inviteParams.creator)
      inviteParams.creator = inviteParams.creator.filter(item => item)
    if (!inviteParams.creator || inviteParams.creator.length === 0) {
      if (getAllData) {
        await inviteDone(mainTabId)
      } else {
        await stopCurrentTask(mainTabId, sendResponse)
      }
    }

    // 获取邀请者列表
    inviteParams.inviteCreator = inviteParams.creator

    // 去邀约
    await newTabToInvite(
      params,
      inviteParams,
      getAllData,
      mainTabId,
      windowId,
      sendResponse
    )
  },
  letterCallback: async (data, callbackParams) => {
    let {
      params,
      letterParams,
      letterCheckList,
      getAllData,
      mainTabId,
      windowId,
      sendResponse
    } = callbackParams
    let currentCreatorInfo = []
    if (data.code === 200) {
      const { creatorList, creatorListInfo } = data
      getAllData = data.getAllData || false
      letterParams.creator = creatorList
      currentCreatorInfo = creatorListInfo
      // await updateWindowTabValue(mainTabId || 0, 'currentLetterNextPage', {
      //   currentPage: data.currentPage,
      //   offset: data.offset,
      // })
    } else if (data.code === 404) {
      // 404情况需要判断下网红是否都获取完
      getAllData = data.getAllData || false
      letterParams.creator = null
    }
    delete letterParams.ableLetterCreator

    // 去空
    if (letterParams.creator)
      letterParams.creator = letterParams.creator.filter(item => item)
    if (!letterParams.creator || letterParams.creator.length === 0) {
      if (getAllData) {
        await letterDone(mainTabId)
      } else {
        await stopCurrentTask(mainTabId, sendResponse, '私信网红列表为空')
      }
    }

    // 当前私信用户
    letterParams.letterCreator = letterParams.creator
    // 开始私信==================
    await newTabToLetter(
      {
        currentCreator: letterParams.letterCreator[0],
        currentCreatorInfo: currentCreatorInfo[0],
        getAllData
      },
      mainTabId,
      windowId,
      params,
      letterParams,
      letterCheckList,
      sendResponse
    )
  }
}
// 接收从background页面传过来的获取创作者结果
const getCreatorListResult = async (res, backgroundParams) => {
  const {
    creatorBackgroundParams: { mainTabId, num, blackCreator, filters, source },
    otherBackgroundParams
  } = backgroundParams
  const { callbackName, callbackParams } = otherBackgroundParams
  const callback = getCreatorCallbackMap[callbackName]
  try {
    if (res.code === 200) {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'addProcessLoadingTest',
        string: `成功获取页面数据-${res.code}`
      })
    } else {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'addProcessLoadingTest',
        string: `${res.message}-${res.code}`
      })
    }
    const windowsTabRunning = await getRunningStatus(mainTabId || 0)
    if (!windowsTabRunning) {
      stopCurrentTask(mainTabId, null, '任务已停止')
      return
    }
    if (res.code === 200) {
      // 更新backgroundParams数据
      source.currentPage = res.data.currentPage || 0
      source.getAllData = res.data.getAllData || false
      source.offset = res.data.offset || 0
      const creatorListInfo = res.data.creatorListInfo
      const creatorList = creatorListInfo.map(item => item.name)
      console.log(
        '页面获取的数据 ==== ',
        creatorList,
        creatorListInfo,
        callback,
        callbackParams,
        otherBackgroundParams
      )
      // 服务端判断是否可以邀请
      const filterResult = await filterCreatorListByServer(
        creatorList,
        creatorListInfo,
        source,
        mainTabId,
        null
      ).catch(e => {
        throw new Error(e) // 抛出异常
      })

      if (!filterResult || filterResult.code === 500) {
        throw new Error(
          filterResult && filterResult.message
            ? filterResult.message
            : 'server error'
        )
      } else if (filterResult.code === 404) {
        // 没有网红
        callback(
          {
            code: 404,
            message:
              filterResult && filterResult.message
                ? filterResult.message
                : 'server error',
            getAllData: source.getAllData
          },
          callbackParams
        )
        return
      }

      if (
        (creatorList.length < 0 || creatorList.length === 0) &&
        !res.data.getAllData
      ) {
        // 重新获取
        getPageCreateList(
          mainTabId,
          num,
          blackCreator,
          filters,
          source,
          otherBackgroundParams
        )
      } else {
        callback(
          {
            code: 200,
            creatorList,
            creatorListInfo,
            currentPage: source.currentPage,
            getAllData: source.getAllData,
            offset: source.offset
          },
          callbackParams
        )
      }
    } else if (res.code === 505) {
      // 任务已停止
      return
    } else {
      await stopCurrentTask(mainTabId, null, res.message)
    }
  } catch (e) {
    stopCurrentTask(mainTabId, null, e.toString() || '接收网红出错')
  }
}

// 是否继续打开新的邀约标签页
const nextNewInviteTabFun = async (
  mainTabId,
  windowId,
  params,
  sendResponse
) => {
  const { inviteParams, inviteCheckList = {} } = params
  const windowsTabRunning = await getRunningStatus(mainTabId || 0)
  // 如果没有选择creator需要循环执行，直到达到目标数目
  if (
    !inviteCheckList.creator &&
    !inviteCheckList.selectCreator &&
    windowsTabRunning
  ) {
    // 关闭新打开的页面
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await executeInviteOperation(mainTabId, windowId, params, sendResponse)
    })
  } else if (
    (inviteCheckList.creator || inviteCheckList.selectCreator) &&
    inviteParams.ableInviteCreator &&
    inviteParams.ableInviteCreator.length > 0 &&
    windowsTabRunning
  ) {
    // 手动选择的用户如果超过50个循环执行
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await executeInviteOperation(mainTabId, windowId, params, sendResponse)
    })
  } else if (windowsTabRunning) {
    // 停止主程序，关闭新打开的页面
    chrome.tabs.update(mainTabId, { selected: true })
    await inviteDone(mainTabId)
  }
}
// 是否继续打开新的私信标签页
const nextNewLetterTabFun = async (
  mainTabId,
  windowId,
  params,
  sendResponse
) => {
  const { letterParams, letterCheckList = {} } = params
  const windowsTabRunning = await getRunningStatus(mainTabId || 0)
  // 停留随机时间
  await sleepFun(getRandomNumber(0, 3) * 1000)
  // 如果没有选择creator需要循环执行，直到达到目标数目
  console.log('letterParams=====', letterParams, letterCheckList)
  if (
    !letterCheckList.creator &&
    !letterCheckList.selectCreator &&
    windowsTabRunning
  ) {
    // 关闭新打开的页面
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await executeLetterOperation(mainTabId, windowId, params, sendResponse)
    })
  } else if (
    (letterCheckList.creator || letterCheckList.selectCreator) &&
    letterParams.ableLetterCreator &&
    letterParams.ableLetterCreator.length > 0 &&
    windowsTabRunning
  ) {
    // 手动选择的用户如果循环执行
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await executeLetterOperation(mainTabId, windowId, params, sendResponse)
    })
  } else if (windowsTabRunning) {
    // 停止主程序，关闭新打开的页面
    chrome.tabs.update(mainTabId, { selected: true })
    await letterDone(mainTabId)
  }
}

// ----------------------------------管理相关操作-----------------------------------
const startManage = async (
  mainTabId,
  windowId,
  tabUrl,
  params,
  sendResponse
) => {
  const { manageParams = {}, manageCheckList = {} } = params

  await updateWindowTabValue(mainTabId || 0, 'beforeFindManageData', null)

  if (tabUrl) {
    await updateWindowTabValue(mainTabId, 'currentTabUrl', tabUrl)
  }
  // 重定向到目标页面
  await reloadToTargetPage(mainTabId, '/connection/creator-management')
  let letterName = {
    zh: '批量私信',
    en: 'Batch message'
  }
  let inviteName = {
    zh: '批量邀约',
    en: 'Batch invite'
  }
  let actionName = manageCheckList.manageLetter ? letterName : inviteName
  let message = ''
  if (isChinese) {
    message = `${actionName.zh}任务开始执行，请不要关闭页面`
  } else {
    message = `${actionName.en} task started, please do not close the page`
  }
  notificationsFun(message, 3)

  await updateRunningStatus(mainTabId || 0, true)
  let select = true
  await selectEnglish(mainTabId, sendResponse).catch(() => {
    select = false
  })
  // 如果有筛选项在页面上填写筛选项
  const filtersLabel = manageParams.filtersLabel || null
  select &&
    manageCheckList.filters &&
    (await fillInManageFilterLabel(mainTabId, filtersLabel, sendResponse))
  // 批量私信操作
  manageCheckList.manageLetter &&
    select &&
    (await executeManageLetterOperation(
      mainTabId,
      windowId,
      params,
      sendResponse
    ))
}
// 填充筛选项
const fillInManageFilterLabel = (mainTabId, filtersLabel, sendResponse) => {
  console.log(
    'indowTabId, filtersPage, sendResponse',
    mainTabId,
    filtersLabel,
    sendResponse
  )
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(
      mainTabId,
      {
        action: 'fillInManageFilterLabel',
        tabId: mainTabId,
        filtersLabel
      },
      async res => {
        if (chrome.runtime.lastError) {
          console.log('chrome err', mainTabId, chrome.runtime.lastError)
          chromeRunningErrorTip()
          await stopCurrentTask(mainTabId, sendResponse, 'chrome err')
          reject('chrome error')
        }
        if (res && res.code === 200) {
          resolve(res.message)
        } else if (res && res.code === 505) {
          reject(res.message)
        } else {
          await stopCurrentTask(
            mainTabId,
            sendResponse,
            res ? res.message : 'fill in manage filter label error'
          )
          reject(res ? res.message : 'fill in manage filter label error')
        }
      }
    )
  })
}

// 执行管理操作
const executeManageLetterOperation = async (
  mainTabId,
  windowId,
  params,
  sendResponse
) => {
  try {
    const { manageParams = {}, manageCheckList = {} } = params
    const { filters = {} } = manageParams
    let getAllData = false
    let currentNextPage = await getWindowTabValue(
      mainTabId,
      'currentManageNextPage'
    )

    console.log('currentNextPage===', currentNextPage)
    // 需要从页面上获取网红

    const otherBackgroundParams = {
      callbackParams: {
        params,
        manageParams,
        manageCheckList,
        getAllData,
        mainTabId,
        windowId,
        sendResponse
      },
      callbackName: 'manageLetterCallback'
    }

    console.log(
      'manageParams===',
      manageParams,
      manageParams.manageLetter.onceCreator
    )
    // 去获取创作者
    await getManagePageCreateList(
      mainTabId,
      manageParams.manageLetter.onceCreator || 100,
      filters,
      {
        page: 'manage',
        note: manageCheckList.manageLetter ? 'batchLetter' : 'batchInvite',
        currentPage: (currentNextPage && currentNextPage.currentPage) || 1,
        offset: (currentNextPage && currentNextPage.offset) || 0,
        reqGap: 3,
        skipDay: manageParams.skipDay
      },
      otherBackgroundParams
    )
  } catch (e) {
    console.log('e', e)
    await stopCurrentTask(mainTabId, sendResponse, e.toString())
  }
}

// 从manage页面获取creator
const getManagePageCreateList = async (
  mainTabId,
  num,
  filters,
  source,
  otherBackgroundParams
) => {
  return new Promise((resolve, reject) => {
    // -------------------12.17---------------------
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `去页面抓取网红`
    })
    const backgroundParams = {
      otherBackgroundParams,
      creatorBackgroundParams: {
        mainTabId,
        num,
        filters,
        source
      }
    }

    // 需要向页面请求获取数据
    if (mainTabId) {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'getManageCreatorList',
        mainTabId: mainTabId || 0,
        shopRegion: shopRegion,
        shopId: shopId,
        params: {
          num,
          filters,
          currentPage: source.currentPage,
          offset: source.offset,
          page: source.page,
          reqGap: source.reqGap
        },
        backgroundParams // backgroundParams做传递使用，在content.js中不操作
      })
    } else {
      console.log('no tab')
      reject(null)
    }
  })
}

const manageDone = async (mainTabId, type) => {
  await updateRunningStatus(mainTabId || 0, false)
  await updateWindowTabValue(mainTabId || 0, 'currentManageNextPage', {
    currentPage: 0,
    offset: 0
  })
  await updateWindowTabValue(mainTabId || 0, 'beforeFindManageData', null)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })

  let letterName = {
    zh: '批量私信',
    en: 'Batch message'
  }
  let inviteName = {
    zh: '批量邀约',
    en: 'Batch invite'
  }
  let actionName = type === 'letter' ? letterName : inviteName

  let message = ''
  if (isChinese) {
    message = `${actionName.zh}任务完成`
  } else {
    message = `${actionName.en} task completed`
  }
  notificationsFun(message)

  await sleepFun(1000)
  // 清空页面插入元素
  chrome.tabs.sendMessage(mainTabId, {
    action: 'clearCreatorLoadingContainer'
  })
}

const newTabToManageLetter = async (
  mainTabId,
  windowId,
  params,
  manageParams,
  manageCheckList,
  getAllData,
  sendResponse
) => {
  const tabUrl = await getWindowTabValue(mainTabId, 'currentTabUrl')

  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `正在批量私信...`
    })

    console.log('manageParams', manageParams)
    const creatorList = manageParams.creator
    if (!creatorList || !creatorList.length) {
      reject('no manage create')
    }

    // 拼接query参数
    let queryString = ''
    creatorList.forEach((item, index) => {
      if (!index) {
        queryString += `creator_ids[${index}]=${item.id}`
      } else {
        queryString += `&creator_ids[${index}]=${item.id}`
      }
    })

    console.log('queryString', queryString)

    // 跳转到批量私信页面
    chrome.tabs.create(
      {
        active: true,
        url: `${tabUrl}/connection/creator-management/bulk-im?${queryString}&shop_region=${shopRegion}`,
        openerTabId: mainTabId,
        windowId: windowId
      },
      tab => {
        const onCompletedListener = async details => {
          console.log('页面加载成功啦===')

          if (!details || !tab) return
          if (details.tabId === tab.id) {
            // 子Tab的running和父Tab的running保持一致
            await updateRunningStatus(tab.id || 0, true)
            // 存储当前父Tab存储的子Tab的id
            await updateWindowTabValue(mainTabId, 'currentSubTab', tab.id)
            // 存储当前子页面对应的父页面的id
            await updateWindowTabValue(tab.id, 'currentParentTab', mainTabId)

            try {
              chrome.tabs.sendMessage(tab.id, {
                action: 'newTabToManageLetter',
                tabId: tab.id,
                params: {
                  manageParams: manageParams,
                  tabId: tab.id
                },
                // 为批量私信后的结果提供参数，只是传递下在content中一般不会动
                backgroundParams: {
                  mainTabId,
                  windowId,
                  subTabId: tab.id,
                  params,
                  sendResponse,
                  getAllData
                }
              })
            } catch (e) {
              console.log('e', e)
              await updateRunningStatus(tab.id || 0, false)
              await stopCurrentTask(mainTabId, sendResponse, e.toString())
              reject(e)
            }

            // 移除监听器
            chrome.webNavigation.onCompleted.removeListener(onCompletedListener)
          }
        }
        chrome.webNavigation.onCompleted.addListener(onCompletedListener)
      }
    )
  })
}

// ---------------------------------管理操 作接收从background页面传过来的处理------------

const getManageCreatorCallbackMap = {
  manageLetterCallback: async (data, callbackParams) => {
    let {
      params,
      manageParams,
      manageCheckList,
      getAllData,
      mainTabId,
      windowId,
      sendResponse
    } = callbackParams
    if (data.code === 200) {
      const { creatorListInfo } = data
      getAllData = data.getAllData || false
      manageParams.creator = creatorListInfo
    } else if (data.code === 404) {
      // 404情况需要判断下网红是否都获取完
      getAllData = data.getAllData || false
      manageParams.creator = null
    }

    console.log('这里===2', manageParams)
    // 去空
    if (manageParams.creator)
      manageParams.creator = manageParams.creator.filter(item => item.id)
    if (!manageParams.creator || manageParams.creator.length === 0) {
      if (getAllData) {
        await manageDone(mainTabId, 'letter')
      } else {
        await stopCurrentTask(mainTabId, sendResponse, '私信网红列表为空')
      }
      return
    }

    // 当前私信用户
    // 开始批量私信==================
    await newTabToManageLetter(
      mainTabId,
      windowId,
      params,
      manageParams,
      manageCheckList,
      getAllData,
      sendResponse
    )
  }
}
// 接收从background页面传过来的获取创作者结果
const getManageCreatorListResult = async (res, backgroundParams) => {
  console.log('backgroundParams=======', backgroundParams)
  const {
    creatorBackgroundParams: { mainTabId, num, filters, source },
    otherBackgroundParams
  } = backgroundParams
  const { callbackName, callbackParams } = otherBackgroundParams
  const callback = getManageCreatorCallbackMap[callbackName]
  try {
    if (res.code === 200) {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'addProcessLoadingTest',
        string: `成功获取页面数据-${res.code}`
      })
    } else {
      chrome.tabs.sendMessage(mainTabId, {
        action: 'addProcessLoadingTest',
        string: `${res.message}-${res.code}`
      })
    }
    const windowsTabRunning = await getRunningStatus(mainTabId || 0)
    if (!windowsTabRunning) {
      stopCurrentTask(mainTabId, null, '任务已停止')
      return
    }
    if (res.code === 200) {
      // 更新backgroundParams数据
      source.currentPage = res.data.currentPage || 0
      source.getAllData = res.data.getAllData || false
      source.offset = res.data.offset || 0
      const creatorListInfo = res.data.creatorListInfo
      const creatorList = creatorListInfo.map(item => item.name)
      console.log(
        '页面获取的数据manage ==== ',
        creatorList,
        creatorListInfo,
        callback,
        callbackParams,
        otherBackgroundParams
      )
      // 服务端判断是否可以邀请
      const filterResult = await filterCreatorListByServer(
        creatorList,
        creatorListInfo,
        source,
        mainTabId,
        null
      ).catch(e => {
        throw new Error(e) // 抛出异常
      })

      if (!filterResult || filterResult.code === 500) {
        throw new Error(
          filterResult && filterResult.message
            ? filterResult.message
            : 'server error'
        )
      } else if (filterResult.code === 404) {
        // 没有网红
        callback(
          {
            code: 404,
            message:
              filterResult && filterResult.message
                ? filterResult.message
                : 'server error',
            getAllData: source.getAllData
          },
          callbackParams
        )
        return
      }

      if (creatorList.length < num && !source.getAllData) {
        // 重新获取
        getManagePageCreateList(
          mainTabId,
          num,
          filters,
          source,
          otherBackgroundParams
        )
      } else {
        callback(
          {
            code: 200,
            creatorList,
            creatorListInfo,
            currentPage: source.currentPage,
            getAllData: source.getAllData,
            offset: source.offset
          },
          callbackParams
        )
      }
    } else if (res.code === 505) {
      // 任务已停止
      return
    } else {
      await stopCurrentTask(mainTabId, null, res.message)
    }
  } catch (e) {
    stopCurrentTask(mainTabId, null, e.toString() || '接收网红出错')
  }
}

//
// 接收从background页面传过来的批量私信结果
const manageLetterResultFun = async (res, backgroundParams) => {
  console.log(
    '完成一次邀约',
    'manageLetterResultFun-----------backgroundParams',
    res,
    backgroundParams
  )
  const { mainTabId, windowId, params, sendResponse, subTabId, getAllData } =
    backgroundParams

  console.log('getAllData', getAllData)
  if (getAllData) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前tab(私信不管是否成功都关闭)
    await closeNewTab(subTabId || 0)
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await manageDone(mainTabId, 'letter')
    })
    return
  }
  if (res.code === 200 || res.code === 201) {
    await updateRunningStatus(subTabId || 0, false)
    // 关闭当前tab
    if (res.code === 200) await closeNewTab(subTabId || 0)
    // 激活主tab，执行下一个操作
    chrome.tabs.update(mainTabId, { selected: true }, async () => {
      await executeManageLetterOperation(
        mainTabId,
        windowId,
        params,
        sendResponse
      )
    })
  } else {
    await closeNewTab(subTabId || 0)
    // 触发stop操作
    await stopCurrentTask(mainTabId, sendResponse, res.message)
  }
}

// ----------------------------------搜索------------------------------------------
// eslint-disable-next-line no-unused-vars
const searchGetCreatorList = (mainTabId, keyWord) => {
  return new Promise((resolve, reject) => {
    chrome.tabs.sendMessage(
      mainTabId,
      {
        action: 'searchGetCreatorList',
        params: {
          keyWord
        }
      },
      res => {
        if (res.code === 200) {
          resolve(res.message)
        } else {
          reject(res.message)
        }
      }
    )
  })
}
// 判断

/**
 * 200：正常返回并提示信息
 * 500: 常规错误
 * 501: chrome运行错误
 *
 *
 */
//  ----------------------------- store相关操作 ------------------------
// 获取running数据
const getRunningStatus = async mainTabId => {
  return await chrome.storage.local
    .get(['running'])
    .then(store => {
      if (store && store.running) {
        const runningList = store.running
        return runningList[mainTabId] || false
      }
      return false
    })
    .catch(err => {
      console.log('storage err', err)
    })
}
// 更新running数据
const updateRunningStatus = async (mainTabId, run) => {
  // 存储之前过滤一批，不在运行的running都删除，如果不清除的话就会比较占用空间

  const remainingRun = {}
  await chrome.storage.local
    .get(['running'])
    .then(store => {
      if (store && store.running) {
        const runningList = store.running
        Object.keys(runningList).forEach(tabKey => {
          if (runningList[tabKey]) {
            remainingRun[tabKey] = runningList[tabKey]
          }
        })
      }
    })
    .catch(err => {
      console.log('storage err', err)
    })

  chrome.storage.local
    .set({
      running: {
        ...remainingRun,
        [`${mainTabId}`]: run
      }
    })
    .then(() => {
      // 通知页面更新running状态
      updateRunningStatusToPage(mainTabId, run)
    })
    .catch(err => {
      console.log('storage err', err)
    })
}

// 存储任意变量
const setStoreSignalVar = (key, value) => {
  chrome.storage.local
    .set({
      [key]: value
    })
    .catch(err => {
      console.log('storage err', err)
    })
}

/**
 *  存储当前windowTab对应的一些信息
 * 当前运行的子页面 -currentSubTab
 * 收藏夹对应的值 - invite
 */
const updateWindowTabValue = async (mainTabId, storeKey, currentValue) => {
  try {
    // 存储之前过滤一批，不在运行的running都删除，如果不清除的话就会比较占用空间
    const remainingRunKeyList = []
    await chrome.storage.local
      .get(['running'])
      .then(store => {
        if (store && store.running) {
          const runningList = store.running
          Object.keys(runningList).forEach(tabKey => {
            if (runningList[tabKey]) {
              remainingRunKeyList.push(tabKey)
            }
          })
        }
      })
      .catch(err => {
        console.log('storage err', err)
      })
    const remainingLastCreator = {}
    await chrome.storage.local
      .get([storeKey])
      .then(store => {
        if (store && store[storeKey]) {
          const lastCreatorList = store[storeKey]
          Object.keys(lastCreatorList).forEach(tabKey => {
            if (remainingRunKeyList.includes(tabKey)) {
              remainingLastCreator[tabKey] = lastCreatorList[tabKey]
            }
          })
        }
      })
      .catch(err => {
        console.log('storage err', err)
      })

    await chrome.storage.local
      .set({
        [storeKey]: {
          ...remainingLastCreator,
          [mainTabId]: currentValue
        }
      })
      .catch(err => {
        console.log('storage err', err)
      })
  } catch (e) {
    chrome.tabs.sendMessage(mainTabId, {
      action: 'addProcessLoadingTest',
      string: `浏览器更新数据出错`
    })
    console.log('e', e)
    await stopCurrentTask(mainTabId)
  }
}
// 获取LastCreator值
// 获取当前windowTab对应的一些信息
const getWindowTabValue = async (mainTabId, storeKey) => {
  return await chrome.storage.local
    .get([storeKey])
    .then(store => {
      if (store && store[storeKey]) {
        const lastCreatorList = store[storeKey]
        return lastCreatorList[mainTabId] || null
      } else {
        return null
      }
    })
    .catch(err => {
      console.log('storage err', err)
    })
}

//  ----------------------mouseDown模拟点击跳过限制----------------
const impMousedownToClick = (params, sender, sendResponse) => {
  chrome.debugger.attach({ tabId: sender.tab.id }, '1.2', function () {
    let flag = true
    // sendResponse({ yourEvent: '正在调整, 需要时间生效' })
    const xC = params.x
    const yC = params.y
    chrome.debugger.sendCommand(
      { tabId: sender.tab.id },
      'Input.dispatchMouseEvent',
      { type: 'mousePressed', x: xC, y: yC, button: 'left', clickCount: 1 },
      function () {
        if (chrome.runtime.lastError) {
          console.error(chrome.runtime.lastError.message)
          flag = false
          return
        }
      }
    )
    chrome.debugger.sendCommand(
      { tabId: sender.tab.id },
      'Input.dispatchMouseEvent',
      {
        type: 'mouseReleased',
        x: xC,
        y: yC,
        button: 'left',
        clickCount: 1
      },
      function () {
        if (chrome.runtime.lastError) {
          console.error(chrome.runtime.lastError.message)
          flag = false
          return
        }
        if (flag) {
          sendResponse({ code: 200, message: '点击成功' })
        } else {
          sendResponse({ code: 500, message: '点击失败' })
        }
        chrome.debugger.detach({ tabId: sender.tab.id }, () => {
          console.log('取消attach')
        })
      }
    )
  })
}

// chrome错误提示
const chromeRunningErrorTip = () => {
  let message = ''
  if (isChinese) {
    message = 'chrome运行错误,请稍后再试'
  } else {
    message = 'chrome is running incorrectly, please try again later'
  }
  notificationsFun(message)
}
//  弹窗通知
const notificationsFun = (message, time = null) => {
  chrome.notifications.create(
    {
      type: 'basic',
      iconUrl: 'noxinfluencer_128.png',
      title: '',
      message: message,
      priority: 2,
      requireInteraction: time ? false : true
    },
    function (notificationId) {
      if (chrome.runtime.lastError) {
        console.error(chrome.runtime.lastError.message)
      } else {
        // 设置一个定时器，在 60 秒后清除通知
        // 设置定时操作
        if (time)
          chrome.alarms.create(`clearNotificationAlarm|${notificationId}`, {
            when: Date.now() + time * 1000
          })
      }
    }
  )
}
// 设置用户点击才能关闭弹窗
// 添加点击事件监听，当用户点击时关闭通知
chrome.notifications.onClicked.addListener(function (notificationId) {
  chrome.notifications.clear(notificationId)
})
// 获取url参数
function getQueryParams(url) {
  const queryPosition = url.indexOf('?')
  var queryString = url.slice(queryPosition + 1)
  var queryArray = queryString.split('&')
  const queryParams = {}

  queryArray.forEach(function (param) {
    var pair = param.split('=')
    var key = decodeURIComponent(pair[0])
    var value = decodeURIComponent(pair[1] || '')
    queryParams[key] = value
  })

  return queryParams
}

// sleep函数
async function sleepFun(time) {
  await new Promise(resolve => {
    setTimeout(() => {
      resolve()
    }, time)
  })
}

// 深拷贝
function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }
  let copy = Array.isArray(obj) ? [] : {}
  for (let key in obj) {
    // eslint-disable-next-line
    if (obj.hasOwnProperty && obj.hasOwnProperty(key)) {
      copy[key] = deepClone(obj[key]) // 递归拷贝每个属性
    }
  }
  return copy
}
// min-max的随机数
function getRandomNumber(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 插件自动更新
chrome.runtime.onUpdateAvailable.addListener(() => {
  chrome.runtime.reload()
})
