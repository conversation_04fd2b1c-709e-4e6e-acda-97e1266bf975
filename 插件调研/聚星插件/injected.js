;(function () {
  var XHR = XMLHttpRequest.prototype

  var open = XHR.open
  var send = XHR.send

  XHR.open = function (method, url) {
    this._method = method
    this._url = url
    return open.apply(this, arguments)
  }

  XHR.send = function () {
    // 测试接口
    if (this._url.includes('/ws/v2/youtube/star/search')) {
      this.addEventListener('load', function () {
        window.postMessage({ type: 'xhr-search', data: this.response }, '*') // 将响应发送到 content script
      })
    } else if (this._url.includes('/account/info')) {
      // 店铺基本信息
      this.addEventListener('load', function () {
        const shopInfoRes = JSON.parse(this.response)
        let shopId = ''
        if (shopInfoRes.code === 0) {
          if (shopInfoRes.shop_info && shopInfoRes.shop_info.length) {
            shopId = shopInfoRes.shop_info[0].shop_id
          } else {
            shopId = shopInfoRes['binding_seller_id']
          }
        }
        window.postMessage(
          {
            type: 'xhr-accountinfo',
            data: {
              shopId
            }
          },
          '*'
        )
      })
    } else if (
      this._url.includes('api/v1/oec/affiliate/creator/marketplace/find') ||
      this._url.includes('api/v1/oec/affiliate/creator/marketplace/search') ||
      this._url.includes(
        'api/v1/oec/affiliate/creator/marketplace/recommendation'
      )
    ) {
      // 捕获搜素接口，为了初始化网红选择框
      this.addEventListener('load', function () {
        window.postMessage(
          {
            type: 'xhr-findcreator',
            data: {
              creator: JSON.parse(this.response)
            }
          },
          '*'
        )
      })
    } else if (
      this._url.includes('api/v1/oec/affiliate/seller/invitation_group/create')
    ) {
      // 邀请的创建接口
      this.addEventListener('load', function () {
        window.postMessage(
          {
            type: 'xhr-createinvite',
            data: JSON.parse(this.response)
          },
          '*'
        )
      })
    } else if (
      this._url.includes('api/v1/affiliate/lux/creator/auth_profiles')
    ) {
      // letter页面是否加载成功
      this.addEventListener('load', function () {
        window.postMessage(
          {
            type: 'xhr-authprofiles',
            data: JSON.parse(this.response)
          },
          '*'
        )
      })
    } else if (this._url.includes('api/v1/affiliate/product_selection/list')) {
      // 邀约页面搜索接口
      this.addEventListener('load', function () {
        window.postMessage(
          {
            type: 'xhr-invitation-group-search',
            data: {
              url: this._url
            }
          },
          '*'
        )
      })
    }

    return send.apply(this, arguments)
  }
})(XMLHttpRequest)

// 解析 URL 查询参数的函数
// eslint-disable-next-line no-unused-vars
function getQueryParams(url) {
  var queryParams = {}
  var parser = document.createElement('a')
  parser.href = url

  var queryString = parser.search.substring(1)
  var queryArray = queryString.split('&')

  queryArray.forEach(function (param) {
    var pair = param.split('=')
    var key = decodeURIComponent(pair[0])
    var value = decodeURIComponent(pair[1] || '')
    queryParams[key] = value
  })

  return queryParams
}
