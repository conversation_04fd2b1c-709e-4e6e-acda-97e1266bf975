var _=function(){"use strict";var t={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},e="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",i={5:e,"5module":e+" export import",6:e+" const class extends export import super"},s=/^in(stanceof)?$/,r="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙՠ-ֈא-תׯ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࡠ-ࡪࢠ-ࢴࢶ-ࣇऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱৼਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഄ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄຆ-ຊຌ-ຣລວ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡸᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᲐ-ᲺᲽ-Ჿᳩ-ᳬᳮ-ᳳᳵᳶᳺᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄯㄱ-ㆎㆠ-ㆿㇰ-ㇿ㐀-䶿一-鿼ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞿꟂ-ꟊꟵ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꣾꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭩꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",n="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߽߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛࣓-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯৾ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ૺ-૿ଁ-ଃ଼ା-ୄେୈୋ-୍୕-ୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఄా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഀ-ഃ഻഼ാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ඁ-ඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᪿᫀᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭᳴᳷-᳹᷀-᷹᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧ꠬ꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱ꣿ-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",a=new RegExp("["+r+"]"),o=new RegExp("["+r+n+"]");r=n=null;var h=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,14,29,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,28,43,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,14,35,349,41,7,1,79,28,11,0,9,21,107,20,28,22,13,52,76,44,33,24,27,35,30,0,3,0,9,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,21,2,31,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,14,0,72,26,230,43,117,63,32,7,3,0,3,7,2,1,2,23,16,0,2,0,95,7,3,38,17,0,2,0,29,0,11,39,8,0,22,0,12,45,20,0,35,56,264,8,2,36,18,0,50,29,113,6,2,1,2,37,22,0,26,5,2,1,2,31,15,0,328,18,190,0,80,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,689,63,129,74,6,0,67,12,65,1,2,0,29,6135,9,1237,43,8,8952,286,50,2,18,3,9,395,2309,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,2357,44,11,6,17,0,370,43,1301,196,60,67,8,0,1205,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42717,35,4148,12,221,3,5761,15,7472,3104,541,1507,4938],c=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,574,3,9,9,370,1,154,10,176,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,6,1,45,0,13,2,49,13,9,3,2,11,83,11,7,0,161,11,6,9,7,3,56,1,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,5,0,82,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,243,14,166,9,71,5,2,1,3,3,2,0,2,1,13,9,120,6,3,6,4,0,29,9,41,6,2,3,9,0,10,10,47,15,406,7,2,7,17,9,57,21,2,13,123,5,4,0,2,1,2,6,2,0,9,9,49,4,2,1,2,4,9,9,330,3,19306,9,135,4,60,6,26,9,1014,0,2,54,8,3,82,0,12,1,19628,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,262,6,10,9,419,13,1495,6,110,6,6,9,4759,9,787719,239];function p(t,e){for(var i=65536,s=0;s<e.length;s+=2){if((i+=e[s])>t)return!1;if((i+=e[s+1])>=t)return!0}}function l(t,e){return t<65?36===t:t<91||(t<97?95===t:t<123||(t<=65535?t>=170&&a.test(String.fromCharCode(t)):!1!==e&&p(t,h)))}function u(t,e){return t<48?36===t:t<58||!(t<65)&&(t<91||(t<97?95===t:t<123||(t<=65535?t>=170&&o.test(String.fromCharCode(t)):!1!==e&&(p(t,h)||p(t,c)))))}var d=function(t,e){void 0===e&&(e={}),this.label=t,this.keyword=e.keyword,this.beforeExpr=!!e.beforeExpr,this.startsExpr=!!e.startsExpr,this.isLoop=!!e.isLoop,this.isAssign=!!e.isAssign,this.prefix=!!e.prefix,this.postfix=!!e.postfix,this.binop=e.binop||null,this.updateContext=null};function f(t,e){return new d(t,{beforeExpr:!0,binop:e})}var m={beforeExpr:!0},x={startsExpr:!0},g={};function y(t,e){return void 0===e&&(e={}),e.keyword=t,g[t]=new d(t,e)}var v={num:new d("num",x),regexp:new d("regexp",x),string:new d("string",x),name:new d("name",x),eof:new d("eof"),bracketL:new d("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new d("]"),braceL:new d("{",{beforeExpr:!0,startsExpr:!0}),braceR:new d("}"),parenL:new d("(",{beforeExpr:!0,startsExpr:!0}),parenR:new d(")"),comma:new d(",",m),semi:new d(";",m),colon:new d(":",m),dot:new d("."),question:new d("?",m),questionDot:new d("?."),arrow:new d("=>",m),template:new d("template"),invalidTemplate:new d("invalidTemplate"),ellipsis:new d("...",m),backQuote:new d("`",x),dollarBraceL:new d("${",{beforeExpr:!0,startsExpr:!0}),eq:new d("=",{beforeExpr:!0,isAssign:!0}),assign:new d("_=",{beforeExpr:!0,isAssign:!0}),incDec:new d("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new d("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:f("||",1),logicalAND:f("&&",2),bitwiseOR:f("|",3),bitwiseXOR:f("^",4),bitwiseAND:f("&",5),equality:f("==/!=/===/!==",6),relational:f("</>/<=/>=",7),bitShift:f("<</>>/>>>",8),plusMin:new d("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:f("%",10),star:f("*",10),slash:f("/",10),starstar:new d("**",{beforeExpr:!0}),coalesce:f("??",1),_break:y("break"),_case:y("case",m),_catch:y("catch"),_continue:y("continue"),_debugger:y("debugger"),_default:y("default",m),_do:y("do",{isLoop:!0,beforeExpr:!0}),_else:y("else",m),_finally:y("finally"),_for:y("for",{isLoop:!0}),_function:y("function",x),_if:y("if"),_return:y("return",m),_switch:y("switch"),_throw:y("throw",m),_try:y("try"),_var:y("var"),_const:y("const"),_while:y("while",{isLoop:!0}),_with:y("with"),_new:y("new",{beforeExpr:!0,startsExpr:!0}),_this:y("this",x),_super:y("super",x),_class:y("class",x),_extends:y("extends",m),_export:y("export"),_import:y("import",x),_null:y("null",x),_true:y("true",x),_false:y("false",x),_in:y("in",{beforeExpr:!0,binop:7}),_instanceof:y("instanceof",{beforeExpr:!0,binop:7}),_typeof:y("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:y("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:y("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},b=/\r\n?|\n|\u2028|\u2029/,S=new RegExp(b.source,"g");function k(t,e){return 10===t||13===t||!e&&(8232===t||8233===t)}var C=/[\u1680\u2000-\u200a\u202f\u205f\u3000\ufeff]/,E=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,_=Object.prototype,w=_.hasOwnProperty,I=_.toString;function A(t,e){return w.call(t,e)}var P=Array.isArray||function(t){return"[object Array]"===I.call(t)};function T(t){return new RegExp("^(?:"+t.replace(/ /g,"|")+")$")}var N=function(t,e){this.line=t,this.column=e};N.prototype.offset=function(t){return new N(this.line,this.column+t)};var V=function(t,e,i){this.start=e,this.end=i,null!==t.sourceFile&&(this.source=t.sourceFile)};function L(t,e){for(var i=1,s=0;;){S.lastIndex=s;var r=S.exec(t);if(!(r&&r.index<e))return new N(i,e-s);++i,s=r.index+r[0].length}}var R={ecmaVersion:10,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowAwaitOutsideFunction:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1};function O(t){var e={};for(var i in R)e[i]=t&&A(t,i)?t[i]:R[i];if(e.ecmaVersion>=2015&&(e.ecmaVersion-=2009),null==e.allowReserved&&(e.allowReserved=e.ecmaVersion<5),P(e.onToken)){var s=e.onToken;e.onToken=function(t){return s.push(t)}}return P(e.onComment)&&(e.onComment=function(t,e){return function(i,s,r,n,a,o){var h={type:i?"Block":"Line",value:s,start:r,end:n};t.locations&&(h.loc=new V(this,a,o)),t.ranges&&(h.range=[r,n]),e.push(h)}}(e,e.onComment)),e}function D(t,e){return 2|(t?4:0)|(e?8:0)}var F=function(e,s,r){this.options=e=O(e),this.sourceFile=e.sourceFile,this.keywords=T(i[e.ecmaVersion>=6?6:"module"===e.sourceType?"5module":5]);var n="";if(!0!==e.allowReserved){for(var a=e.ecmaVersion;!(n=t[a]);a--);"module"===e.sourceType&&(n+=" await")}this.reservedWords=T(n);var o=(n?n+" ":"")+t.strict;this.reservedWordsStrict=T(o),this.reservedWordsStrictBind=T(o+" "+t.strictBind),this.input=String(s),this.containsEsc=!1,r?(this.pos=r,this.lineStart=this.input.lastIndexOf("\n",r-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(b).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=v.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.yieldPos=this.awaitPos=this.awaitIdentPos=0,this.labels=[],this.undefinedExports={},0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterScope(1),this.regexpState=null},M={inFunction:{configurable:!0},inGenerator:{configurable:!0},inAsync:{configurable:!0},allowSuper:{configurable:!0},allowDirectSuper:{configurable:!0},treatFunctionsAsVar:{configurable:!0}};F.prototype.parse=function(){var t=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(t)},M.inFunction.get=function(){return(2&this.currentVarScope().flags)>0},M.inGenerator.get=function(){return(8&this.currentVarScope().flags)>0},M.inAsync.get=function(){return(4&this.currentVarScope().flags)>0},M.allowSuper.get=function(){return(64&this.currentThisScope().flags)>0},M.allowDirectSuper.get=function(){return(128&this.currentThisScope().flags)>0},M.treatFunctionsAsVar.get=function(){return this.treatFunctionsAsVarInScope(this.currentScope())},F.prototype.inNonArrowFunction=function(){return(2&this.currentThisScope().flags)>0},F.extend=function(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];for(var i=this,s=0;s<t.length;s++)i=t[s](i);return i},F.parse=function(t,e){return new this(e,t).parse()},F.parseExpressionAt=function(t,e,i){var s=new this(i,t,e);return s.nextToken(),s.parseExpression()},F.tokenizer=function(t,e){return new this(e,t)},Object.defineProperties(F.prototype,M);var B=F.prototype,U=/^(?:'((?:\\.|[^'\\])*?)'|"((?:\\.|[^"\\])*?)")/;function H(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=this.doubleProto=-1}B.strictDirective=function(t){for(;;){E.lastIndex=t,t+=E.exec(this.input)[0].length;var e=U.exec(this.input.slice(t));if(!e)return!1;if("use strict"===(e[1]||e[2])){E.lastIndex=t+e[0].length;var i=E.exec(this.input),s=i.index+i[0].length,r=this.input.charAt(s);return";"===r||"}"===r||b.test(i[0])&&!(/[(`.[+\-/*%<>=,?^&]/.test(r)||"!"===r&&"="===this.input.charAt(s+1))}t+=e[0].length,E.lastIndex=t,t+=E.exec(this.input)[0].length,";"===this.input[t]&&t++}},B.eat=function(t){return this.type===t&&(this.next(),!0)},B.isContextual=function(t){return this.type===v.name&&this.value===t&&!this.containsEsc},B.eatContextual=function(t){return!!this.isContextual(t)&&(this.next(),!0)},B.expectContextual=function(t){this.eatContextual(t)||this.unexpected()},B.canInsertSemicolon=function(){return this.type===v.eof||this.type===v.braceR||b.test(this.input.slice(this.lastTokEnd,this.start))},B.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},B.semicolon=function(){this.eat(v.semi)||this.insertSemicolon()||this.unexpected()},B.afterTrailingComma=function(t,e){if(this.type===t)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),e||this.next(),!0},B.expect=function(t){this.eat(t)||this.unexpected()},B.unexpected=function(t){this.raise(null!=t?t:this.start,"Unexpected token")},B.checkPatternErrors=function(t,e){if(t){t.trailingComma>-1&&this.raiseRecoverable(t.trailingComma,"Comma is not permitted after the rest element");var i=e?t.parenthesizedAssign:t.parenthesizedBind;i>-1&&this.raiseRecoverable(i,"Parenthesized pattern")}},B.checkExpressionErrors=function(t,e){if(!t)return!1;var i=t.shorthandAssign,s=t.doubleProto;if(!e)return i>=0||s>=0;i>=0&&this.raise(i,"Shorthand property assignments are valid only in destructuring patterns"),s>=0&&this.raiseRecoverable(s,"Redefinition of __proto__ property")},B.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},B.isSimpleAssignTarget=function(t){return"ParenthesizedExpression"===t.type?this.isSimpleAssignTarget(t.expression):"Identifier"===t.type||"MemberExpression"===t.type};var j=F.prototype;j.parseTopLevel=function(t){var e={};for(t.body||(t.body=[]);this.type!==v.eof;){var i=this.parseStatement(null,!0,e);t.body.push(i)}if(this.inModule)for(var s=0,r=Object.keys(this.undefinedExports);s<r.length;s+=1){var n=r[s];this.raiseRecoverable(this.undefinedExports[n].start,"Export '"+n+"' is not defined")}return this.adaptDirectivePrologue(t.body),this.next(),t.sourceType=this.options.sourceType,this.finishNode(t,"Program")};var G={kind:"loop"},q={kind:"switch"};j.isLet=function(t){if(this.options.ecmaVersion<6||!this.isContextual("let"))return!1;E.lastIndex=this.pos;var e=E.exec(this.input),i=this.pos+e[0].length,r=this.input.charCodeAt(i);if(91===r)return!0;if(t)return!1;if(123===r)return!0;if(l(r,!0)){for(var n=i+1;u(this.input.charCodeAt(n),!0);)++n;var a=this.input.slice(i,n);if(!s.test(a))return!0}return!1},j.isAsyncFunction=function(){if(this.options.ecmaVersion<8||!this.isContextual("async"))return!1;E.lastIndex=this.pos;var t=E.exec(this.input),e=this.pos+t[0].length;return!(b.test(this.input.slice(this.pos,e))||"function"!==this.input.slice(e,e+8)||e+8!==this.input.length&&u(this.input.charAt(e+8)))},j.parseStatement=function(t,e,i){var s,r=this.type,n=this.startNode();switch(this.isLet(t)&&(r=v._var,s="let"),r){case v._break:case v._continue:return this.parseBreakContinueStatement(n,r.keyword);case v._debugger:return this.parseDebuggerStatement(n);case v._do:return this.parseDoStatement(n);case v._for:return this.parseForStatement(n);case v._function:return t&&(this.strict||"if"!==t&&"label"!==t)&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(n,!1,!t);case v._class:return t&&this.unexpected(),this.parseClass(n,!0);case v._if:return this.parseIfStatement(n);case v._return:return this.parseReturnStatement(n);case v._switch:return this.parseSwitchStatement(n);case v._throw:return this.parseThrowStatement(n);case v._try:return this.parseTryStatement(n);case v._const:case v._var:return s=s||this.value,t&&"var"!==s&&this.unexpected(),this.parseVarStatement(n,s);case v._while:return this.parseWhileStatement(n);case v._with:return this.parseWithStatement(n);case v.braceL:return this.parseBlock(!0,n);case v.semi:return this.parseEmptyStatement(n);case v._export:case v._import:if(this.options.ecmaVersion>10&&r===v._import){E.lastIndex=this.pos;var a=E.exec(this.input),o=this.pos+a[0].length,h=this.input.charCodeAt(o);if(40===h||46===h)return this.parseExpressionStatement(n,this.parseExpression())}return this.options.allowImportExportEverywhere||(e||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),r===v._import?this.parseImport(n):this.parseExport(n,i);default:if(this.isAsyncFunction())return t&&this.unexpected(),this.next(),this.parseFunctionStatement(n,!0,!t);var c=this.value,p=this.parseExpression();return r===v.name&&"Identifier"===p.type&&this.eat(v.colon)?this.parseLabeledStatement(n,c,p,t):this.parseExpressionStatement(n,p)}},j.parseBreakContinueStatement=function(t,e){var i="break"===e;this.next(),this.eat(v.semi)||this.insertSemicolon()?t.label=null:this.type!==v.name?this.unexpected():(t.label=this.parseIdent(),this.semicolon());for(var s=0;s<this.labels.length;++s){var r=this.labels[s];if(null==t.label||r.name===t.label.name){if(null!=r.kind&&(i||"loop"===r.kind))break;if(t.label&&i)break}}return s===this.labels.length&&this.raise(t.start,"Unsyntactic "+e),this.finishNode(t,i?"BreakStatement":"ContinueStatement")},j.parseDebuggerStatement=function(t){return this.next(),this.semicolon(),this.finishNode(t,"DebuggerStatement")},j.parseDoStatement=function(t){return this.next(),this.labels.push(G),t.body=this.parseStatement("do"),this.labels.pop(),this.expect(v._while),t.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(v.semi):this.semicolon(),this.finishNode(t,"DoWhileStatement")},j.parseForStatement=function(t){this.next();var e=this.options.ecmaVersion>=9&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction)&&this.eatContextual("await")?this.lastTokStart:-1;if(this.labels.push(G),this.enterScope(0),this.expect(v.parenL),this.type===v.semi)return e>-1&&this.unexpected(e),this.parseFor(t,null);var i=this.isLet();if(this.type===v._var||this.type===v._const||i){var s=this.startNode(),r=i?"let":this.value;return this.next(),this.parseVar(s,!0,r),this.finishNode(s,"VariableDeclaration"),(this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of"))&&1===s.declarations.length?(this.options.ecmaVersion>=9&&(this.type===v._in?e>-1&&this.unexpected(e):t.await=e>-1),this.parseForIn(t,s)):(e>-1&&this.unexpected(e),this.parseFor(t,s))}var n=new H,a=this.parseExpression(!0,n);return this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.options.ecmaVersion>=9&&(this.type===v._in?e>-1&&this.unexpected(e):t.await=e>-1),this.toAssignable(a,!1,n),this.checkLVal(a),this.parseForIn(t,a)):(this.checkExpressionErrors(n,!0),e>-1&&this.unexpected(e),this.parseFor(t,a))},j.parseFunctionStatement=function(t,e,i){return this.next(),this.parseFunction(t,z|(i?0:Q),!1,e)},j.parseIfStatement=function(t){return this.next(),t.test=this.parseParenExpression(),t.consequent=this.parseStatement("if"),t.alternate=this.eat(v._else)?this.parseStatement("if"):null,this.finishNode(t,"IfStatement")},j.parseReturnStatement=function(t){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(v.semi)||this.insertSemicolon()?t.argument=null:(t.argument=this.parseExpression(),this.semicolon()),this.finishNode(t,"ReturnStatement")},j.parseSwitchStatement=function(t){var e;this.next(),t.discriminant=this.parseParenExpression(),t.cases=[],this.expect(v.braceL),this.labels.push(q),this.enterScope(0);for(var i=!1;this.type!==v.braceR;)if(this.type===v._case||this.type===v._default){var s=this.type===v._case;e&&this.finishNode(e,"SwitchCase"),t.cases.push(e=this.startNode()),e.consequent=[],this.next(),s?e.test=this.parseExpression():(i&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),i=!0,e.test=null),this.expect(v.colon)}else e||this.unexpected(),e.consequent.push(this.parseStatement(null));return this.exitScope(),e&&this.finishNode(e,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(t,"SwitchStatement")},j.parseThrowStatement=function(t){return this.next(),b.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),t.argument=this.parseExpression(),this.semicolon(),this.finishNode(t,"ThrowStatement")};var W=[];j.parseTryStatement=function(t){if(this.next(),t.block=this.parseBlock(),t.handler=null,this.type===v._catch){var e=this.startNode();if(this.next(),this.eat(v.parenL)){e.param=this.parseBindingAtom();var i="Identifier"===e.param.type;this.enterScope(i?32:0),this.checkLVal(e.param,i?4:2),this.expect(v.parenR)}else this.options.ecmaVersion<10&&this.unexpected(),e.param=null,this.enterScope(0);e.body=this.parseBlock(!1),this.exitScope(),t.handler=this.finishNode(e,"CatchClause")}return t.finalizer=this.eat(v._finally)?this.parseBlock():null,t.handler||t.finalizer||this.raise(t.start,"Missing catch or finally clause"),this.finishNode(t,"TryStatement")},j.parseVarStatement=function(t,e){return this.next(),this.parseVar(t,!1,e),this.semicolon(),this.finishNode(t,"VariableDeclaration")},j.parseWhileStatement=function(t){return this.next(),t.test=this.parseParenExpression(),this.labels.push(G),t.body=this.parseStatement("while"),this.labels.pop(),this.finishNode(t,"WhileStatement")},j.parseWithStatement=function(t){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),t.object=this.parseParenExpression(),t.body=this.parseStatement("with"),this.finishNode(t,"WithStatement")},j.parseEmptyStatement=function(t){return this.next(),this.finishNode(t,"EmptyStatement")},j.parseLabeledStatement=function(t,e,i,s){for(var r=0,n=this.labels;r<n.length;r+=1){n[r].name===e&&this.raise(i.start,"Label '"+e+"' is already declared")}for(var a=this.type.isLoop?"loop":this.type===v._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var h=this.labels[o];if(h.statementStart!==t.start)break;h.statementStart=this.start,h.kind=a}return this.labels.push({name:e,kind:a,statementStart:this.start}),t.body=this.parseStatement(s?-1===s.indexOf("label")?s+"label":s:"label"),this.labels.pop(),t.label=i,this.finishNode(t,"LabeledStatement")},j.parseExpressionStatement=function(t,e){return t.expression=e,this.semicolon(),this.finishNode(t,"ExpressionStatement")},j.parseBlock=function(t,e,i){for(void 0===t&&(t=!0),void 0===e&&(e=this.startNode()),e.body=[],this.expect(v.braceL),t&&this.enterScope(0);this.type!==v.braceR;){var s=this.parseStatement(null);e.body.push(s)}return i&&(this.strict=!1),this.next(),t&&this.exitScope(),this.finishNode(e,"BlockStatement")},j.parseFor=function(t,e){return t.init=e,this.expect(v.semi),t.test=this.type===v.semi?null:this.parseExpression(),this.expect(v.semi),t.update=this.type===v.parenR?null:this.parseExpression(),this.expect(v.parenR),t.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(t,"ForStatement")},j.parseForIn=function(t,e){var i=this.type===v._in;return this.next(),"VariableDeclaration"===e.type&&null!=e.declarations[0].init&&(!i||this.options.ecmaVersion<8||this.strict||"var"!==e.kind||"Identifier"!==e.declarations[0].id.type)?this.raise(e.start,(i?"for-in":"for-of")+" loop variable declaration may not have an initializer"):"AssignmentPattern"===e.type&&this.raise(e.start,"Invalid left-hand side in for-loop"),t.left=e,t.right=i?this.parseExpression():this.parseMaybeAssign(),this.expect(v.parenR),t.body=this.parseStatement("for"),this.exitScope(),this.labels.pop(),this.finishNode(t,i?"ForInStatement":"ForOfStatement")},j.parseVar=function(t,e,i){for(t.declarations=[],t.kind=i;;){var s=this.startNode();if(this.parseVarId(s,i),this.eat(v.eq)?s.init=this.parseMaybeAssign(e):"const"!==i||this.type===v._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"===s.id.type||e&&(this.type===v._in||this.isContextual("of"))?s.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),t.declarations.push(this.finishNode(s,"VariableDeclarator")),!this.eat(v.comma))break}return t},j.parseVarId=function(t,e){t.id=this.parseBindingAtom(),this.checkLVal(t.id,"var"===e?1:2,!1)};var z=1,Q=2;j.parseFunction=function(t,e,i,s){this.initFunction(t),(this.options.ecmaVersion>=9||this.options.ecmaVersion>=6&&!s)&&(this.type===v.star&&e&Q&&this.unexpected(),t.generator=this.eat(v.star)),this.options.ecmaVersion>=8&&(t.async=!!s),e&z&&(t.id=4&e&&this.type!==v.name?null:this.parseIdent(),!t.id||e&Q||this.checkLVal(t.id,this.strict||t.generator||t.async?this.treatFunctionsAsVar?1:2:3));var r=this.yieldPos,n=this.awaitPos,a=this.awaitIdentPos;return this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(D(t.async,t.generator)),e&z||(t.id=this.type===v.name?this.parseIdent():null),this.parseFunctionParams(t),this.parseFunctionBody(t,i,!1),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=a,this.finishNode(t,e&z?"FunctionDeclaration":"FunctionExpression")},j.parseFunctionParams=function(t){this.expect(v.parenL),t.params=this.parseBindingList(v.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},j.parseClass=function(t,e){this.next();var i=this.strict;this.strict=!0,this.parseClassId(t,e),this.parseClassSuper(t);var s=this.startNode(),r=!1;for(s.body=[],this.expect(v.braceL);this.type!==v.braceR;){var n=this.parseClassElement(null!==t.superClass);n&&(s.body.push(n),"MethodDefinition"===n.type&&"constructor"===n.kind&&(r&&this.raise(n.start,"Duplicate constructor in the same class"),r=!0))}return this.strict=i,this.next(),t.body=this.finishNode(s,"ClassBody"),this.finishNode(t,e?"ClassDeclaration":"ClassExpression")},j.parseClassElement=function(t){var e=this;if(this.eat(v.semi))return null;var i=this.startNode(),s=function(t,s){void 0===s&&(s=!1);var r=e.start,n=e.startLoc;return!!e.eatContextual(t)&&(!(e.type===v.parenL||s&&e.canInsertSemicolon())||(i.key&&e.unexpected(),i.computed=!1,i.key=e.startNodeAt(r,n),i.key.name=t,e.finishNode(i.key,"Identifier"),!1))};i.kind="method",i.static=s("static");var r=this.eat(v.star),n=!1;r||(this.options.ecmaVersion>=8&&s("async",!0)?(n=!0,r=this.options.ecmaVersion>=9&&this.eat(v.star)):s("get")?i.kind="get":s("set")&&(i.kind="set")),i.key||this.parsePropertyName(i);var a=i.key,o=!1;return i.computed||i.static||!("Identifier"===a.type&&"constructor"===a.name||"Literal"===a.type&&"constructor"===a.value)?i.static&&"Identifier"===a.type&&"prototype"===a.name&&this.raise(a.start,"Classes may not have a static property named prototype"):("method"!==i.kind&&this.raise(a.start,"Constructor can't have get/set modifier"),r&&this.raise(a.start,"Constructor can't be a generator"),n&&this.raise(a.start,"Constructor can't be an async method"),i.kind="constructor",o=t),this.parseClassMethod(i,r,n,o),"get"===i.kind&&0!==i.value.params.length&&this.raiseRecoverable(i.value.start,"getter should have no params"),"set"===i.kind&&1!==i.value.params.length&&this.raiseRecoverable(i.value.start,"setter should have exactly one param"),"set"===i.kind&&"RestElement"===i.value.params[0].type&&this.raiseRecoverable(i.value.params[0].start,"Setter cannot use rest params"),i},j.parseClassMethod=function(t,e,i,s){return t.value=this.parseMethod(e,i,s),this.finishNode(t,"MethodDefinition")},j.parseClassId=function(t,e){this.type===v.name?(t.id=this.parseIdent(),e&&this.checkLVal(t.id,2,!1)):(!0===e&&this.unexpected(),t.id=null)},j.parseClassSuper=function(t){t.superClass=this.eat(v._extends)?this.parseExprSubscripts():null},j.parseExport=function(t,e){if(this.next(),this.eat(v.star))return this.options.ecmaVersion>=11&&(this.eatContextual("as")?(t.exported=this.parseIdent(!0),this.checkExport(e,t.exported.name,this.lastTokStart)):t.exported=null),this.expectContextual("from"),this.type!==v.string&&this.unexpected(),t.source=this.parseExprAtom(),this.semicolon(),this.finishNode(t,"ExportAllDeclaration");if(this.eat(v._default)){var i;if(this.checkExport(e,"default",this.lastTokStart),this.type===v._function||(i=this.isAsyncFunction())){var s=this.startNode();this.next(),i&&this.next(),t.declaration=this.parseFunction(s,4|z,!1,i)}else if(this.type===v._class){var r=this.startNode();t.declaration=this.parseClass(r,"nullableID")}else t.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(t,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())t.declaration=this.parseStatement(null),"VariableDeclaration"===t.declaration.type?this.checkVariableExport(e,t.declaration.declarations):this.checkExport(e,t.declaration.id.name,t.declaration.id.start),t.specifiers=[],t.source=null;else{if(t.declaration=null,t.specifiers=this.parseExportSpecifiers(e),this.eatContextual("from"))this.type!==v.string&&this.unexpected(),t.source=this.parseExprAtom();else{for(var n=0,a=t.specifiers;n<a.length;n+=1){var o=a[n];this.checkUnreserved(o.local),this.checkLocalExport(o.local)}t.source=null}this.semicolon()}return this.finishNode(t,"ExportNamedDeclaration")},j.checkExport=function(t,e,i){t&&(A(t,e)&&this.raiseRecoverable(i,"Duplicate export '"+e+"'"),t[e]=!0)},j.checkPatternExport=function(t,e){var i=e.type;if("Identifier"===i)this.checkExport(t,e.name,e.start);else if("ObjectPattern"===i)for(var s=0,r=e.properties;s<r.length;s+=1){var n=r[s];this.checkPatternExport(t,n)}else if("ArrayPattern"===i)for(var a=0,o=e.elements;a<o.length;a+=1){var h=o[a];h&&this.checkPatternExport(t,h)}else"Property"===i?this.checkPatternExport(t,e.value):"AssignmentPattern"===i?this.checkPatternExport(t,e.left):"RestElement"===i?this.checkPatternExport(t,e.argument):"ParenthesizedExpression"===i&&this.checkPatternExport(t,e.expression)},j.checkVariableExport=function(t,e){if(t)for(var i=0,s=e;i<s.length;i+=1){var r=s[i];this.checkPatternExport(t,r.id)}},j.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},j.parseExportSpecifiers=function(t){var e=[],i=!0;for(this.expect(v.braceL);!this.eat(v.braceR);){if(i)i=!1;else if(this.expect(v.comma),this.afterTrailingComma(v.braceR))break;var s=this.startNode();s.local=this.parseIdent(!0),s.exported=this.eatContextual("as")?this.parseIdent(!0):s.local,this.checkExport(t,s.exported.name,s.exported.start),e.push(this.finishNode(s,"ExportSpecifier"))}return e},j.parseImport=function(t){return this.next(),this.type===v.string?(t.specifiers=W,t.source=this.parseExprAtom()):(t.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),t.source=this.type===v.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(t,"ImportDeclaration")},j.parseImportSpecifiers=function(){var t=[],e=!0;if(this.type===v.name){var i=this.startNode();if(i.local=this.parseIdent(),this.checkLVal(i.local,2),t.push(this.finishNode(i,"ImportDefaultSpecifier")),!this.eat(v.comma))return t}if(this.type===v.star){var s=this.startNode();return this.next(),this.expectContextual("as"),s.local=this.parseIdent(),this.checkLVal(s.local,2),t.push(this.finishNode(s,"ImportNamespaceSpecifier")),t}for(this.expect(v.braceL);!this.eat(v.braceR);){if(e)e=!1;else if(this.expect(v.comma),this.afterTrailingComma(v.braceR))break;var r=this.startNode();r.imported=this.parseIdent(!0),this.eatContextual("as")?r.local=this.parseIdent():(this.checkUnreserved(r.imported),r.local=r.imported),this.checkLVal(r.local,2),t.push(this.finishNode(r,"ImportSpecifier"))}return t},j.adaptDirectivePrologue=function(t){for(var e=0;e<t.length&&this.isDirectiveCandidate(t[e]);++e)t[e].directive=t[e].expression.raw.slice(1,-1)},j.isDirectiveCandidate=function(t){return"ExpressionStatement"===t.type&&"Literal"===t.expression.type&&"string"==typeof t.expression.value&&('"'===this.input[t.start]||"'"===this.input[t.start])};var K=F.prototype;K.toAssignable=function(t,e,i){if(this.options.ecmaVersion>=6&&t)switch(t.type){case"Identifier":this.inAsync&&"await"===t.name&&this.raise(t.start,"Cannot use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":case"RestElement":break;case"ObjectExpression":t.type="ObjectPattern",i&&this.checkPatternErrors(i,!0);for(var s=0,r=t.properties;s<r.length;s+=1){var n=r[s];this.toAssignable(n,e),"RestElement"!==n.type||"ArrayPattern"!==n.argument.type&&"ObjectPattern"!==n.argument.type||this.raise(n.argument.start,"Unexpected token")}break;case"Property":"init"!==t.kind&&this.raise(t.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(t.value,e);break;case"ArrayExpression":t.type="ArrayPattern",i&&this.checkPatternErrors(i,!0),this.toAssignableList(t.elements,e);break;case"SpreadElement":t.type="RestElement",this.toAssignable(t.argument,e),"AssignmentPattern"===t.argument.type&&this.raise(t.argument.start,"Rest elements cannot have a default value");break;case"AssignmentExpression":"="!==t.operator&&this.raise(t.left.end,"Only '=' operator can be used for specifying default value."),t.type="AssignmentPattern",delete t.operator,this.toAssignable(t.left,e);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(t.expression,e,i);break;case"ChainExpression":this.raiseRecoverable(t.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":if(!e)break;default:this.raise(t.start,"Assigning to rvalue")}else i&&this.checkPatternErrors(i,!0);return t},K.toAssignableList=function(t,e){for(var i=t.length,s=0;s<i;s++){var r=t[s];r&&this.toAssignable(r,e)}if(i){var n=t[i-1];6===this.options.ecmaVersion&&e&&n&&"RestElement"===n.type&&"Identifier"!==n.argument.type&&this.unexpected(n.argument.start)}return t},K.parseSpread=function(t){var e=this.startNode();return this.next(),e.argument=this.parseMaybeAssign(!1,t),this.finishNode(e,"SpreadElement")},K.parseRestBinding=function(){var t=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==v.name&&this.unexpected(),t.argument=this.parseBindingAtom(),this.finishNode(t,"RestElement")},K.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case v.bracketL:var t=this.startNode();return this.next(),t.elements=this.parseBindingList(v.bracketR,!0,!0),this.finishNode(t,"ArrayPattern");case v.braceL:return this.parseObj(!0)}return this.parseIdent()},K.parseBindingList=function(t,e,i){for(var s=[],r=!0;!this.eat(t);)if(r?r=!1:this.expect(v.comma),e&&this.type===v.comma)s.push(null);else{if(i&&this.afterTrailingComma(t))break;if(this.type===v.ellipsis){var n=this.parseRestBinding();this.parseBindingListItem(n),s.push(n),this.type===v.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(t);break}var a=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(a),s.push(a)}return s},K.parseBindingListItem=function(t){return t},K.parseMaybeDefault=function(t,e,i){if(i=i||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(v.eq))return i;var s=this.startNodeAt(t,e);return s.left=i,s.right=this.parseMaybeAssign(),this.finishNode(s,"AssignmentPattern")},K.checkLVal=function(t,e,i){switch(void 0===e&&(e=0),t.type){case"Identifier":2===e&&"let"===t.name&&this.raiseRecoverable(t.start,"let is disallowed as a lexically bound name"),this.strict&&this.reservedWordsStrictBind.test(t.name)&&this.raiseRecoverable(t.start,(e?"Binding ":"Assigning to ")+t.name+" in strict mode"),i&&(A(i,t.name)&&this.raiseRecoverable(t.start,"Argument name clash"),i[t.name]=!0),0!==e&&5!==e&&this.declareName(t.name,e,t.start);break;case"ChainExpression":this.raiseRecoverable(t.start,"Optional chaining cannot appear in left-hand side");break;case"MemberExpression":e&&this.raiseRecoverable(t.start,"Binding member expression");break;case"ObjectPattern":for(var s=0,r=t.properties;s<r.length;s+=1){var n=r[s];this.checkLVal(n,e,i)}break;case"Property":this.checkLVal(t.value,e,i);break;case"ArrayPattern":for(var a=0,o=t.elements;a<o.length;a+=1){var h=o[a];h&&this.checkLVal(h,e,i)}break;case"AssignmentPattern":this.checkLVal(t.left,e,i);break;case"RestElement":this.checkLVal(t.argument,e,i);break;case"ParenthesizedExpression":this.checkLVal(t.expression,e,i);break;default:this.raise(t.start,(e?"Binding":"Assigning to")+" rvalue")}};var $=F.prototype;$.checkPropClash=function(t,e,i){if(!(this.options.ecmaVersion>=9&&"SpreadElement"===t.type||this.options.ecmaVersion>=6&&(t.computed||t.method||t.shorthand))){var s,r=t.key;switch(r.type){case"Identifier":s=r.name;break;case"Literal":s=String(r.value);break;default:return}var n=t.kind;if(this.options.ecmaVersion>=6)"__proto__"===s&&"init"===n&&(e.proto&&(i?i.doubleProto<0&&(i.doubleProto=r.start):this.raiseRecoverable(r.start,"Redefinition of __proto__ property")),e.proto=!0);else{var a=e[s="$"+s];if(a)("init"===n?this.strict&&a.init||a.get||a.set:a.init||a[n])&&this.raiseRecoverable(r.start,"Redefinition of property");else a=e[s]={init:!1,get:!1,set:!1};a[n]=!0}}},$.parseExpression=function(t,e){var i=this.start,s=this.startLoc,r=this.parseMaybeAssign(t,e);if(this.type===v.comma){var n=this.startNodeAt(i,s);for(n.expressions=[r];this.eat(v.comma);)n.expressions.push(this.parseMaybeAssign(t,e));return this.finishNode(n,"SequenceExpression")}return r},$.parseMaybeAssign=function(t,e,i){if(this.isContextual("yield")){if(this.inGenerator)return this.parseYield(t);this.exprAllowed=!1}var s=!1,r=-1,n=-1;e?(r=e.parenthesizedAssign,n=e.trailingComma,e.parenthesizedAssign=e.trailingComma=-1):(e=new H,s=!0);var a=this.start,o=this.startLoc;this.type!==v.parenL&&this.type!==v.name||(this.potentialArrowAt=this.start);var h=this.parseMaybeConditional(t,e);if(i&&(h=i.call(this,h,a,o)),this.type.isAssign){var c=this.startNodeAt(a,o);return c.operator=this.value,c.left=this.type===v.eq?this.toAssignable(h,!1,e):h,s||(e.parenthesizedAssign=e.trailingComma=e.doubleProto=-1),e.shorthandAssign>=c.left.start&&(e.shorthandAssign=-1),this.checkLVal(h),this.next(),c.right=this.parseMaybeAssign(t),this.finishNode(c,"AssignmentExpression")}return s&&this.checkExpressionErrors(e,!0),r>-1&&(e.parenthesizedAssign=r),n>-1&&(e.trailingComma=n),h},$.parseMaybeConditional=function(t,e){var i=this.start,s=this.startLoc,r=this.parseExprOps(t,e);if(this.checkExpressionErrors(e))return r;if(this.eat(v.question)){var n=this.startNodeAt(i,s);return n.test=r,n.consequent=this.parseMaybeAssign(),this.expect(v.colon),n.alternate=this.parseMaybeAssign(t),this.finishNode(n,"ConditionalExpression")}return r},$.parseExprOps=function(t,e){var i=this.start,s=this.startLoc,r=this.parseMaybeUnary(e,!1);return this.checkExpressionErrors(e)||r.start===i&&"ArrowFunctionExpression"===r.type?r:this.parseExprOp(r,i,s,-1,t)},$.parseExprOp=function(t,e,i,s,r){var n=this.type.binop;if(null!=n&&(!r||this.type!==v._in)&&n>s){var a=this.type===v.logicalOR||this.type===v.logicalAND,o=this.type===v.coalesce;o&&(n=v.logicalAND.binop);var h=this.value;this.next();var c=this.start,p=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1),c,p,n,r),u=this.buildBinary(e,i,t,l,h,a||o);return(a&&this.type===v.coalesce||o&&(this.type===v.logicalOR||this.type===v.logicalAND))&&this.raiseRecoverable(this.start,"Logical expressions and coalesce expressions cannot be mixed. Wrap either by parentheses"),this.parseExprOp(u,e,i,s,r)}return t},$.buildBinary=function(t,e,i,s,r,n){var a=this.startNodeAt(t,e);return a.left=i,a.operator=r,a.right=s,this.finishNode(a,n?"LogicalExpression":"BinaryExpression")},$.parseMaybeUnary=function(t,e){var i,s=this.start,r=this.startLoc;if(this.isContextual("await")&&(this.inAsync||!this.inFunction&&this.options.allowAwaitOutsideFunction))i=this.parseAwait(),e=!0;else if(this.type.prefix){var n=this.startNode(),a=this.type===v.incDec;n.operator=this.value,n.prefix=!0,this.next(),n.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(t,!0),a?this.checkLVal(n.argument):this.strict&&"delete"===n.operator&&"Identifier"===n.argument.type?this.raiseRecoverable(n.start,"Deleting local variable in strict mode"):e=!0,i=this.finishNode(n,a?"UpdateExpression":"UnaryExpression")}else{if(i=this.parseExprSubscripts(t),this.checkExpressionErrors(t))return i;for(;this.type.postfix&&!this.canInsertSemicolon();){var o=this.startNodeAt(s,r);o.operator=this.value,o.prefix=!1,o.argument=i,this.checkLVal(i),this.next(),i=this.finishNode(o,"UpdateExpression")}}return!e&&this.eat(v.starstar)?this.buildBinary(s,r,i,this.parseMaybeUnary(null,!1),"**",!1):i},$.parseExprSubscripts=function(t){var e=this.start,i=this.startLoc,s=this.parseExprAtom(t);if("ArrowFunctionExpression"===s.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd))return s;var r=this.parseSubscripts(s,e,i);return t&&"MemberExpression"===r.type&&(t.parenthesizedAssign>=r.start&&(t.parenthesizedAssign=-1),t.parenthesizedBind>=r.start&&(t.parenthesizedBind=-1)),r},$.parseSubscripts=function(t,e,i,s){for(var r=this.options.ecmaVersion>=8&&"Identifier"===t.type&&"async"===t.name&&this.lastTokEnd===t.end&&!this.canInsertSemicolon()&&t.end-t.start==5&&this.potentialArrowAt===t.start,n=!1;;){var a=this.parseSubscript(t,e,i,s,r,n);if(a.optional&&(n=!0),a===t||"ArrowFunctionExpression"===a.type){if(n){var o=this.startNodeAt(e,i);o.expression=a,a=this.finishNode(o,"ChainExpression")}return a}t=a}},$.parseSubscript=function(t,e,i,s,r,n){var a=this.options.ecmaVersion>=11,o=a&&this.eat(v.questionDot);s&&o&&this.raise(this.lastTokStart,"Optional chaining cannot appear in the callee of new expressions");var h=this.eat(v.bracketL);if(h||o&&this.type!==v.parenL&&this.type!==v.backQuote||this.eat(v.dot)){var c=this.startNodeAt(e,i);c.object=t,c.property=h?this.parseExpression():this.parseIdent("never"!==this.options.allowReserved),c.computed=!!h,h&&this.expect(v.bracketR),a&&(c.optional=o),t=this.finishNode(c,"MemberExpression")}else if(!s&&this.eat(v.parenL)){var p=new H,l=this.yieldPos,u=this.awaitPos,d=this.awaitIdentPos;this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0;var f=this.parseExprList(v.parenR,this.options.ecmaVersion>=8,!1,p);if(r&&!o&&!this.canInsertSemicolon()&&this.eat(v.arrow))return this.checkPatternErrors(p,!1),this.checkYieldAwaitInDefaultParams(),this.awaitIdentPos>0&&this.raise(this.awaitIdentPos,"Cannot use 'await' as identifier inside an async function"),this.yieldPos=l,this.awaitPos=u,this.awaitIdentPos=d,this.parseArrowExpression(this.startNodeAt(e,i),f,!0);this.checkExpressionErrors(p,!0),this.yieldPos=l||this.yieldPos,this.awaitPos=u||this.awaitPos,this.awaitIdentPos=d||this.awaitIdentPos;var m=this.startNodeAt(e,i);m.callee=t,m.arguments=f,a&&(m.optional=o),t=this.finishNode(m,"CallExpression")}else if(this.type===v.backQuote){(o||n)&&this.raise(this.start,"Optional chaining cannot appear in the tag of tagged template expressions");var x=this.startNodeAt(e,i);x.tag=t,x.quasi=this.parseTemplate({isTagged:!0}),t=this.finishNode(x,"TaggedTemplateExpression")}return t},$.parseExprAtom=function(t){this.type===v.slash&&this.readRegexp();var e,i=this.potentialArrowAt===this.start;switch(this.type){case v._super:return this.allowSuper||this.raise(this.start,"'super' keyword outside a method"),e=this.startNode(),this.next(),this.type!==v.parenL||this.allowDirectSuper||this.raise(e.start,"super() call outside constructor of a subclass"),this.type!==v.dot&&this.type!==v.bracketL&&this.type!==v.parenL&&this.unexpected(),this.finishNode(e,"Super");case v._this:return e=this.startNode(),this.next(),this.finishNode(e,"ThisExpression");case v.name:var s=this.start,r=this.startLoc,n=this.containsEsc,a=this.parseIdent(!1);if(this.options.ecmaVersion>=8&&!n&&"async"===a.name&&!this.canInsertSemicolon()&&this.eat(v._function))return this.parseFunction(this.startNodeAt(s,r),0,!1,!0);if(i&&!this.canInsertSemicolon()){if(this.eat(v.arrow))return this.parseArrowExpression(this.startNodeAt(s,r),[a],!1);if(this.options.ecmaVersion>=8&&"async"===a.name&&this.type===v.name&&!n)return a=this.parseIdent(!1),!this.canInsertSemicolon()&&this.eat(v.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(s,r),[a],!0)}return a;case v.regexp:var o=this.value;return(e=this.parseLiteral(o.value)).regex={pattern:o.pattern,flags:o.flags},e;case v.num:case v.string:return this.parseLiteral(this.value);case v._null:case v._true:case v._false:return(e=this.startNode()).value=this.type===v._null?null:this.type===v._true,e.raw=this.type.keyword,this.next(),this.finishNode(e,"Literal");case v.parenL:var h=this.start,c=this.parseParenAndDistinguishExpression(i);return t&&(t.parenthesizedAssign<0&&!this.isSimpleAssignTarget(c)&&(t.parenthesizedAssign=h),t.parenthesizedBind<0&&(t.parenthesizedBind=h)),c;case v.bracketL:return e=this.startNode(),this.next(),e.elements=this.parseExprList(v.bracketR,!0,!0,t),this.finishNode(e,"ArrayExpression");case v.braceL:return this.parseObj(!1,t);case v._function:return e=this.startNode(),this.next(),this.parseFunction(e,0);case v._class:return this.parseClass(this.startNode(),!1);case v._new:return this.parseNew();case v.backQuote:return this.parseTemplate();case v._import:return this.options.ecmaVersion>=11?this.parseExprImport():this.unexpected();default:this.unexpected()}},$.parseExprImport=function(){var t=this.startNode();this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword import");var e=this.parseIdent(!0);switch(this.type){case v.parenL:return this.parseDynamicImport(t);case v.dot:return t.meta=e,this.parseImportMeta(t);default:this.unexpected()}},$.parseDynamicImport=function(t){if(this.next(),t.source=this.parseMaybeAssign(),!this.eat(v.parenR)){var e=this.start;this.eat(v.comma)&&this.eat(v.parenR)?this.raiseRecoverable(e,"Trailing comma is not allowed in import()"):this.unexpected(e)}return this.finishNode(t,"ImportExpression")},$.parseImportMeta=function(t){this.next();var e=this.containsEsc;return t.property=this.parseIdent(!0),"meta"!==t.property.name&&this.raiseRecoverable(t.property.start,"The only valid meta property for import is 'import.meta'"),e&&this.raiseRecoverable(t.start,"'import.meta' must not contain escaped characters"),"module"!==this.options.sourceType&&this.raiseRecoverable(t.start,"Cannot use 'import.meta' outside a module"),this.finishNode(t,"MetaProperty")},$.parseLiteral=function(t){var e=this.startNode();return e.value=t,e.raw=this.input.slice(this.start,this.end),110===e.raw.charCodeAt(e.raw.length-1)&&(e.bigint=e.raw.slice(0,-1).replace(/_/g,"")),this.next(),this.finishNode(e,"Literal")},$.parseParenExpression=function(){this.expect(v.parenL);var t=this.parseExpression();return this.expect(v.parenR),t},$.parseParenAndDistinguishExpression=function(t){var e,i=this.start,s=this.startLoc,r=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var n,a=this.start,o=this.startLoc,h=[],c=!0,p=!1,l=new H,u=this.yieldPos,d=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==v.parenR;){if(c?c=!1:this.expect(v.comma),r&&this.afterTrailingComma(v.parenR,!0)){p=!0;break}if(this.type===v.ellipsis){n=this.start,h.push(this.parseParenItem(this.parseRestBinding())),this.type===v.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}h.push(this.parseMaybeAssign(!1,l,this.parseParenItem))}var f=this.start,m=this.startLoc;if(this.expect(v.parenR),t&&!this.canInsertSemicolon()&&this.eat(v.arrow))return this.checkPatternErrors(l,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=u,this.awaitPos=d,this.parseParenArrowList(i,s,h);h.length&&!p||this.unexpected(this.lastTokStart),n&&this.unexpected(n),this.checkExpressionErrors(l,!0),this.yieldPos=u||this.yieldPos,this.awaitPos=d||this.awaitPos,h.length>1?((e=this.startNodeAt(a,o)).expressions=h,this.finishNodeAt(e,"SequenceExpression",f,m)):e=h[0]}else e=this.parseParenExpression();if(this.options.preserveParens){var x=this.startNodeAt(i,s);return x.expression=e,this.finishNode(x,"ParenthesizedExpression")}return e},$.parseParenItem=function(t){return t},$.parseParenArrowList=function(t,e,i){return this.parseArrowExpression(this.startNodeAt(t,e),i)};var X=[];$.parseNew=function(){this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword new");var t=this.startNode(),e=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(v.dot)){t.meta=e;var i=this.containsEsc;return t.property=this.parseIdent(!0),"target"!==t.property.name&&this.raiseRecoverable(t.property.start,"The only valid meta property for new is 'new.target'"),i&&this.raiseRecoverable(t.start,"'new.target' must not contain escaped characters"),this.inNonArrowFunction()||this.raiseRecoverable(t.start,"'new.target' can only be used in functions"),this.finishNode(t,"MetaProperty")}var s=this.start,r=this.startLoc,n=this.type===v._import;return t.callee=this.parseSubscripts(this.parseExprAtom(),s,r,!0),n&&"ImportExpression"===t.callee.type&&this.raise(s,"Cannot use new with import()"),this.eat(v.parenL)?t.arguments=this.parseExprList(v.parenR,this.options.ecmaVersion>=8,!1):t.arguments=X,this.finishNode(t,"NewExpression")},$.parseTemplateElement=function(t){var e=t.isTagged,i=this.startNode();return this.type===v.invalidTemplate?(e||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),i.value={raw:this.value,cooked:null}):i.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),i.tail=this.type===v.backQuote,this.finishNode(i,"TemplateElement")},$.parseTemplate=function(t){void 0===t&&(t={});var e=t.isTagged;void 0===e&&(e=!1);var i=this.startNode();this.next(),i.expressions=[];var s=this.parseTemplateElement({isTagged:e});for(i.quasis=[s];!s.tail;)this.type===v.eof&&this.raise(this.pos,"Unterminated template literal"),this.expect(v.dollarBraceL),i.expressions.push(this.parseExpression()),this.expect(v.braceR),i.quasis.push(s=this.parseTemplateElement({isTagged:e}));return this.next(),this.finishNode(i,"TemplateLiteral")},$.isAsyncProp=function(t){return!t.computed&&"Identifier"===t.key.type&&"async"===t.key.name&&(this.type===v.name||this.type===v.num||this.type===v.string||this.type===v.bracketL||this.type.keyword||this.options.ecmaVersion>=9&&this.type===v.star)&&!b.test(this.input.slice(this.lastTokEnd,this.start))},$.parseObj=function(t,e){var i=this.startNode(),s=!0,r={};for(i.properties=[],this.next();!this.eat(v.braceR);){if(s)s=!1;else if(this.expect(v.comma),this.options.ecmaVersion>=5&&this.afterTrailingComma(v.braceR))break;var n=this.parseProperty(t,e);t||this.checkPropClash(n,r,e),i.properties.push(n)}return this.finishNode(i,t?"ObjectPattern":"ObjectExpression")},$.parseProperty=function(t,e){var i,s,r,n,a=this.startNode();if(this.options.ecmaVersion>=9&&this.eat(v.ellipsis))return t?(a.argument=this.parseIdent(!1),this.type===v.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.finishNode(a,"RestElement")):(this.type===v.parenL&&e&&(e.parenthesizedAssign<0&&(e.parenthesizedAssign=this.start),e.parenthesizedBind<0&&(e.parenthesizedBind=this.start)),a.argument=this.parseMaybeAssign(!1,e),this.type===v.comma&&e&&e.trailingComma<0&&(e.trailingComma=this.start),this.finishNode(a,"SpreadElement"));this.options.ecmaVersion>=6&&(a.method=!1,a.shorthand=!1,(t||e)&&(r=this.start,n=this.startLoc),t||(i=this.eat(v.star)));var o=this.containsEsc;return this.parsePropertyName(a),!t&&!o&&this.options.ecmaVersion>=8&&!i&&this.isAsyncProp(a)?(s=!0,i=this.options.ecmaVersion>=9&&this.eat(v.star),this.parsePropertyName(a,e)):s=!1,this.parsePropertyValue(a,t,i,s,r,n,e,o),this.finishNode(a,"Property")},$.parsePropertyValue=function(t,e,i,s,r,n,a,o){if((i||s)&&this.type===v.colon&&this.unexpected(),this.eat(v.colon))t.value=e?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,a),t.kind="init";else if(this.options.ecmaVersion>=6&&this.type===v.parenL)e&&this.unexpected(),t.kind="init",t.method=!0,t.value=this.parseMethod(i,s);else if(e||o||!(this.options.ecmaVersion>=5)||t.computed||"Identifier"!==t.key.type||"get"!==t.key.name&&"set"!==t.key.name||this.type===v.comma||this.type===v.braceR||this.type===v.eq)this.options.ecmaVersion>=6&&!t.computed&&"Identifier"===t.key.type?((i||s)&&this.unexpected(),this.checkUnreserved(t.key),"await"!==t.key.name||this.awaitIdentPos||(this.awaitIdentPos=r),t.kind="init",e?t.value=this.parseMaybeDefault(r,n,t.key):this.type===v.eq&&a?(a.shorthandAssign<0&&(a.shorthandAssign=this.start),t.value=this.parseMaybeDefault(r,n,t.key)):t.value=t.key,t.shorthand=!0):this.unexpected();else{(i||s)&&this.unexpected(),t.kind=t.key.name,this.parsePropertyName(t),t.value=this.parseMethod(!1);var h="get"===t.kind?0:1;if(t.value.params.length!==h){var c=t.value.start;"get"===t.kind?this.raiseRecoverable(c,"getter should have no params"):this.raiseRecoverable(c,"setter should have exactly one param")}else"set"===t.kind&&"RestElement"===t.value.params[0].type&&this.raiseRecoverable(t.value.params[0].start,"Setter cannot use rest params")}},$.parsePropertyName=function(t){if(this.options.ecmaVersion>=6){if(this.eat(v.bracketL))return t.computed=!0,t.key=this.parseMaybeAssign(),this.expect(v.bracketR),t.key;t.computed=!1}return t.key=this.type===v.num||this.type===v.string?this.parseExprAtom():this.parseIdent("never"!==this.options.allowReserved)},$.initFunction=function(t){t.id=null,this.options.ecmaVersion>=6&&(t.generator=t.expression=!1),this.options.ecmaVersion>=8&&(t.async=!1)},$.parseMethod=function(t,e,i){var s=this.startNode(),r=this.yieldPos,n=this.awaitPos,a=this.awaitIdentPos;return this.initFunction(s),this.options.ecmaVersion>=6&&(s.generator=t),this.options.ecmaVersion>=8&&(s.async=!!e),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,this.enterScope(64|D(e,s.generator)|(i?128:0)),this.expect(v.parenL),s.params=this.parseBindingList(v.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(s,!1,!0),this.yieldPos=r,this.awaitPos=n,this.awaitIdentPos=a,this.finishNode(s,"FunctionExpression")},$.parseArrowExpression=function(t,e,i){var s=this.yieldPos,r=this.awaitPos,n=this.awaitIdentPos;return this.enterScope(16|D(i,!1)),this.initFunction(t),this.options.ecmaVersion>=8&&(t.async=!!i),this.yieldPos=0,this.awaitPos=0,this.awaitIdentPos=0,t.params=this.toAssignableList(e,!0),this.parseFunctionBody(t,!0,!1),this.yieldPos=s,this.awaitPos=r,this.awaitIdentPos=n,this.finishNode(t,"ArrowFunctionExpression")},$.parseFunctionBody=function(t,e,i){var s=e&&this.type!==v.braceL,r=this.strict,n=!1;if(s)t.body=this.parseMaybeAssign(),t.expression=!0,this.checkParams(t,!1);else{var a=this.options.ecmaVersion>=7&&!this.isSimpleParamList(t.params);r&&!a||(n=this.strictDirective(this.end))&&a&&this.raiseRecoverable(t.start,"Illegal 'use strict' directive in function with non-simple parameter list");var o=this.labels;this.labels=[],n&&(this.strict=!0),this.checkParams(t,!r&&!n&&!e&&!i&&this.isSimpleParamList(t.params)),this.strict&&t.id&&this.checkLVal(t.id,5),t.body=this.parseBlock(!1,void 0,n&&!r),t.expression=!1,this.adaptDirectivePrologue(t.body.body),this.labels=o}this.exitScope()},$.isSimpleParamList=function(t){for(var e=0,i=t;e<i.length;e+=1){if("Identifier"!==i[e].type)return!1}return!0},$.checkParams=function(t,e){for(var i={},s=0,r=t.params;s<r.length;s+=1){var n=r[s];this.checkLVal(n,1,e?null:i)}},$.parseExprList=function(t,e,i,s){for(var r=[],n=!0;!this.eat(t);){if(n)n=!1;else if(this.expect(v.comma),e&&this.afterTrailingComma(t))break;var a=void 0;i&&this.type===v.comma?a=null:this.type===v.ellipsis?(a=this.parseSpread(s),s&&this.type===v.comma&&s.trailingComma<0&&(s.trailingComma=this.start)):a=this.parseMaybeAssign(!1,s),r.push(a)}return r},$.checkUnreserved=function(t){var e=t.start,i=t.end,s=t.name;(this.inGenerator&&"yield"===s&&this.raiseRecoverable(e,"Cannot use 'yield' as identifier inside a generator"),this.inAsync&&"await"===s&&this.raiseRecoverable(e,"Cannot use 'await' as identifier inside an async function"),this.keywords.test(s)&&this.raise(e,"Unexpected keyword '"+s+"'"),this.options.ecmaVersion<6&&-1!==this.input.slice(e,i).indexOf("\\"))||(this.strict?this.reservedWordsStrict:this.reservedWords).test(s)&&(this.inAsync||"await"!==s||this.raiseRecoverable(e,"Cannot use keyword 'await' outside an async function"),this.raiseRecoverable(e,"The keyword '"+s+"' is reserved"))},$.parseIdent=function(t,e){var i=this.startNode();return this.type===v.name?i.name=this.value:this.type.keyword?(i.name=this.type.keyword,"class"!==i.name&&"function"!==i.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(!!t),this.finishNode(i,"Identifier"),t||(this.checkUnreserved(i),"await"!==i.name||this.awaitIdentPos||(this.awaitIdentPos=i.start)),i},$.parseYield=function(t){this.yieldPos||(this.yieldPos=this.start);var e=this.startNode();return this.next(),this.type===v.semi||this.canInsertSemicolon()||this.type!==v.star&&!this.type.startsExpr?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(v.star),e.argument=this.parseMaybeAssign(t)),this.finishNode(e,"YieldExpression")},$.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var t=this.startNode();return this.next(),t.argument=this.parseMaybeUnary(null,!1),this.finishNode(t,"AwaitExpression")};var Y=F.prototype;Y.raise=function(t,e){var i=L(this.input,t);e+=" ("+i.line+":"+i.column+")";var s=new SyntaxError(e);throw s.pos=t,s.loc=i,s.raisedAt=this.pos,s},Y.raiseRecoverable=Y.raise,Y.curPosition=function(){if(this.options.locations)return new N(this.curLine,this.pos-this.lineStart)};var Z=F.prototype,J=function(t){this.flags=t,this.var=[],this.lexical=[],this.functions=[]};Z.enterScope=function(t){this.scopeStack.push(new J(t))},Z.exitScope=function(){this.scopeStack.pop()},Z.treatFunctionsAsVarInScope=function(t){return 2&t.flags||!this.inModule&&1&t.flags},Z.declareName=function(t,e,i){var s=!1;if(2===e){var r=this.currentScope();s=r.lexical.indexOf(t)>-1||r.functions.indexOf(t)>-1||r.var.indexOf(t)>-1,r.lexical.push(t),this.inModule&&1&r.flags&&delete this.undefinedExports[t]}else if(4===e){this.currentScope().lexical.push(t)}else if(3===e){var n=this.currentScope();s=this.treatFunctionsAsVar?n.lexical.indexOf(t)>-1:n.lexical.indexOf(t)>-1||n.var.indexOf(t)>-1,n.functions.push(t)}else for(var a=this.scopeStack.length-1;a>=0;--a){var o=this.scopeStack[a];if(o.lexical.indexOf(t)>-1&&!(32&o.flags&&o.lexical[0]===t)||!this.treatFunctionsAsVarInScope(o)&&o.functions.indexOf(t)>-1){s=!0;break}if(o.var.push(t),this.inModule&&1&o.flags&&delete this.undefinedExports[t],3&o.flags)break}s&&this.raiseRecoverable(i,"Identifier '"+t+"' has already been declared")},Z.checkLocalExport=function(t){-1===this.scopeStack[0].lexical.indexOf(t.name)&&-1===this.scopeStack[0].var.indexOf(t.name)&&(this.undefinedExports[t.name]=t)},Z.currentScope=function(){return this.scopeStack[this.scopeStack.length-1]},Z.currentVarScope=function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if(3&e.flags)return e}},Z.currentThisScope=function(){for(var t=this.scopeStack.length-1;;t--){var e=this.scopeStack[t];if(3&e.flags&&!(16&e.flags))return e}};var tt=function(t,e,i){this.type="",this.start=e,this.end=0,t.options.locations&&(this.loc=new V(t,i)),t.options.directSourceFile&&(this.sourceFile=t.options.directSourceFile),t.options.ranges&&(this.range=[e,0])},et=F.prototype;function it(t,e,i,s){return t.type=e,t.end=i,this.options.locations&&(t.loc.end=s),this.options.ranges&&(t.range[1]=i),t}et.startNode=function(){return new tt(this,this.start,this.startLoc)},et.startNodeAt=function(t,e){return new tt(this,t,e)},et.finishNode=function(t,e){return it.call(this,t,e,this.lastTokEnd,this.lastTokEndLoc)},et.finishNodeAt=function(t,e,i,s){return it.call(this,t,e,i,s)};var st=function(t,e,i,s,r){this.token=t,this.isExpr=!!e,this.preserveSpace=!!i,this.override=s,this.generator=!!r},rt={b_stat:new st("{",!1),b_expr:new st("{",!0),b_tmpl:new st("${",!1),p_stat:new st("(",!1),p_expr:new st("(",!0),q_tmpl:new st("`",!0,!0,(function(t){return t.tryReadTemplateToken()})),f_stat:new st("function",!1),f_expr:new st("function",!0),f_expr_gen:new st("function",!0,!1,null,!0),f_gen:new st("function",!1,!1,null,!0)},nt=F.prototype;nt.initialContext=function(){return[rt.b_stat]},nt.braceIsBlock=function(t){var e=this.curContext();return e===rt.f_expr||e===rt.f_stat||(t!==v.colon||e!==rt.b_stat&&e!==rt.b_expr?t===v._return||t===v.name&&this.exprAllowed?b.test(this.input.slice(this.lastTokEnd,this.start)):t===v._else||t===v.semi||t===v.eof||t===v.parenR||t===v.arrow||(t===v.braceL?e===rt.b_stat:t!==v._var&&t!==v._const&&t!==v.name&&!this.exprAllowed):!e.isExpr)},nt.inGeneratorContext=function(){for(var t=this.context.length-1;t>=1;t--){var e=this.context[t];if("function"===e.token)return e.generator}return!1},nt.updateContext=function(t){var e,i=this.type;i.keyword&&t===v.dot?this.exprAllowed=!1:(e=i.updateContext)?e.call(this,t):this.exprAllowed=i.beforeExpr},v.parenR.updateContext=v.braceR.updateContext=function(){if(1!==this.context.length){var t=this.context.pop();t===rt.b_stat&&"function"===this.curContext().token&&(t=this.context.pop()),this.exprAllowed=!t.isExpr}else this.exprAllowed=!0},v.braceL.updateContext=function(t){this.context.push(this.braceIsBlock(t)?rt.b_stat:rt.b_expr),this.exprAllowed=!0},v.dollarBraceL.updateContext=function(){this.context.push(rt.b_tmpl),this.exprAllowed=!0},v.parenL.updateContext=function(t){var e=t===v._if||t===v._for||t===v._with||t===v._while;this.context.push(e?rt.p_stat:rt.p_expr),this.exprAllowed=!0},v.incDec.updateContext=function(){},v._function.updateContext=v._class.updateContext=function(t){!t.beforeExpr||t===v.semi||t===v._else||t===v._return&&b.test(this.input.slice(this.lastTokEnd,this.start))||(t===v.colon||t===v.braceL)&&this.curContext()===rt.b_stat?this.context.push(rt.f_stat):this.context.push(rt.f_expr),this.exprAllowed=!1},v.backQuote.updateContext=function(){this.curContext()===rt.q_tmpl?this.context.pop():this.context.push(rt.q_tmpl),this.exprAllowed=!1},v.star.updateContext=function(t){if(t===v._function){var e=this.context.length-1;this.context[e]===rt.f_expr?this.context[e]=rt.f_expr_gen:this.context[e]=rt.f_gen}this.exprAllowed=!0},v.name.updateContext=function(t){var e=!1;this.options.ecmaVersion>=6&&t!==v.dot&&("of"===this.value&&!this.exprAllowed||"yield"===this.value&&this.inGeneratorContext())&&(e=!0),this.exprAllowed=e};var at="ASCII ASCII_Hex_Digit AHex Alphabetic Alpha Any Assigned Bidi_Control Bidi_C Bidi_Mirrored Bidi_M Case_Ignorable CI Cased Changes_When_Casefolded CWCF Changes_When_Casemapped CWCM Changes_When_Lowercased CWL Changes_When_NFKC_Casefolded CWKCF Changes_When_Titlecased CWT Changes_When_Uppercased CWU Dash Default_Ignorable_Code_Point DI Deprecated Dep Diacritic Dia Emoji Emoji_Component Emoji_Modifier Emoji_Modifier_Base Emoji_Presentation Extender Ext Grapheme_Base Gr_Base Grapheme_Extend Gr_Ext Hex_Digit Hex IDS_Binary_Operator IDSB IDS_Trinary_Operator IDST ID_Continue IDC ID_Start IDS Ideographic Ideo Join_Control Join_C Logical_Order_Exception LOE Lowercase Lower Math Noncharacter_Code_Point NChar Pattern_Syntax Pat_Syn Pattern_White_Space Pat_WS Quotation_Mark QMark Radical Regional_Indicator RI Sentence_Terminal STerm Soft_Dotted SD Terminal_Punctuation Term Unified_Ideograph UIdeo Uppercase Upper Variation_Selector VS White_Space space XID_Continue XIDC XID_Start XIDS",ot=at+" Extended_Pictographic",ht={9:at,10:ot,11:ot},ct="Cased_Letter LC Close_Punctuation Pe Connector_Punctuation Pc Control Cc cntrl Currency_Symbol Sc Dash_Punctuation Pd Decimal_Number Nd digit Enclosing_Mark Me Final_Punctuation Pf Format Cf Initial_Punctuation Pi Letter L Letter_Number Nl Line_Separator Zl Lowercase_Letter Ll Mark M Combining_Mark Math_Symbol Sm Modifier_Letter Lm Modifier_Symbol Sk Nonspacing_Mark Mn Number N Open_Punctuation Ps Other C Other_Letter Lo Other_Number No Other_Punctuation Po Other_Symbol So Paragraph_Separator Zp Private_Use Co Punctuation P punct Separator Z Space_Separator Zs Spacing_Mark Mc Surrogate Cs Symbol S Titlecase_Letter Lt Unassigned Cn Uppercase_Letter Lu",pt="Adlam Adlm Ahom Ahom Anatolian_Hieroglyphs Hluw Arabic Arab Armenian Armn Avestan Avst Balinese Bali Bamum Bamu Bassa_Vah Bass Batak Batk Bengali Beng Bhaiksuki Bhks Bopomofo Bopo Brahmi Brah Braille Brai Buginese Bugi Buhid Buhd Canadian_Aboriginal Cans Carian Cari Caucasian_Albanian Aghb Chakma Cakm Cham Cham Cherokee Cher Common Zyyy Coptic Copt Qaac Cuneiform Xsux Cypriot Cprt Cyrillic Cyrl Deseret Dsrt Devanagari Deva Duployan Dupl Egyptian_Hieroglyphs Egyp Elbasan Elba Ethiopic Ethi Georgian Geor Glagolitic Glag Gothic Goth Grantha Gran Greek Grek Gujarati Gujr Gurmukhi Guru Han Hani Hangul Hang Hanunoo Hano Hatran Hatr Hebrew Hebr Hiragana Hira Imperial_Aramaic Armi Inherited Zinh Qaai Inscriptional_Pahlavi Phli Inscriptional_Parthian Prti Javanese Java Kaithi Kthi Kannada Knda Katakana Kana Kayah_Li Kali Kharoshthi Khar Khmer Khmr Khojki Khoj Khudawadi Sind Lao Laoo Latin Latn Lepcha Lepc Limbu Limb Linear_A Lina Linear_B Linb Lisu Lisu Lycian Lyci Lydian Lydi Mahajani Mahj Malayalam Mlym Mandaic Mand Manichaean Mani Marchen Marc Masaram_Gondi Gonm Meetei_Mayek Mtei Mende_Kikakui Mend Meroitic_Cursive Merc Meroitic_Hieroglyphs Mero Miao Plrd Modi Modi Mongolian Mong Mro Mroo Multani Mult Myanmar Mymr Nabataean Nbat New_Tai_Lue Talu Newa Newa Nko Nkoo Nushu Nshu Ogham Ogam Ol_Chiki Olck Old_Hungarian Hung Old_Italic Ital Old_North_Arabian Narb Old_Permic Perm Old_Persian Xpeo Old_South_Arabian Sarb Old_Turkic Orkh Oriya Orya Osage Osge Osmanya Osma Pahawh_Hmong Hmng Palmyrene Palm Pau_Cin_Hau Pauc Phags_Pa Phag Phoenician Phnx Psalter_Pahlavi Phlp Rejang Rjng Runic Runr Samaritan Samr Saurashtra Saur Sharada Shrd Shavian Shaw Siddham Sidd SignWriting Sgnw Sinhala Sinh Sora_Sompeng Sora Soyombo Soyo Sundanese Sund Syloti_Nagri Sylo Syriac Syrc Tagalog Tglg Tagbanwa Tagb Tai_Le Tale Tai_Tham Lana Tai_Viet Tavt Takri Takr Tamil Taml Tangut Tang Telugu Telu Thaana Thaa Thai Thai Tibetan Tibt Tifinagh Tfng Tirhuta Tirh Ugaritic Ugar Vai Vaii Warang_Citi Wara Yi Yiii Zanabazar_Square Zanb",lt=pt+" Dogra Dogr Gunjala_Gondi Gong Hanifi_Rohingya Rohg Makasar Maka Medefaidrin Medf Old_Sogdian Sogo Sogdian Sogd",ut={9:pt,10:lt,11:lt+" Elymaic Elym Nandinagari Nand Nyiakeng_Puachue_Hmong Hmnp Wancho Wcho"},dt={};function ft(t){var e=dt[t]={binary:T(ht[t]+" "+ct),nonBinary:{General_Category:T(ct),Script:T(ut[t])}};e.nonBinary.Script_Extensions=e.nonBinary.Script,e.nonBinary.gc=e.nonBinary.General_Category,e.nonBinary.sc=e.nonBinary.Script,e.nonBinary.scx=e.nonBinary.Script_Extensions}ft(9),ft(10),ft(11);var mt=F.prototype,xt=function(t){this.parser=t,this.validFlags="gim"+(t.options.ecmaVersion>=6?"uy":"")+(t.options.ecmaVersion>=9?"s":""),this.unicodeProperties=dt[t.options.ecmaVersion>=11?11:t.options.ecmaVersion],this.source="",this.flags="",this.start=0,this.switchU=!1,this.switchN=!1,this.pos=0,this.lastIntValue=0,this.lastStringValue="",this.lastAssertionIsQuantifiable=!1,this.numCapturingParens=0,this.maxBackReference=0,this.groupNames=[],this.backReferenceNames=[]};function gt(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}function yt(t){return 36===t||t>=40&&t<=43||46===t||63===t||t>=91&&t<=94||t>=123&&t<=125}function vt(t){return t>=65&&t<=90||t>=97&&t<=122}function bt(t){return vt(t)||95===t}function St(t){return bt(t)||kt(t)}function kt(t){return t>=48&&t<=57}function Ct(t){return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102}function Et(t){return t>=65&&t<=70?t-65+10:t>=97&&t<=102?t-97+10:t-48}function _t(t){return t>=48&&t<=55}xt.prototype.reset=function(t,e,i){var s=-1!==i.indexOf("u");this.start=0|t,this.source=e+"",this.flags=i,this.switchU=s&&this.parser.options.ecmaVersion>=6,this.switchN=s&&this.parser.options.ecmaVersion>=9},xt.prototype.raise=function(t){this.parser.raiseRecoverable(this.start,"Invalid regular expression: /"+this.source+"/: "+t)},xt.prototype.at=function(t,e){void 0===e&&(e=!1);var i=this.source,s=i.length;if(t>=s)return-1;var r=i.charCodeAt(t);if(!e&&!this.switchU||r<=55295||r>=57344||t+1>=s)return r;var n=i.charCodeAt(t+1);return n>=56320&&n<=57343?(r<<10)+n-56613888:r},xt.prototype.nextIndex=function(t,e){void 0===e&&(e=!1);var i=this.source,s=i.length;if(t>=s)return s;var r,n=i.charCodeAt(t);return!e&&!this.switchU||n<=55295||n>=57344||t+1>=s||(r=i.charCodeAt(t+1))<56320||r>57343?t+1:t+2},xt.prototype.current=function(t){return void 0===t&&(t=!1),this.at(this.pos,t)},xt.prototype.lookahead=function(t){return void 0===t&&(t=!1),this.at(this.nextIndex(this.pos,t),t)},xt.prototype.advance=function(t){void 0===t&&(t=!1),this.pos=this.nextIndex(this.pos,t)},xt.prototype.eat=function(t,e){return void 0===e&&(e=!1),this.current(e)===t&&(this.advance(e),!0)},mt.validateRegExpFlags=function(t){for(var e=t.validFlags,i=t.flags,s=0;s<i.length;s++){var r=i.charAt(s);-1===e.indexOf(r)&&this.raise(t.start,"Invalid regular expression flag"),i.indexOf(r,s+1)>-1&&this.raise(t.start,"Duplicate regular expression flag")}},mt.validateRegExpPattern=function(t){this.regexp_pattern(t),!t.switchN&&this.options.ecmaVersion>=9&&t.groupNames.length>0&&(t.switchN=!0,this.regexp_pattern(t))},mt.regexp_pattern=function(t){t.pos=0,t.lastIntValue=0,t.lastStringValue="",t.lastAssertionIsQuantifiable=!1,t.numCapturingParens=0,t.maxBackReference=0,t.groupNames.length=0,t.backReferenceNames.length=0,this.regexp_disjunction(t),t.pos!==t.source.length&&(t.eat(41)&&t.raise("Unmatched ')'"),(t.eat(93)||t.eat(125))&&t.raise("Lone quantifier brackets")),t.maxBackReference>t.numCapturingParens&&t.raise("Invalid escape");for(var e=0,i=t.backReferenceNames;e<i.length;e+=1){var s=i[e];-1===t.groupNames.indexOf(s)&&t.raise("Invalid named capture referenced")}},mt.regexp_disjunction=function(t){for(this.regexp_alternative(t);t.eat(124);)this.regexp_alternative(t);this.regexp_eatQuantifier(t,!0)&&t.raise("Nothing to repeat"),t.eat(123)&&t.raise("Lone quantifier brackets")},mt.regexp_alternative=function(t){for(;t.pos<t.source.length&&this.regexp_eatTerm(t););},mt.regexp_eatTerm=function(t){return this.regexp_eatAssertion(t)?(t.lastAssertionIsQuantifiable&&this.regexp_eatQuantifier(t)&&t.switchU&&t.raise("Invalid quantifier"),!0):!!(t.switchU?this.regexp_eatAtom(t):this.regexp_eatExtendedAtom(t))&&(this.regexp_eatQuantifier(t),!0)},mt.regexp_eatAssertion=function(t){var e=t.pos;if(t.lastAssertionIsQuantifiable=!1,t.eat(94)||t.eat(36))return!0;if(t.eat(92)){if(t.eat(66)||t.eat(98))return!0;t.pos=e}if(t.eat(40)&&t.eat(63)){var i=!1;if(this.options.ecmaVersion>=9&&(i=t.eat(60)),t.eat(61)||t.eat(33))return this.regexp_disjunction(t),t.eat(41)||t.raise("Unterminated group"),t.lastAssertionIsQuantifiable=!i,!0}return t.pos=e,!1},mt.regexp_eatQuantifier=function(t,e){return void 0===e&&(e=!1),!!this.regexp_eatQuantifierPrefix(t,e)&&(t.eat(63),!0)},mt.regexp_eatQuantifierPrefix=function(t,e){return t.eat(42)||t.eat(43)||t.eat(63)||this.regexp_eatBracedQuantifier(t,e)},mt.regexp_eatBracedQuantifier=function(t,e){var i=t.pos;if(t.eat(123)){var s=0,r=-1;if(this.regexp_eatDecimalDigits(t)&&(s=t.lastIntValue,t.eat(44)&&this.regexp_eatDecimalDigits(t)&&(r=t.lastIntValue),t.eat(125)))return-1!==r&&r<s&&!e&&t.raise("numbers out of order in {} quantifier"),!0;t.switchU&&!e&&t.raise("Incomplete quantifier"),t.pos=i}return!1},mt.regexp_eatAtom=function(t){return this.regexp_eatPatternCharacters(t)||t.eat(46)||this.regexp_eatReverseSolidusAtomEscape(t)||this.regexp_eatCharacterClass(t)||this.regexp_eatUncapturingGroup(t)||this.regexp_eatCapturingGroup(t)},mt.regexp_eatReverseSolidusAtomEscape=function(t){var e=t.pos;if(t.eat(92)){if(this.regexp_eatAtomEscape(t))return!0;t.pos=e}return!1},mt.regexp_eatUncapturingGroup=function(t){var e=t.pos;if(t.eat(40)){if(t.eat(63)&&t.eat(58)){if(this.regexp_disjunction(t),t.eat(41))return!0;t.raise("Unterminated group")}t.pos=e}return!1},mt.regexp_eatCapturingGroup=function(t){if(t.eat(40)){if(this.options.ecmaVersion>=9?this.regexp_groupSpecifier(t):63===t.current()&&t.raise("Invalid group"),this.regexp_disjunction(t),t.eat(41))return t.numCapturingParens+=1,!0;t.raise("Unterminated group")}return!1},mt.regexp_eatExtendedAtom=function(t){return t.eat(46)||this.regexp_eatReverseSolidusAtomEscape(t)||this.regexp_eatCharacterClass(t)||this.regexp_eatUncapturingGroup(t)||this.regexp_eatCapturingGroup(t)||this.regexp_eatInvalidBracedQuantifier(t)||this.regexp_eatExtendedPatternCharacter(t)},mt.regexp_eatInvalidBracedQuantifier=function(t){return this.regexp_eatBracedQuantifier(t,!0)&&t.raise("Nothing to repeat"),!1},mt.regexp_eatSyntaxCharacter=function(t){var e=t.current();return!!yt(e)&&(t.lastIntValue=e,t.advance(),!0)},mt.regexp_eatPatternCharacters=function(t){for(var e=t.pos,i=0;-1!==(i=t.current())&&!yt(i);)t.advance();return t.pos!==e},mt.regexp_eatExtendedPatternCharacter=function(t){var e=t.current();return!(-1===e||36===e||e>=40&&e<=43||46===e||63===e||91===e||94===e||124===e)&&(t.advance(),!0)},mt.regexp_groupSpecifier=function(t){if(t.eat(63)){if(this.regexp_eatGroupName(t))return-1!==t.groupNames.indexOf(t.lastStringValue)&&t.raise("Duplicate capture group name"),void t.groupNames.push(t.lastStringValue);t.raise("Invalid group")}},mt.regexp_eatGroupName=function(t){if(t.lastStringValue="",t.eat(60)){if(this.regexp_eatRegExpIdentifierName(t)&&t.eat(62))return!0;t.raise("Invalid capture group name")}return!1},mt.regexp_eatRegExpIdentifierName=function(t){if(t.lastStringValue="",this.regexp_eatRegExpIdentifierStart(t)){for(t.lastStringValue+=gt(t.lastIntValue);this.regexp_eatRegExpIdentifierPart(t);)t.lastStringValue+=gt(t.lastIntValue);return!0}return!1},mt.regexp_eatRegExpIdentifierStart=function(t){var e=t.pos,i=this.options.ecmaVersion>=11,s=t.current(i);return t.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(t,i)&&(s=t.lastIntValue),function(t){return l(t,!0)||36===t||95===t}(s)?(t.lastIntValue=s,!0):(t.pos=e,!1)},mt.regexp_eatRegExpIdentifierPart=function(t){var e=t.pos,i=this.options.ecmaVersion>=11,s=t.current(i);return t.advance(i),92===s&&this.regexp_eatRegExpUnicodeEscapeSequence(t,i)&&(s=t.lastIntValue),function(t){return u(t,!0)||36===t||95===t||8204===t||8205===t}(s)?(t.lastIntValue=s,!0):(t.pos=e,!1)},mt.regexp_eatAtomEscape=function(t){return!!(this.regexp_eatBackReference(t)||this.regexp_eatCharacterClassEscape(t)||this.regexp_eatCharacterEscape(t)||t.switchN&&this.regexp_eatKGroupName(t))||(t.switchU&&(99===t.current()&&t.raise("Invalid unicode escape"),t.raise("Invalid escape")),!1)},mt.regexp_eatBackReference=function(t){var e=t.pos;if(this.regexp_eatDecimalEscape(t)){var i=t.lastIntValue;if(t.switchU)return i>t.maxBackReference&&(t.maxBackReference=i),!0;if(i<=t.numCapturingParens)return!0;t.pos=e}return!1},mt.regexp_eatKGroupName=function(t){if(t.eat(107)){if(this.regexp_eatGroupName(t))return t.backReferenceNames.push(t.lastStringValue),!0;t.raise("Invalid named reference")}return!1},mt.regexp_eatCharacterEscape=function(t){return this.regexp_eatControlEscape(t)||this.regexp_eatCControlLetter(t)||this.regexp_eatZero(t)||this.regexp_eatHexEscapeSequence(t)||this.regexp_eatRegExpUnicodeEscapeSequence(t,!1)||!t.switchU&&this.regexp_eatLegacyOctalEscapeSequence(t)||this.regexp_eatIdentityEscape(t)},mt.regexp_eatCControlLetter=function(t){var e=t.pos;if(t.eat(99)){if(this.regexp_eatControlLetter(t))return!0;t.pos=e}return!1},mt.regexp_eatZero=function(t){return 48===t.current()&&!kt(t.lookahead())&&(t.lastIntValue=0,t.advance(),!0)},mt.regexp_eatControlEscape=function(t){var e=t.current();return 116===e?(t.lastIntValue=9,t.advance(),!0):110===e?(t.lastIntValue=10,t.advance(),!0):118===e?(t.lastIntValue=11,t.advance(),!0):102===e?(t.lastIntValue=12,t.advance(),!0):114===e&&(t.lastIntValue=13,t.advance(),!0)},mt.regexp_eatControlLetter=function(t){var e=t.current();return!!vt(e)&&(t.lastIntValue=e%32,t.advance(),!0)},mt.regexp_eatRegExpUnicodeEscapeSequence=function(t,e){void 0===e&&(e=!1);var i,s=t.pos,r=e||t.switchU;if(t.eat(117)){if(this.regexp_eatFixedHexDigits(t,4)){var n=t.lastIntValue;if(r&&n>=55296&&n<=56319){var a=t.pos;if(t.eat(92)&&t.eat(117)&&this.regexp_eatFixedHexDigits(t,4)){var o=t.lastIntValue;if(o>=56320&&o<=57343)return t.lastIntValue=1024*(n-55296)+(o-56320)+65536,!0}t.pos=a,t.lastIntValue=n}return!0}if(r&&t.eat(123)&&this.regexp_eatHexDigits(t)&&t.eat(125)&&((i=t.lastIntValue)>=0&&i<=1114111))return!0;r&&t.raise("Invalid unicode escape"),t.pos=s}return!1},mt.regexp_eatIdentityEscape=function(t){if(t.switchU)return!!this.regexp_eatSyntaxCharacter(t)||!!t.eat(47)&&(t.lastIntValue=47,!0);var e=t.current();return!(99===e||t.switchN&&107===e)&&(t.lastIntValue=e,t.advance(),!0)},mt.regexp_eatDecimalEscape=function(t){t.lastIntValue=0;var e=t.current();if(e>=49&&e<=57){do{t.lastIntValue=10*t.lastIntValue+(e-48),t.advance()}while((e=t.current())>=48&&e<=57);return!0}return!1},mt.regexp_eatCharacterClassEscape=function(t){var e=t.current();if(function(t){return 100===t||68===t||115===t||83===t||119===t||87===t}(e))return t.lastIntValue=-1,t.advance(),!0;if(t.switchU&&this.options.ecmaVersion>=9&&(80===e||112===e)){if(t.lastIntValue=-1,t.advance(),t.eat(123)&&this.regexp_eatUnicodePropertyValueExpression(t)&&t.eat(125))return!0;t.raise("Invalid property name")}return!1},mt.regexp_eatUnicodePropertyValueExpression=function(t){var e=t.pos;if(this.regexp_eatUnicodePropertyName(t)&&t.eat(61)){var i=t.lastStringValue;if(this.regexp_eatUnicodePropertyValue(t)){var s=t.lastStringValue;return this.regexp_validateUnicodePropertyNameAndValue(t,i,s),!0}}if(t.pos=e,this.regexp_eatLoneUnicodePropertyNameOrValue(t)){var r=t.lastStringValue;return this.regexp_validateUnicodePropertyNameOrValue(t,r),!0}return!1},mt.regexp_validateUnicodePropertyNameAndValue=function(t,e,i){A(t.unicodeProperties.nonBinary,e)||t.raise("Invalid property name"),t.unicodeProperties.nonBinary[e].test(i)||t.raise("Invalid property value")},mt.regexp_validateUnicodePropertyNameOrValue=function(t,e){t.unicodeProperties.binary.test(e)||t.raise("Invalid property name")},mt.regexp_eatUnicodePropertyName=function(t){var e=0;for(t.lastStringValue="";bt(e=t.current());)t.lastStringValue+=gt(e),t.advance();return""!==t.lastStringValue},mt.regexp_eatUnicodePropertyValue=function(t){var e=0;for(t.lastStringValue="";St(e=t.current());)t.lastStringValue+=gt(e),t.advance();return""!==t.lastStringValue},mt.regexp_eatLoneUnicodePropertyNameOrValue=function(t){return this.regexp_eatUnicodePropertyValue(t)},mt.regexp_eatCharacterClass=function(t){if(t.eat(91)){if(t.eat(94),this.regexp_classRanges(t),t.eat(93))return!0;t.raise("Unterminated character class")}return!1},mt.regexp_classRanges=function(t){for(;this.regexp_eatClassAtom(t);){var e=t.lastIntValue;if(t.eat(45)&&this.regexp_eatClassAtom(t)){var i=t.lastIntValue;!t.switchU||-1!==e&&-1!==i||t.raise("Invalid character class"),-1!==e&&-1!==i&&e>i&&t.raise("Range out of order in character class")}}},mt.regexp_eatClassAtom=function(t){var e=t.pos;if(t.eat(92)){if(this.regexp_eatClassEscape(t))return!0;if(t.switchU){var i=t.current();(99===i||_t(i))&&t.raise("Invalid class escape"),t.raise("Invalid escape")}t.pos=e}var s=t.current();return 93!==s&&(t.lastIntValue=s,t.advance(),!0)},mt.regexp_eatClassEscape=function(t){var e=t.pos;if(t.eat(98))return t.lastIntValue=8,!0;if(t.switchU&&t.eat(45))return t.lastIntValue=45,!0;if(!t.switchU&&t.eat(99)){if(this.regexp_eatClassControlLetter(t))return!0;t.pos=e}return this.regexp_eatCharacterClassEscape(t)||this.regexp_eatCharacterEscape(t)},mt.regexp_eatClassControlLetter=function(t){var e=t.current();return!(!kt(e)&&95!==e)&&(t.lastIntValue=e%32,t.advance(),!0)},mt.regexp_eatHexEscapeSequence=function(t){var e=t.pos;if(t.eat(120)){if(this.regexp_eatFixedHexDigits(t,2))return!0;t.switchU&&t.raise("Invalid escape"),t.pos=e}return!1},mt.regexp_eatDecimalDigits=function(t){var e=t.pos,i=0;for(t.lastIntValue=0;kt(i=t.current());)t.lastIntValue=10*t.lastIntValue+(i-48),t.advance();return t.pos!==e},mt.regexp_eatHexDigits=function(t){var e=t.pos,i=0;for(t.lastIntValue=0;Ct(i=t.current());)t.lastIntValue=16*t.lastIntValue+Et(i),t.advance();return t.pos!==e},mt.regexp_eatLegacyOctalEscapeSequence=function(t){if(this.regexp_eatOctalDigit(t)){var e=t.lastIntValue;if(this.regexp_eatOctalDigit(t)){var i=t.lastIntValue;e<=3&&this.regexp_eatOctalDigit(t)?t.lastIntValue=64*e+8*i+t.lastIntValue:t.lastIntValue=8*e+i}else t.lastIntValue=e;return!0}return!1},mt.regexp_eatOctalDigit=function(t){var e=t.current();return _t(e)?(t.lastIntValue=e-48,t.advance(),!0):(t.lastIntValue=0,!1)},mt.regexp_eatFixedHexDigits=function(t,e){var i=t.pos;t.lastIntValue=0;for(var s=0;s<e;++s){var r=t.current();if(!Ct(r))return t.pos=i,!1;t.lastIntValue=16*t.lastIntValue+Et(r),t.advance()}return!0};var wt=function(t){this.type=t.type,this.value=t.value,this.start=t.start,this.end=t.end,t.options.locations&&(this.loc=new V(t,t.startLoc,t.endLoc)),t.options.ranges&&(this.range=[t.start,t.end])},It=F.prototype;function At(t){return"function"!=typeof BigInt?null:BigInt(t.replace(/_/g,""))}function Pt(t){return t<=65535?String.fromCharCode(t):(t-=65536,String.fromCharCode(55296+(t>>10),56320+(1023&t)))}It.next=function(t){!t&&this.type.keyword&&this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+this.type.keyword),this.options.onToken&&this.options.onToken(new wt(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},It.getToken=function(){return this.next(),new wt(this)},"undefined"!=typeof Symbol&&(It[Symbol.iterator]=function(){var t=this;return{next:function(){var e=t.getToken();return{done:e.type===v.eof,value:e}}}}),It.curContext=function(){return this.context[this.context.length-1]},It.nextToken=function(){var t=this.curContext();return t&&t.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(v.eof):t.override?t.override(this):void this.readToken(this.fullCharCodeAtPos())},It.readToken=function(t){return l(t,this.options.ecmaVersion>=6)||92===t?this.readWord():this.getTokenFromCode(t)},It.fullCharCodeAtPos=function(){var t=this.input.charCodeAt(this.pos);return t<=55295||t>=57344?t:(t<<10)+this.input.charCodeAt(this.pos+1)-56613888},It.skipBlockComment=function(){var t,e=this.options.onComment&&this.curPosition(),i=this.pos,s=this.input.indexOf("*/",this.pos+=2);if(-1===s&&this.raise(this.pos-2,"Unterminated comment"),this.pos=s+2,this.options.locations)for(S.lastIndex=i;(t=S.exec(this.input))&&t.index<this.pos;)++this.curLine,this.lineStart=t.index+t[0].length;this.options.onComment&&this.options.onComment(!0,this.input.slice(i+2,s),i,this.pos,e,this.curPosition())},It.skipLineComment=function(t){for(var e=this.pos,i=this.options.onComment&&this.curPosition(),s=this.input.charCodeAt(this.pos+=t);this.pos<this.input.length&&!k(s);)s=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(e+t,this.pos),e,this.pos,i,this.curPosition())},It.skipSpace=function(){t:for(;this.pos<this.input.length;){var t=this.input.charCodeAt(this.pos);switch(t){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break t}break;default:if(!(t>8&&t<14||t>=5760&&C.test(String.fromCharCode(t))))break t;++this.pos}}},It.finishToken=function(t,e){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var i=this.type;this.type=t,this.value=e,this.updateContext(i)},It.readToken_dot=function(){var t=this.input.charCodeAt(this.pos+1);if(t>=48&&t<=57)return this.readNumber(!0);var e=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===t&&46===e?(this.pos+=3,this.finishToken(v.ellipsis)):(++this.pos,this.finishToken(v.dot))},It.readToken_slash=function(){var t=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===t?this.finishOp(v.assign,2):this.finishOp(v.slash,1)},It.readToken_mult_modulo_exp=function(t){var e=this.input.charCodeAt(this.pos+1),i=1,s=42===t?v.star:v.modulo;return this.options.ecmaVersion>=7&&42===t&&42===e&&(++i,s=v.starstar,e=this.input.charCodeAt(this.pos+2)),61===e?this.finishOp(v.assign,i+1):this.finishOp(s,i)},It.readToken_pipe_amp=function(t){var e=this.input.charCodeAt(this.pos+1);if(e===t){if(this.options.ecmaVersion>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(v.assign,3);return this.finishOp(124===t?v.logicalOR:v.logicalAND,2)}return 61===e?this.finishOp(v.assign,2):this.finishOp(124===t?v.bitwiseOR:v.bitwiseAND,1)},It.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(v.assign,2):this.finishOp(v.bitwiseXOR,1)},It.readToken_plus_min=function(t){var e=this.input.charCodeAt(this.pos+1);return e===t?45!==e||this.inModule||62!==this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!b.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(v.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===e?this.finishOp(v.assign,2):this.finishOp(v.plusMin,1)},It.readToken_lt_gt=function(t){var e=this.input.charCodeAt(this.pos+1),i=1;return e===t?(i=62===t&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+i)?this.finishOp(v.assign,i+1):this.finishOp(v.bitShift,i)):33!==e||60!==t||this.inModule||45!==this.input.charCodeAt(this.pos+2)||45!==this.input.charCodeAt(this.pos+3)?(61===e&&(i=2),this.finishOp(v.relational,i)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},It.readToken_eq_excl=function(t){var e=this.input.charCodeAt(this.pos+1);return 61===e?this.finishOp(v.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===t&&62===e&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(v.arrow)):this.finishOp(61===t?v.eq:v.prefix,1)},It.readToken_question=function(){var t=this.options.ecmaVersion;if(t>=11){var e=this.input.charCodeAt(this.pos+1);if(46===e){var i=this.input.charCodeAt(this.pos+2);if(i<48||i>57)return this.finishOp(v.questionDot,2)}if(63===e){if(t>=12)if(61===this.input.charCodeAt(this.pos+2))return this.finishOp(v.assign,3);return this.finishOp(v.coalesce,2)}}return this.finishOp(v.question,1)},It.getTokenFromCode=function(t){switch(t){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(v.parenL);case 41:return++this.pos,this.finishToken(v.parenR);case 59:return++this.pos,this.finishToken(v.semi);case 44:return++this.pos,this.finishToken(v.comma);case 91:return++this.pos,this.finishToken(v.bracketL);case 93:return++this.pos,this.finishToken(v.bracketR);case 123:return++this.pos,this.finishToken(v.braceL);case 125:return++this.pos,this.finishToken(v.braceR);case 58:return++this.pos,this.finishToken(v.colon);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(v.backQuote);case 48:var e=this.input.charCodeAt(this.pos+1);if(120===e||88===e)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===e||79===e)return this.readRadixNumber(8);if(98===e||66===e)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(t);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(t);case 124:case 38:return this.readToken_pipe_amp(t);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(t);case 60:case 62:return this.readToken_lt_gt(t);case 61:case 33:return this.readToken_eq_excl(t);case 63:return this.readToken_question();case 126:return this.finishOp(v.prefix,1)}this.raise(this.pos,"Unexpected character '"+Pt(t)+"'")},It.finishOp=function(t,e){var i=this.input.slice(this.pos,this.pos+e);return this.pos+=e,this.finishToken(t,i)},It.readRegexp=function(){for(var t,e,i=this.pos;;){this.pos>=this.input.length&&this.raise(i,"Unterminated regular expression");var s=this.input.charAt(this.pos);if(b.test(s)&&this.raise(i,"Unterminated regular expression"),t)t=!1;else{if("["===s)e=!0;else if("]"===s&&e)e=!1;else if("/"===s&&!e)break;t="\\"===s}++this.pos}var r=this.input.slice(i,this.pos);++this.pos;var n=this.pos,a=this.readWord1();this.containsEsc&&this.unexpected(n);var o=this.regexpState||(this.regexpState=new xt(this));o.reset(i,r,a),this.validateRegExpFlags(o),this.validateRegExpPattern(o);var h=null;try{h=new RegExp(r,a)}catch(c){}return this.finishToken(v.regexp,{pattern:r,flags:a,value:h})},It.readInt=function(t,e,i){for(var s=this.options.ecmaVersion>=12&&void 0===e,r=i&&48===this.input.charCodeAt(this.pos),n=this.pos,a=0,o=0,h=0,c=null==e?1/0:e;h<c;++h,++this.pos){var p=this.input.charCodeAt(this.pos),l=void 0;if(s&&95===p)r&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed in legacy octal numeric literals"),95===o&&this.raiseRecoverable(this.pos,"Numeric separator must be exactly one underscore"),0===h&&this.raiseRecoverable(this.pos,"Numeric separator is not allowed at the first of digits"),o=p;else{if((l=p>=97?p-97+10:p>=65?p-65+10:p>=48&&p<=57?p-48:1/0)>=t)break;o=p,a=a*t+l}}return s&&95===o&&this.raiseRecoverable(this.pos-1,"Numeric separator is not allowed at the last of digits"),this.pos===n||null!=e&&this.pos-n!==e?null:a},It.readRadixNumber=function(t){var e=this.pos;this.pos+=2;var i=this.readInt(t);return null==i&&this.raise(this.start+2,"Expected number in radix "+t),this.options.ecmaVersion>=11&&110===this.input.charCodeAt(this.pos)?(i=At(this.input.slice(e,this.pos)),++this.pos):l(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(v.num,i)},It.readNumber=function(t){var e=this.pos;t||null!==this.readInt(10,void 0,!0)||this.raise(e,"Invalid number");var i=this.pos-e>=2&&48===this.input.charCodeAt(e);i&&this.strict&&this.raise(e,"Invalid number");var s=this.input.charCodeAt(this.pos);if(!i&&!t&&this.options.ecmaVersion>=11&&110===s){var r=At(this.input.slice(e,this.pos));return++this.pos,l(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(v.num,r)}i&&/[89]/.test(this.input.slice(e,this.pos))&&(i=!1),46!==s||i||(++this.pos,this.readInt(10),s=this.input.charCodeAt(this.pos)),69!==s&&101!==s||i||(43!==(s=this.input.charCodeAt(++this.pos))&&45!==s||++this.pos,null===this.readInt(10)&&this.raise(e,"Invalid number")),l(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var n,a=(n=this.input.slice(e,this.pos),i?parseInt(n,8):parseFloat(n.replace(/_/g,"")));return this.finishToken(v.num,a)},It.readCodePoint=function(){var t;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var e=++this.pos;t=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,t>1114111&&this.invalidStringToken(e,"Code point out of bounds")}else t=this.readHexChar(4);return t},It.readString=function(t){for(var e="",i=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var s=this.input.charCodeAt(this.pos);if(s===t)break;92===s?(e+=this.input.slice(i,this.pos),e+=this.readEscapedChar(!1),i=this.pos):(k(s,this.options.ecmaVersion>=10)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return e+=this.input.slice(i,this.pos++),this.finishToken(v.string,e)};var Tt={};It.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(t){if(t!==Tt)throw t;this.readInvalidTemplateToken()}this.inTemplateElement=!1},It.invalidStringToken=function(t,e){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw Tt;this.raise(t,e)},It.readTmplToken=function(){for(var t="",e=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var i=this.input.charCodeAt(this.pos);if(96===i||36===i&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==v.template&&this.type!==v.invalidTemplate?(t+=this.input.slice(e,this.pos),this.finishToken(v.template,t)):36===i?(this.pos+=2,this.finishToken(v.dollarBraceL)):(++this.pos,this.finishToken(v.backQuote));if(92===i)t+=this.input.slice(e,this.pos),t+=this.readEscapedChar(!0),e=this.pos;else if(k(i)){switch(t+=this.input.slice(e,this.pos),++this.pos,i){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:t+="\n";break;default:t+=String.fromCharCode(i)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),e=this.pos}else++this.pos}},It.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(v.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},It.readEscapedChar=function(t){var e=this.input.charCodeAt(++this.pos);switch(++this.pos,e){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return Pt(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";case 56:case 57:if(t){var i=this.pos-1;return this.invalidStringToken(i,"Invalid escape sequence in template string"),null}default:if(e>=48&&e<=55){var s=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(s,8);return r>255&&(s=s.slice(0,-1),r=parseInt(s,8)),this.pos+=s.length-1,e=this.input.charCodeAt(this.pos),"0"===s&&56!==e&&57!==e||!this.strict&&!t||this.invalidStringToken(this.pos-1-s.length,t?"Octal literal in template string":"Octal literal in strict mode"),String.fromCharCode(r)}return k(e)?"":String.fromCharCode(e)}},It.readHexChar=function(t){var e=this.pos,i=this.readInt(16,t);return null===i&&this.invalidStringToken(e,"Bad character escape sequence"),i},It.readWord1=function(){this.containsEsc=!1;for(var t="",e=!0,i=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var r=this.fullCharCodeAtPos();if(u(r,s))this.pos+=r<=65535?1:2;else{if(92!==r)break;this.containsEsc=!0,t+=this.input.slice(i,this.pos);var n=this.pos;117!==this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var a=this.readCodePoint();(e?l:u)(a,s)||this.invalidStringToken(n,"Invalid Unicode escape"),t+=Pt(a),i=this.pos}e=!1}return t+this.input.slice(i,this.pos)},It.readWord=function(){var t=this.readWord1(),e=v.name;return this.keywords.test(t)&&(e=g[t]),this.finishToken(e,t)};F.acorn={Parser:F,version:"7.4.1",defaultOptions:R,Position:N,SourceLocation:V,getLineInfo:L,Node:tt,TokenType:d,tokTypes:v,keywordTypes:g,TokContext:st,tokContexts:rt,isIdentifierChar:u,isIdentifierStart:l,Token:wt,isNewLine:k,lineBreak:b,lineBreakG:S,nonASCIIwhitespace:C};class Nt extends Error{}class Vt extends SyntaxError{}class Lt extends ReferenceError{}class Rt extends TypeError{}class Ot extends Nt{}class Dt extends Vt{}class Ft extends Lt{}const Mt={UnknownError:[3001,"%0",Ot],ExecutionTimeOutError:[3002,"Script execution timed out after %0ms",Ot],NodeTypeSyntaxError:[1001,"Unknown node type: %0",Ft],BinaryOperatorSyntaxError:[1002,"Unknown binary operator: %0",Ft],LogicalOperatorSyntaxError:[1003,"Unknown logical operator: %0",Ft],UnaryOperatorSyntaxError:[1004,"Unknown unary operator: %0",Ft],UpdateOperatorSyntaxError:[1005,"Unknown update operator: %0",Ft],ObjectStructureSyntaxError:[1006,"Unknown object structure: %0",Ft],AssignmentExpressionSyntaxError:[1007,"Unknown assignment expression: %0",Ft],VariableTypeSyntaxError:[1008,"Unknown variable type: %0",Ft],ParamTypeSyntaxError:[1009,"Unknown param type: %0",Ft],AssignmentTypeSyntaxError:[1010,"Unknown assignment type: %0",Ft],FunctionUndefinedReferenceError:[2001,"%0 is not a function",Lt],VariableUndefinedReferenceError:[2002,"%0 is not defined",Lt],IsNotConstructor:[2003,"%0 is not a constructor",Rt]};function Bt(t,e){Object.defineProperty(t,"name",{value:e,writable:!1,enumerable:!1,configurable:!0})}const Ut=Object.prototype.hasOwnProperty,Ht=Symbol("Break"),jt=Symbol("Continue"),Gt=Symbol("DefaultCase"),qt=Symbol("EmptyStatementReturn"),Wt=Symbol("WithScopeName"),zt=Symbol("SuperScopeName"),Qt=Symbol("RootScopeName"),Kt=Symbol("GlobalScopeName");function $t(t){return"function"==typeof t}class Xt{constructor(t){this.interpreter=t}generator(){const t=this.interpreter;return{getOptions:t.getOptions.bind(t),getCurrentScope:function(){return this.getCurrentScope()}.bind(t),getGlobalScope:function(){return this.getGlobalScope()}.bind(t),getCurrentContext:function(){return this.getCurrentContext()}.bind(t),getExecStartTime:t.getExecStartTime.bind(t)}}}function Yt(t,e,i=!0){if(!(t instanceof Xt))throw new Error("Illegal call");if("string"!=typeof e)return e;if(!e)return;const s=t.generator(),r={timeout:s.getOptions().timeout,_initEnv:function(){i||this.setCurrentContext(s.getCurrentContext()),this.execStartTime=s.getExecStartTime(),this.execEndTime=this.execStartTime}},n=i?s.getGlobalScope():s.getCurrentScope();return new ne(n,r).evaluate(e)}function Zt(t,...e){if(!(t instanceof Xt))throw new Error("Illegal call");const i=t.generator(),s=e.pop(),r=new ne(i.getGlobalScope(),i.getOptions()),n=`\n\t\t    (function anonymous(${e.join(",")}){\n\t\t        ${s}\n\t\t    });\n\t\t    `;return r.evaluate(n)}Object.defineProperty(Yt,"__IS_EVAL_FUNC",{value:!0,writable:!1,enumerable:!1,configurable:!1}),Object.defineProperty(Zt,"__IS_FUNCTION_FUNC",{value:!0,writable:!1,enumerable:!1,configurable:!1});class Jt{constructor(t){this.value=t}}class te{constructor(t){this.value=t}}class ee{constructor(t){this.value=t}}class ie{constructor(t,e=null,i){this.name=i,this.parent=e,this.data=t,this.labelStack=[]}}function se(){}const re={NaN:NaN,Infinity:1/0,undefined:void 0,Object:Object,Array:Array,String:String,Boolean:Boolean,Number:Number,Date:Date,RegExp:RegExp,Error:Error,URIError:URIError,TypeError:TypeError,RangeError:RangeError,SyntaxError:SyntaxError,ReferenceError:ReferenceError,Math:Math,parseInt:parseInt,parseFloat:parseFloat,isNaN:isNaN,isFinite:isFinite,decodeURI:decodeURI,decodeURIComponent:decodeURIComponent,encodeURI:encodeURI,encodeURIComponent:encodeURIComponent,escape:escape,unescape:unescape,eval:Yt,Function:Zt};"undefined"!=typeof JSON&&(re.JSON=JSON),"undefined"!=typeof Promise&&(re.Promise=Promise),"undefined"!=typeof Set&&(re.Set=Set),"undefined"!=typeof Map&&(re.Map=Map),"undefined"!=typeof Symbol&&(re.Symbol=Symbol),"undefined"!=typeof Proxy&&(re.Proxy=Proxy),"undefined"!=typeof WeakMap&&(re.WeakMap=WeakMap),"undefined"!=typeof WeakSet&&(re.WeakSet=WeakSet),"undefined"!=typeof Reflect&&(re.Reflect=Reflect);class ne{constructor(t=ne.global,e={}){this.sourceList=[],this.collectDeclVars=Object.create(null),this.collectDeclFuncs=Object.create(null),this.isVarDeclMode=!1,this.lastExecNode=null,this.isRunning=!1,this.options={ecmaVersion:e.ecmaVersion||ne.ecmaVersion,timeout:e.timeout||0,rootContext:e.rootContext,globalContextInFunction:void 0===e.globalContextInFunction?ne.globalContextInFunction:e.globalContextInFunction,_initEnv:e._initEnv},this.context=t||Object.create(null),this.callStack=[],this.initEnvironment(this.context)}initEnvironment(t){let e;if(t instanceof ie)e=t;else{let s=null;const r=this.createSuperScope(t);this.options.rootContext&&(s=new ie((i=this.options.rootContext,Object.create(i)),r,Qt)),e=new ie(t,s||r,Kt)}var i;this.globalScope=e,this.currentScope=this.globalScope,this.globalContext=e.data,this.currentContext=e.data,this.collectDeclVars=Object.create(null),this.collectDeclFuncs=Object.create(null),this.execStartTime=Date.now(),this.execEndTime=this.execStartTime;const s=this.options._initEnv;s&&s.call(this)}getExecStartTime(){return this.execStartTime}getExecutionTime(){return this.execEndTime-this.execStartTime}setExecTimeout(t=0){this.options.timeout=t}getOptions(){return this.options}getGlobalScope(){return this.globalScope}getCurrentScope(){return this.currentScope}getCurrentContext(){return this.currentContext}isInterruptThrow(t){return t instanceof Ot||t instanceof Ft||t instanceof Dt}createSuperScope(t){let e=Object.assign({},re);return Object.keys(e).forEach((i=>{i in t&&delete e[i]})),new ie(e,null,zt)}setCurrentContext(t){this.currentContext=t}setCurrentScope(t){this.currentScope=t}evaluate(t=""){let e;var i,s;if(t)return i=t,s={ranges:!0,locations:!0,ecmaVersion:this.options.ecmaVersion||ne.ecmaVersion},e=F.parse(i,s),this.evaluateNode(e,t)}appendCode(t){return this.evaluate(t)}evaluateNode(t,e=""){this.value=void 0,this.source=e,this.sourceList.push(e),this.isRunning=!0,this.execStartTime=Date.now(),this.execEndTime=this.execStartTime,this.collectDeclVars=Object.create(null),this.collectDeclFuncs=Object.create(null);const i=this.getCurrentScope(),s=this.getCurrentContext(),r=i.labelStack.concat([]),n=this.callStack.concat([]),a=()=>{this.setCurrentScope(i),this.setCurrentContext(s),i.labelStack=r,this.callStack=n};try{const e=this.createClosure(t);this.addDeclarationsToScope(this.collectDeclVars,this.collectDeclFuncs,this.getCurrentScope()),e()}catch(o){throw o}finally{a(),this.execEndTime=Date.now()}return this.isRunning=!1,this.getValue()}createErrorMessage(t,e,i){let s=t[1].replace("%0",String(e));return null!==i&&(s+=this.getNodePosition(i||this.lastExecNode)),s}createError(t,e){return new e(t)}createThrowError(t,e){return this.createError(t,e)}createInternalThrowError(t,e,i){return this.createError(this.createErrorMessage(t,e,i),t[2])}checkTimeout(){if(!this.isRunning)return!1;const t=this.options.timeout||0;return Date.now()-this.execStartTime>t}getNodePosition(t){if(t){const e="";return t.loc?` [${t.loc.start.line}:${t.loc.start.column}]${e}`:""}return""}createClosure(t){let e;switch(t.type){case"BinaryExpression":e=this.binaryExpressionHandler(t);break;case"LogicalExpression":e=this.logicalExpressionHandler(t);break;case"UnaryExpression":e=this.unaryExpressionHandler(t);break;case"UpdateExpression":e=this.updateExpressionHandler(t);break;case"ObjectExpression":e=this.objectExpressionHandler(t);break;case"ArrayExpression":e=this.arrayExpressionHandler(t);break;case"CallExpression":e=this.callExpressionHandler(t);break;case"NewExpression":e=this.newExpressionHandler(t);break;case"MemberExpression":e=this.memberExpressionHandler(t);break;case"ThisExpression":e=this.thisExpressionHandler(t);break;case"SequenceExpression":e=this.sequenceExpressionHandler(t);break;case"Literal":e=this.literalHandler(t);break;case"Identifier":e=this.identifierHandler(t);break;case"AssignmentExpression":e=this.assignmentExpressionHandler(t);break;case"FunctionDeclaration":e=this.functionDeclarationHandler(t);break;case"VariableDeclaration":e=this.variableDeclarationHandler(t);break;case"BlockStatement":case"Program":e=this.programHandler(t);break;case"ExpressionStatement":e=this.expressionStatementHandler(t);break;case"EmptyStatement":e=this.emptyStatementHandler(t);break;case"ReturnStatement":e=this.returnStatementHandler(t);break;case"FunctionExpression":e=this.functionExpressionHandler(t);break;case"IfStatement":e=this.ifStatementHandler(t);break;case"ConditionalExpression":e=this.conditionalExpressionHandler(t);break;case"ForStatement":e=this.forStatementHandler(t);break;case"WhileStatement":e=this.whileStatementHandler(t);break;case"DoWhileStatement":e=this.doWhileStatementHandler(t);break;case"ForInStatement":e=this.forInStatementHandler(t);break;case"WithStatement":e=this.withStatementHandler(t);break;case"ThrowStatement":e=this.throwStatementHandler(t);break;case"TryStatement":e=this.tryStatementHandler(t);break;case"ContinueStatement":e=this.continueStatementHandler(t);break;case"BreakStatement":e=this.breakStatementHandler(t);break;case"SwitchStatement":e=this.switchStatementHandler(t);break;case"LabeledStatement":e=this.labeledStatementHandler(t);break;case"DebuggerStatement":e=this.debuggerStatementHandler(t);break;default:throw this.createInternalThrowError(Mt.NodeTypeSyntaxError,t.type,t)}return(...i)=>{const s=this.options.timeout;if(s&&s>0&&this.checkTimeout())throw this.createInternalThrowError(Mt.ExecutionTimeOutError,s,null);return this.lastExecNode=t,e(...i)}}binaryExpressionHandler(t){const e=this.createClosure(t.left),i=this.createClosure(t.right);return()=>{const s=e(),r=i();switch(t.operator){case"==":return s==r;case"!=":return s!=r;case"===":return s===r;case"!==":return s!==r;case"<":return s<r;case"<=":return s<=r;case">":return s>r;case">=":return s>=r;case"<<":return s<<r;case">>":return s>>r;case">>>":return s>>>r;case"+":return s+r;case"-":return s-r;case"*":return s*r;case"**":return Math.pow(s,r);case"/":return s/r;case"%":return s%r;case"|":return s|r;case"^":return s^r;case"&":return s&r;case"in":return s in r;case"instanceof":return s instanceof r;default:throw this.createInternalThrowError(Mt.BinaryOperatorSyntaxError,t.operator,t)}}}logicalExpressionHandler(t){const e=this.createClosure(t.left),i=this.createClosure(t.right);return()=>{switch(t.operator){case"||":return e()||i();case"&&":return e()&&i();default:throw this.createInternalThrowError(Mt.LogicalOperatorSyntaxError,t.operator,t)}}}unaryExpressionHandler(t){if("delete"===t.operator){const e=this.createObjectGetter(t.argument),i=this.createNameGetter(t.argument);return()=>delete e()[i()]}{let e;if("typeof"===t.operator&&"Identifier"===t.argument.type){const i=this.createObjectGetter(t.argument),s=this.createNameGetter(t.argument);e=()=>i()[s()]}else e=this.createClosure(t.argument);return()=>{const i=e();switch(t.operator){case"-":return-i;case"+":return+i;case"!":return!i;case"~":return~i;case"void":return;case"typeof":return typeof i;default:throw this.createInternalThrowError(Mt.UnaryOperatorSyntaxError,t.operator,t)}}}}updateExpressionHandler(t){const e=this.createObjectGetter(t.argument),i=this.createNameGetter(t.argument);return()=>{const s=e(),r=i();switch(this.assertVariable(s,r,t),t.operator){case"++":return t.prefix?++s[r]:s[r]++;case"--":return t.prefix?--s[r]:s[r]--;default:throw this.createInternalThrowError(Mt.UpdateOperatorSyntaxError,t.operator,t)}}}objectExpressionHandler(t){const e=[];const i=Object.create(null);return t.properties.forEach((t=>{const s=t.kind,r=function(t){return"Identifier"===t.type?t.name:"Literal"===t.type?t.value:this.throwError(Mt.ObjectStructureSyntaxError,t.type,t)}(t.key);i[r]&&"init"!==s||(i[r]={}),i[r][s]=this.createClosure(t.value),e.push({key:r,property:t})})),()=>{const t={},s=e.length;for(let r=0;r<s;r++){const s=e[r],n=s.key,a=i[n],o=a.init?a.init():void 0,h=a.get?a.get():function(){},c=a.set?a.set():function(t){};if("set"in a||"get"in a){const e={configurable:!0,enumerable:!0,get:h,set:c};Object.defineProperty(t,n,e)}else{const e=s.property,i=e.kind;"Identifier"!==e.key.type||"FunctionExpression"!==e.value.type||"init"!==i||e.value.id||Bt(o,e.key.name),t[n]=o}}return t}}arrayExpressionHandler(t){const e=t.elements.map((t=>t?this.createClosure(t):t));return()=>{const t=e.length,i=Array(t);for(let s=0;s<t;s++){const t=e[s];t&&(i[s]=t())}return i}}safeObjectGet(t,e,i){return t[e]}createCallFunctionGetter(t){if("MemberExpression"===t.type){const e=this.createClosure(t.object),i=this.createMemberKeyGetter(t),s=this.source;return()=>{const r=e(),n=i(),a=this.safeObjectGet(r,n,t);if(!a||!$t(a)){const e=s.slice(t.start,t.end);throw this.createInternalThrowError(Mt.FunctionUndefinedReferenceError,e,t)}return a.__IS_EVAL_FUNC?t=>a(new Xt(this),t,!0):a.__IS_FUNCTION_FUNC?(...t)=>a(new Xt(this),...t):a.bind(r)}}{const e=this.createClosure(t);return()=>{let i="";"Identifier"===t.type&&(i=t.name);const s=e();if(!s||!$t(s))throw this.createInternalThrowError(Mt.FunctionUndefinedReferenceError,i,t);if("Identifier"===t.type&&s.__IS_EVAL_FUNC&&"eval"===i)return t=>{const e=this.getScopeFromName(i,this.getCurrentScope()),r=e.name===zt||e.name===Kt||e.name===Qt;return s(new Xt(this),t,!r)};if(s.__IS_EVAL_FUNC)return t=>s(new Xt(this),t,!0);if(s.__IS_FUNCTION_FUNC)return(...t)=>s(new Xt(this),...t);let r=this.options.globalContextInFunction;if("Identifier"===t.type){const e=this.getIdentifierScope(t);e.name===Wt&&(r=e.data)}return s.bind(r)}}}callExpressionHandler(t){const e=this.createCallFunctionGetter(t.callee),i=t.arguments.map((t=>this.createClosure(t)));return()=>e()(...i.map((t=>t())))}functionExpressionHandler(t){const e=this,i=this.source,s=this.collectDeclVars,r=this.collectDeclFuncs;this.collectDeclVars=Object.create(null),this.collectDeclFuncs=Object.create(null);const n=t.id?t.id.name:"",a=t.params.length,o=t.params.map((t=>this.createParamNameGetter(t))),h=this.createClosure(t.body),c=this.collectDeclVars,p=this.collectDeclFuncs;return this.collectDeclVars=s,this.collectDeclFuncs=r,()=>{const s=e.getCurrentScope(),r=function(...t){e.callStack.push(`${n}`);const i=e.getCurrentScope(),a=function(t=null,e){return new ie(Object.create(null),t,e)}(s,`FunctionScope(${n})`);e.setCurrentScope(a),e.addDeclarationsToScope(c,p,a),n&&(a.data[n]=r),a.data.arguments=arguments,o.forEach(((e,i)=>{a.data[e()]=t[i]}));const l=e.getCurrentContext();e.setCurrentContext(this);const u=h();if(e.setCurrentContext(l),e.setCurrentScope(i),e.callStack.pop(),u instanceof Jt)return u.value};return Bt(r,n),Object.defineProperty(r,"length",{value:a,writable:!1,enumerable:!1,configurable:!0}),Object.defineProperty(r,"toString",{value:()=>i.slice(t.start,t.end),writable:!0,configurable:!0,enumerable:!1}),Object.defineProperty(r,"valueOf",{value:()=>i.slice(t.start,t.end),writable:!0,configurable:!0,enumerable:!1}),r}}newExpressionHandler(t){const e=this.source,i=this.createClosure(t.callee),s=t.arguments.map((t=>this.createClosure(t)));return()=>{const r=i();if(!$t(r)||r.__IS_EVAL_FUNC){const i=t.callee,s=e.slice(i.start,i.end);throw this.createInternalThrowError(Mt.IsNotConstructor,s,t)}return r.__IS_FUNCTION_FUNC?r(new Xt(this),...s.map((t=>t()))):new r(...s.map((t=>t())))}}memberExpressionHandler(t){const e=this.createClosure(t.object),i=this.createMemberKeyGetter(t);return()=>e()[i()]}thisExpressionHandler(t){return()=>this.getCurrentContext()}sequenceExpressionHandler(t){const e=t.expressions.map((t=>this.createClosure(t)));return()=>{let t;const i=e.length;for(let s=0;s<i;s++){t=(0,e[s])()}return t}}literalHandler(t){return()=>t.regex?new RegExp(t.regex.pattern,t.regex.flags):t.value}identifierHandler(t){return()=>{const e=this.getCurrentScope(),i=this.getScopeDataFromName(t.name,e);return this.assertVariable(i,t.name,t),i[t.name]}}getIdentifierScope(t){const e=this.getCurrentScope();return this.getScopeFromName(t.name,e)}assignmentExpressionHandler(t){"Identifier"!==t.left.type||"FunctionExpression"!==t.right.type||t.right.id||(t.right.id={type:"Identifier",name:t.left.name});const e=this.createObjectGetter(t.left),i=this.createNameGetter(t.left),s=this.createClosure(t.right);return()=>{const r=e(),n=i(),a=s();switch("="!==t.operator&&this.assertVariable(r,n,t),t.operator){case"=":return r[n]=a;case"+=":return r[n]+=a;case"-=":return r[n]-=a;case"*=":return r[n]*=a;case"**=":return r[n]=Math.pow(r[n],a);case"/=":return r[n]/=a;case"%=":return r[n]%=a;case"<<=":return r[n]<<=a;case">>=":return r[n]>>=a;case">>>=":return r[n]>>>=a;case"&=":return r[n]&=a;case"^=":return r[n]^=a;case"|=":return r[n]|=a;default:throw this.createInternalThrowError(Mt.AssignmentExpressionSyntaxError,t.type,t)}}}functionDeclarationHandler(t){if(t.id){const e=this.functionExpressionHandler(t);Object.defineProperty(e,"isFunctionDeclareClosure",{value:!0,writable:!1,configurable:!1,enumerable:!1}),this.funcDeclaration(t.id.name,e)}return()=>qt}getVariableName(t){if("Identifier"===t.type)return t.name;throw this.createInternalThrowError(Mt.VariableTypeSyntaxError,t.type,t)}variableDeclarationHandler(t){let e;const i=[];for(let s=0;s<t.declarations.length;s++){const e=t.declarations[s];this.varDeclaration(this.getVariableName(e.id)),e.init&&i.push({type:"AssignmentExpression",operator:"=",left:e.id,right:e.init})}return i.length&&(e=this.createClosure({type:"BlockStatement",body:i})),()=>{if(e){const t=this.isVarDeclMode;this.isVarDeclMode=!0,e(),this.isVarDeclMode=t}return qt}}assertVariable(t,e,i){if(t===this.globalScope.data&&!(e in t))throw this.createInternalThrowError(Mt.VariableUndefinedReferenceError,e,i)}programHandler(t){const e=t.body.map((t=>this.createClosure(t)));return()=>{let t=qt;for(let i=0;i<e.length;i++){const s=e[i],r=this.setValue(s());if(r!==qt&&(t=r,t instanceof Jt||t instanceof te||t instanceof ee||t===Ht||t===jt))break}return t}}expressionStatementHandler(t){return this.createClosure(t.expression)}emptyStatementHandler(t){return()=>qt}returnStatementHandler(t){const e=t.argument?this.createClosure(t.argument):se;return()=>new Jt(e())}ifStatementHandler(t){const e=this.createClosure(t.test),i=this.createClosure(t.consequent),s=t.alternate?this.createClosure(t.alternate):
/*!important*/()=>qt;return()=>e()?i():s()}conditionalExpressionHandler(t){return this.ifStatementHandler(t)}forStatementHandler(t){let e=se,i=t.test?this.createClosure(t.test):()=>!0,s=se;const r=this.createClosure(t.body);return"ForStatement"===t.type&&(e=t.init?this.createClosure(t.init):e,s=t.update?this.createClosure(t.update):se),n=>{let a,o=qt,h="DoWhileStatement"===t.type;for(n&&"LabeledStatement"===n.type&&(a=n.label.name),e();h||i();s()){h=!1;const t=this.setValue(r());if(t!==qt&&t!==jt){if(t===Ht)break;if(o=t,o instanceof ee&&o.value===a)o=qt;else if(o instanceof Jt||o instanceof te||o instanceof ee)break}}return o}}whileStatementHandler(t){return this.forStatementHandler(t)}doWhileStatementHandler(t){return this.forStatementHandler(t)}forInStatementHandler(t){let e=t.left;const i=this.createClosure(t.right),s=this.createClosure(t.body);return"VariableDeclaration"===t.left.type&&(this.createClosure(t.left)(),e=t.left.declarations[0].id),t=>{let r,n,a=qt;t&&"LabeledStatement"===t.type&&(r=t.label.name);const o=i();for(n in o){this.assignmentExpressionHandler({type:"AssignmentExpression",operator:"=",left:e,right:{type:"Literal",value:n}})();const t=this.setValue(s());if(t!==qt&&t!==jt){if(t===Ht)break;if(a=t,a instanceof ee&&a.value===r)a=qt;else if(a instanceof Jt||a instanceof te||a instanceof ee)break}}return a}}withStatementHandler(t){const e=this.createClosure(t.object),i=this.createClosure(t.body);return()=>{const t=e(),s=this.getCurrentScope(),r=new ie(t,s,Wt);this.setCurrentScope(r);const n=this.setValue(i());return this.setCurrentScope(s),n}}throwStatementHandler(t){const e=this.createClosure(t.argument);return()=>{throw this.setValue(void 0),e()}}tryStatementHandler(t){const e=this.createClosure(t.block),i=t.handler?this.catchClauseHandler(t.handler):null,s=t.finalizer?this.createClosure(t.finalizer):null;return()=>{const t=this.getCurrentScope(),r=this.getCurrentContext(),n=t.labelStack.concat([]),a=this.callStack.concat([]);let o,h,c=qt;const p=()=>{this.setCurrentScope(t),this.setCurrentContext(r),t.labelStack=n,this.callStack=a};try{c=this.setValue(e()),c instanceof Jt&&(o=c)}catch(l){if(p(),this.isInterruptThrow(l))throw l;if(i)try{c=this.setValue(i(l)),c instanceof Jt&&(o=c)}catch(u){if(p(),this.isInterruptThrow(u))throw u;h=u}}if(s)try{c=s(),c instanceof Jt&&(o=c)}catch(l){if(p(),this.isInterruptThrow(l))throw l;h=l}if(h)throw h;return o||c}}catchClauseHandler(t){const e=this.createParamNameGetter(t.param),i=this.createClosure(t.body);return t=>{let s;const r=this.getCurrentScope().data,n=e(),a=Ut.call(r,n),o=r[n];return r[n]=t,s=i(),a?r[n]=o:delete r[n],s}}continueStatementHandler(t){return()=>t.label?new ee(t.label.name):jt}breakStatementHandler(t){return()=>t.label?new te(t.label.name):Ht}switchStatementHandler(t){const e=this.createClosure(t.discriminant),i=t.cases.map((t=>this.switchCaseHandler(t)));return()=>{const t=e();let s,r,n=!1,a=0,o=!1;for(let e=0;e<2;e++){for(let e=a;e<i.length;e++){const h=i[e](),c=h.testClosure();if(o||c!==Gt||(o=!0,a=e),n||c===t){if(n=!0,r=this.setValue(h.bodyClosure()),r===qt)continue;if(r===Ht)break;if(s=r,s instanceof Jt||s instanceof te||s instanceof ee||s===jt)break}}if(n||!o)break;n=!0}return s}}switchCaseHandler(t){const e=t.test?this.createClosure(t.test):()=>Gt,i=this.createClosure({type:"BlockStatement",body:t.consequent});return()=>({testClosure:e,bodyClosure:i})}labeledStatementHandler(t){const e=t.label.name,i=this.createClosure(t.body);return()=>{let s;const r=this.getCurrentScope();return r.labelStack.push(e),s=i(t),s instanceof te&&s.value===e&&(s=qt),r.labelStack.pop(),s}}debuggerStatementHandler(t){return()=>qt}createParamNameGetter(t){if("Identifier"===t.type)return()=>t.name;throw this.createInternalThrowError(Mt.ParamTypeSyntaxError,t.type,t)}createObjectKeyGetter(t){let e;return e="Identifier"===t.type?()=>t.name:this.createClosure(t),function(){return e()}}createMemberKeyGetter(t){return t.computed?this.createClosure(t.property):this.createObjectKeyGetter(t.property)}createObjectGetter(t){switch(t.type){case"Identifier":return()=>this.getScopeDataFromName(t.name,this.getCurrentScope());case"MemberExpression":return this.createClosure(t.object);default:throw this.createInternalThrowError(Mt.AssignmentTypeSyntaxError,t.type,t)}}createNameGetter(t){switch(t.type){case"Identifier":return()=>t.name;case"MemberExpression":return this.createMemberKeyGetter(t);default:throw this.createInternalThrowError(Mt.AssignmentTypeSyntaxError,t.type,t)}}varDeclaration(t){this.collectDeclVars[t]=void 0}funcDeclaration(t,e){this.collectDeclFuncs[t]=e}addDeclarationsToScope(t,e,i){const s=i.data;for(let r in e){const t=e[r];s[r]=t?t():t}for(let r in t)r in s||(s[r]=void 0)}getScopeValue(t,e){return this.getScopeFromName(t,e).data[t]}getScopeDataFromName(t,e){return this.getScopeFromName(t,e).data}getScopeFromName(t,e){let i=e;do{if(t in i.data)return i}while(i=i.parent);return this.globalScope}setValue(t){const e=this.callStack.length;return this.isVarDeclMode||e||t===qt||t===Ht||t===jt||t instanceof te||t instanceof ee||(this.value=t instanceof Jt?t.value:t),t}getValue(){return this.value}}async function ae(t){const e=new ne(window,{timeout:1e5});console.time("myFunction"),console.timeEnd("myFunction"),e.evaluate(t)}return ne.version="1.4.8",ne.eval=Yt,ne.Function=Zt,ne.ecmaVersion=5,ne.globalContextInFunction=void 0,ne.global=Object.create(null),window.dlEvalCore=ae,ae}();
