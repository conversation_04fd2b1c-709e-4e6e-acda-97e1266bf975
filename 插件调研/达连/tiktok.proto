syntax = "proto3";

package tiktok;


message Response {
  int32 cmd = 1;
  int64 sequenceId = 2;
  int32 statusCode = 3;
  string errorDesc = 4;
  uint64 inboxType = 5;
  ResponseBody body = 6;
  string logId = 7;
  map<string, string> headers = 8;
  int64 startTimeStamp = 9;
  int64 requestArrivedTime = 10;
  int64 serverExecutionEndTime = 11;
  int32 retryCount = 12;
  int64 serverStartTime = 13;
}
message ResponseBody {
  SendMessageResponseBody send_message_body = 100;
  MessagesPerUserResponseBody messages_per_user_body = 200;
  MessagesPerUserInitV2ResponseBody messages_per_user_init_v2_body = 203;
  MessagesInConversationResponseBody messages_in_conversation_body = 301;
  CreateConversationV2ResponseBody create_conversation_v2_body = 609;
  GetConversationParticipantsReadIndexV3ResponseBody participants_read_index_body = 2000;
  GetConversationParticipantsMinIndexV3ResponseBody participants_min_index_body = 2001;
}

message MessagesPerUserInitV2ResponseBody {
  repeated MessageBody messages = 1;
  repeated ConversationInfoV2 conversations = 2;
  uint64 per_user_cursor = 3;
  uint64 next_cursor = 4;
  bool has_more = 5;
  int32 init_type = 6;
  uint64 cmd_start_index = 7;
  uint64 next_conversation_version = 8;
}

message MessageBody {
  string conversation_id = 1;
  int32 conversation_type = 2;
  uint64 server_message_id = 3;
  uint64 index_in_conversation = 4;
  uint64 conversation_short_id = 5;
  int32 message_type = 6;
  uint64 sender = 7;
  string content = 8;
  map<string, string> ext = 9;
  uint64 create_time = 10;
  uint64 version = 11;
  int32 status = 12;
  uint64 order_in_conversation = 13;
  string sec_sender = 14;
  map<string, PropertyItemList> property_list = 15;
  map<string, string> user_profile = 16;
  uint64 index_in_conversation_v2 = 17;
  ReferenceInfo reference_info = 18;
  uint64 index_in_conversation_v1 = 19;
  bytes content_pb = 20;
  string scene = 21;
  int32 conv_rank_update_rule = 22;
}

message PropertyItemList {
  repeated PropertyItem Items = 1;
}

message PropertyItem {
  int64 uid = 1;
  string sec_uid = 2;
  int64 create_time = 3;
  string idempotent_id = 4;
  string value = 5;
  int64 create_time_micro = 6;
}

message ReferenceInfo {
  int64 referenced_message_id = 1;
  string hint = 2;
  int64 ref_message_type = 3;
  int32 referenced_message_status = 4;
  int64 root_message_id = 5;
  int64 root_message_conv_index = 6;
  int64 sender = 7;
}

message ConversationInfoV2 {
  string conversation_id = 1;
  int64 conversation_short_id = 2;
  int32 conversation_type = 3;
  string ticket = 4;
  ParticipantsPage first_page_participants = 6;
  int32 participants_count = 7;
  bool is_participant = 8;
  int32 inbox_type = 9;
  int32 badge_count = 10;
  int32 badge_count_v2 = 11;
  Participant user_info = 20;
  ConversationCoreInfo conversation_core_info = 50;
  ConversationSettingInfo conversation_setting_info = 51;
}

message ParticipantsPage {
  repeated Participant participants = 1;
  bool has_more = 2;
  int64 cursor = 3;
}

message ConversationSettingInfo {
  string conversation_id = 1;
  int64 conversation_short_id = 2;
  int32 conversation_type = 3;
  int64 min_index = 4;
  int64 read_index = 5;
  int32 mute = 6;
  int32 stick_on_top = 7;
  int32 inbox_type = 8;
  map<string, string> ext = 9;
  int64 setting_version = 10;
  int32 favorite = 11;
  int64 set_top_time = 12;
  int64 set_favorite_time = 13;
  int64 read_index_v2 = 14;
  int64 min_index_v2 = 15;
  int32 read_badge_count = 16;
  int32 read_badge_count_v2 = 17;
}

message SendMessageResponseBody {
  int64 server_message_id = 1;
  string extra_info = 2;
  int32 status = 3;
  string client_message_id = 4;
  int64 check_code = 5;
  string check_message = 6;
  string filtered_content = 7;
  bool is_async_send = 8;
  string new_ticket = 9;
  ConversationInfoV2 conversation = 10;
  int32 inboxPageCategory = 12;
  int32 filter_reason = 13;
}

message Request {
  int32 cmd = 1;
  int64 sequence_id = 2;
  string sdk_version = 3;
  string token = 4;
  int32 refer = 5;
  int32 inbox_type = 6;
  string build_number = 7;
  RequestBody body = 8;
  string device_id = 9;
  string channel = 10;
  string device_platform = 11;
  string device_type = 12;
  string os_version = 13;
  string version_code = 14;
  map<string, string> headers = 15;
  uint32 config_id = 16;
  TokenInfo token_info = 17;
  uint32 auth_type = 18;
  MsgTrace msg_trace = 19;
  uint32 retry_count = 20;
}

message RequestBody {
  SendMessageRequestBody send_message_body = 100;
  MessagesPerUserRequestBody messages_per_user_body = 200;
  MessagesPerUserInitV2RequestBody messages_per_user_init_v2_body = 203;
  MessagesInConversationRequestBody messages_in_conversation_body = 301;
  MarkConversationReadRequestBody mark_conversation_read_body = 604;
  CreateConversationV2RequestBody create_conversation_v2_body = 609;
  SetGroupInfoRequestBody set_group_info_body = 802;
  GetConversationParticipantsReadIndexV3RequestBody participants_read_index_body = 2000;
  GetConversationParticipantsMinIndexV3RequestBody participants_min_index_body = 2001;
}

message MessagesPerUserRequestBody {
  uint64 cursor = 1;
  int32 limit = 2;
  uint64 interval = 3;
  int32 new_user = 4;
}

message MessagesPerUserInitV2RequestBody {
  uint64 cursor = 1;
  int32 new_user = 2;
  int32 init_sub_type = 3;
}

message Frame {
  uint64 seqid = 1;
  uint64 logid = 2;
  int32 service = 3;
  int32 method = 4;
  repeated HeadersList headers = 5;
  string payload_encoding = 6;
  string payload_type = 7;
  bytes payload = 8;
}

message TokenInfo {
  int32 mark_id = 1;
  int32 type = 2;
  int32 app_id = 3;
  int64 user_id = 4;
  int64 timestamp = 5;
}

message ReferencedMessageInfo {
  int64 referenced_message_id = 1;
  string hint = 2;
  int64 root_message_id = 3;
  int64 root_message_conv_index = 4;
}

message MsgTrace {
  repeated emptyObject metrics = 1;
  int32 path = 2;
}

message SendMessageRequestBody {
  string conversation_id = 1;
  int32 conversation_type = 2;
  int64 conversation_short_id = 3;
  string content = 4;
  map<string, string> ext = 5;
  int32 message_type = 6;
  string ticket = 7;
  string client_message_id = 8;
  repeated int64 mentioned_users = 9;
  bool ignore_badge_count = 10;
  ReferencedMessageInfo ref_msg_info = 11;
  map<string, string> client_ext = 12;
  MessageContent content_pb = 13;
  string scene = 14;
}

message Participant {
  int64 user_id = 1;
  int64 sort_order = 2;
  int32 role = 3;
  string alias = 4;
  string sec_uid = 5;
  int32 blocked = 6;
  int64 left_block_time = 7;
  map<string, string> ext = 8;
}

message ConversationCoreInfo {
  string conversation_id = 1;
  int64 conversation_short_id = 2;
  int32 conversation_type = 3;
  int64 info_version = 4;
  string name = 5;
  string desc = 6;
  string icon = 7;
  int32 inbox_type = 8;
  string notice = 9;
  map<string, string> ext = 11;
  int64 owner = 12;
  string sec_owner = 13;
  int32 block_status = 14;
  bool block_normal_only = 15;
  int32 mode = 16;
  int64 creator_uid = 17;
}

message MessageContent {
  ImageCard image_card = 2;
  PictureCard picture_card = 4;
  VideoCard video_card = 5;
  InfoCard info_card = 6;
  StickerCard sticker_card = 7;
  DynamicCard dynamic_card = 101;
}

message SetGroupInfoRequestBody {
  string conversation_id = 1;
  int64 conversation_short_id = 2;
  int32 conversation_type = 3;
  string group_name = 5;
  string group_desc = 6;
  string group_icon = 7;
  map<string, string> ext = 8;
}

message MessagesPerUserResponseBody {
}

message ImageCard {
  ImageCardTitle title = 1;
  repeated BaseVideo videos = 2;
  LinkInfo link_info = 3;
  PreviewHint preview_hint = 4;
  BaseReq req_base = 200;
  BaseResp resp_base = 201;
}
message ImageCardTitle {
  BaseImage image = 1;
  BaseText title = 2;
  BaseText subtitle = 3;
  Button button = 4;
}
message BaseVideo {
  string video_id = 1;
  int32 video_type = 2;
  BaseImage cover = 3;
  string video_model = 4;
  LinkInfo linkInfo = 5;
  Resolution resolution = 6;
}
message LinkInfo {
  repeated string url_list = 1;
  uint32 action_type = 2;
}
message PreviewHint {
  BaseText sender_preview_text = 1;
  BaseText receiver_preview_text = 2;
  BaseText quote_preview_text = 3;
}
message BaseReq {
  QueryData query_data = 1;
}
message BaseResp {
  string ttl = 1;
  repeated string context_menu = 2;
  int64 min_version = 3;
  map<string, string> extra = 4;
}
message BaseImage {
  string image_id = 1;
  repeated string url_list = 2;
  string display_name = 4;
  Resolution resolution = 5;
  string decrypt_key = 6;
  int32 fallback_icon_type = 7;
}
message BaseText {
  string text = 1;
}
message Button {
  BaseText text = 1;
  ButtonStyle style = 2;
  LinkInfo link_info = 4;
}
message Resolution {
  int32 width = 1;
  int32 height = 2;
}
message ButtonStyle {
  int32 height = 1;
  int32 width = 2;
}
message QueryData {
  string resource_id = 1;
  map<string, string> extra = 2;
}
message PictureCard {
  BaseImage image = 1;
  BaseText text = 2;
  LinkInfo link_info = 3;
}
message VideoCard {
  BaseVideo video = 1;
  PreviewHint preview_hint = 2;
  LinkInfo link_info = 3;
  VideoCardFallbackInfo fallback = 4;
  BaseReq req_base = 200;
  BaseResp resp_base = 201;
}
message VideoCardFallbackInfo {
  BaseImage image = 1;
  BaseText text = 2;
  LinkInfo link_info = 3;
}
message InfoCard {
  InfoCardTitle title = 1;
  InfoCardContent content = 2;
  BaseVideo video = 3;
  repeated InfoCardButton buttons = 4;
  LinkInfo link_info = 6;
  BaseReq req_base = 200;
  BaseResp resp_base = 201;
}
message InfoCardTitle {
  BaseText title = 1;
  BaseImage image = 2;
}
message InfoCardContent {
  BaseText description = 1;
  repeated BaseText contents = 2;
}
message InfoCardButton {
  BaseText text = 1;
  BaseText hint = 2;
  LinkInfo link = 3;
  int32 type = 4;
}
message StickerCard {
  BaseImage sticker = 1;
  PreviewHint preview_hint = 2;
  StickerCardFallbackInfo fallback = 3;
  UserInfo sticker_creator_user_info = 4;
  BaseReq req_base = 200;
  BaseResp resp_base = 201;
}
message StickerCardFallbackInfo {
  BaseImage placeholder = 1;
}
message UserInfo {
  int64 user_id = 1;
  string nick_name = 2;
  BaseImage avatar_thumb = 3;
}

message DynamicCard {
  DynamicInfo dynamic_info = 1;
  FallbackInfo fallback_info = 10;
  PreviewHint preview_hint = 11;
  BaseReq req_base = 200;
  BaseResp resp_base = 201;
}
message DynamicInfo {
  string card_key = 1;
  string schema = 2;
  string card_template = 3;
  string business_type = 4;
  string business_id = 5;
  int32 default_height = 6;
  int32 default_width = 7;
  int32 ui_location_type = 8;
  string raw_data = 9;
  string ab_key = 20;
  map<string, DynamicInfo> ab_dynamic_infos = 21;
}

message HeadersList {
  string key = 1;
  string value = 2;
}

message FallbackInfo {
  BaseImage image = 1;
  BaseText text = 2;
  LinkInfo link_info = 3;
}

message sendBoby {
}

message emptyObject {
}

message MarkConversationReadRequestBody {
  string conversation_id = 1;
  int64 conversation_short_id = 2;
  int32 conversation_type = 3;
  int64 read_message_index = 4;
  int64 conv_unread_count = 5;
  int64 total_unread_count = 6;
  int64 read_message_index_v2 = 7;
  int32 read_badge_count = 8;
  string ticket = 9;
  int64 server_message_id = 10;
  int32 read_badge_count_v2 = 11;
}

message GetConversationParticipantsReadIndexV3RequestBody {
  int64 conversation_short_id = 1;
  int32 conversation_type = 2;
  string conversation_id = 3;
}

message ParticipantReadIndex {
  int64 user_id = 1;
  string sec_uid = 2;
  int64 index = 3;
  int64 index_v2 = 4;
  int64 index_min = 5;
}

message GetConversationParticipantsReadIndexV3ResponseBody {
  repeated ParticipantReadIndex indexes = 1;
}

message GetConversationParticipantsMinIndexV3RequestBody {
  int64 conversation_short_id = 1;
  int32 conversation_type = 2;
  string conversation_id = 3;
}

message GetConversationParticipantsMinIndexV3ResponseBody {
  repeated ParticipantMinIndex indexes = 1;
}

message  ParticipantMinIndex{
  int64 user_id = 1;
  string sec_uid = 2;
  int64 index = 3;
  int64 index_v2 = 4;
  int64 index_min = 5;
}

message MessagesInConversationResponseBody {
  repeated MessageBody messages = 1;
  uint64 next_cursor = 2;
  bool has_more = 3;
}

message MessagesInConversationRequestBody {
  string conversation_id = 1;
  int32 conversation_type = 2;
  int64 conversation_short_id = 3;
  int32 direction = 4;
  int64 anchor_index = 5;
  int32 limit = 6;
}

message CreateConversationV2ResponseBody {
  ConversationInfoV2 conversation = 1;
  int64 check_code = 2;
  string check_message = 3;
  string extra_info = 4;
  int32 status = 5;
}

message CreateConversationV2RequestBody {
    int32 conversation_type = 1;
    repeated int64 participants = 2;
    bool persistent = 3;
    string idempotent_id = 4;
    string name = 6;
    string avatar_url = 7;
    string description = 8;
    map<string, string> biz_ext = 11;
}