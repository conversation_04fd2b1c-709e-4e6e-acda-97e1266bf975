import{l as Fr}from"./lodash.a9edce51.js";import"./_commonjsHelpers.b8add541.js";const Mr="https://www.easylinktiktok.com",U=(r,e,t="POST",n={})=>new Promise((s,a)=>{chrome.storage.sync.get(["authorization"]).then(i=>{let o=Fr.cloneDeep(e);fetch(`${Mr}${r}`,{method:t,headers:{accept:"application/json","accept-language":"zh-CN,zh;q=0.9","content-type":"application/json",authorization:i.authorization?`Bearer ${i.authorization}`:"",...n},referrerPolicy:"strict-origin-when-cross-origin",...t!=="GET"?{body:JSON.stringify(o)}:{},mode:"cors",credentials:"include"}).then(l=>l.json()).then(l=>{l.code,s(l)}).catch(l=>{console.error(l)})})}),Nr=(r,e,t="POST",n={})=>new Promise((s,a)=>{fetch("https://easylinktiktok.com/verification/detectSlideImg",{method:"POST",body:e}).then(i=>i.json()).then(i=>{i.code,s(i)}).catch(i=>{console.error(i)})});function Br(r){return U("/user/login",r)}function Dr(r){return U("/user/register",r)}function Lr(r){return U(`/api/template/invitation/search?shopId=${r.shopId}&shopCountry=${r.shopCountry}`,r,"GET")}function Ur(r){return U("/api/template/invitation/new",r,"POST")}function jr(r){return U(`/api/template/invitation/${r} `,{},"DELETE")}function Wr(r){return U(`/api/template/message/search?shopId=${r.shopId}&shopCountry=${r.shopCountry}`,r,"GET")}function Jr(r){return U("/api/template/message/new",r,"POST")}function qr(r){return U(`/api/template/message/${r}`,{},"DELETE")}function Xr(r){return U("/api/bindings/new",r,"POST")}function Hr(r){return U("/api/bindings/all",{},"GET")}function Vr(r){return U("/api/user/invitationCode/invitees",{},"GET")}function Gr(r){return U("/api/user/invitationCode/update",r,"PUT")}function Kr(r){return U("/api/user/relationship/childUsers",r,"GET")}function Qr(r){return U(`/api/user/relationship/updatePassword/${r.id}`,{password:r.password},"PUT")}function zr(r){return U(`/api/user/relationship/delete/${r}`,{},"DELETE")}function Yr(r){return Nr("/verification/detectSlideImg",r,"POST")}function Zr(r){return U("/api/bindings/new",r,"POST")}function es(r){return U("/api/bindings/all",r,"GET")}async function ts(r){try{const e=await fetch(r);if(!e.ok)throw new Error(`Failed to fetch image: ${e.statusText}`);const t=await e.blob();let n="img.jpg";const s=r==null?void 0:r.split(".").pop().split("?")[0];return s&&/^(jpg|jpeg|png|gif|webp|bmp)$/i.test(s)&&(n=`image.${s}`),new File([t],n,{type:t.type||"image/jpeg",lastModified:new Date().getTime()})}catch(e){throw console.error("Error downloading image:",e),e}}const At="RFC3986",Ct={RFC1738:r=>String(r).replace(/%20/g,"+"),RFC3986:r=>String(r)},ns="RFC1738",rs=Array.isArray,te=(()=>{const r=[];for(let e=0;e<256;++e)r.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return r})(),ft=1024,ss=(r,e,t,n,s)=>{if(r.length===0)return r;let a=r;if(typeof r=="symbol"?a=Symbol.prototype.toString.call(r):typeof r!="string"&&(a=String(r)),t==="iso-8859-1")return escape(a).replace(/%u[0-9a-f]{4}/gi,function(o){return"%26%23"+parseInt(o.slice(2),16)+"%3B"});let i="";for(let o=0;o<a.length;o+=ft){const l=a.length>=ft?a.slice(o,o+ft):a,c=[];for(let h=0;h<l.length;++h){let u=l.charCodeAt(h);if(u===45||u===46||u===95||u===126||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||s===ns&&(u===40||u===41)){c[c.length]=l.charAt(h);continue}if(u<128){c[c.length]=te[u];continue}if(u<2048){c[c.length]=te[192|u>>6]+te[128|u&63];continue}if(u<55296||u>=57344){c[c.length]=te[224|u>>12]+te[128|u>>6&63]+te[128|u&63];continue}h+=1,u=65536+((u&1023)<<10|l.charCodeAt(h)&1023),c[c.length]=te[240|u>>18]+te[128|u>>12&63]+te[128|u>>6&63]+te[128|u&63]}i+=c.join("")}return i};function as(r){return!r||typeof r!="object"?!1:!!(r.constructor&&r.constructor.isBuffer&&r.constructor.isBuffer(r))}function sn(r,e){if(rs(r)){const t=[];for(let n=0;n<r.length;n+=1)t.push(e(r[n]));return t}return e(r)}const is=Object.prototype.hasOwnProperty,xn={brackets(r){return String(r)+"[]"},comma:"comma",indices(r,e){return String(r)+"["+e+"]"},repeat(r){return String(r)}},ne=Array.isArray,os=Array.prototype.push,Pn=function(r,e){os.apply(r,ne(e)?e:[e])},ls=Date.prototype.toISOString,N={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:ss,encodeValuesOnly:!1,format:At,formatter:Ct[At],indices:!1,serializeDate(r){return ls.call(r)},skipNulls:!1,strictNullHandling:!1};function cs(r){return typeof r=="string"||typeof r=="number"||typeof r=="boolean"||typeof r=="symbol"||typeof r=="bigint"}const dt={};function En(r,e,t,n,s,a,i,o,l,c,h,u,f,p,_,x,m,$){let g=r,P=$,F=0,w=!1;for(;(P=P.get(dt))!==void 0&&!w;){const C=P.get(r);if(F+=1,typeof C<"u"){if(C===F)throw new RangeError("Cyclic object value");w=!0}typeof P.get(dt)>"u"&&(F=0)}if(typeof c=="function"?g=c(e,g):g instanceof Date?g=f==null?void 0:f(g):t==="comma"&&ne(g)&&(g=sn(g,function(C){return C instanceof Date?f==null?void 0:f(C):C})),g===null){if(a)return l&&!x?l(e,N.encoder,m,"key",p):e;g=""}if(cs(g)||as(g)){if(l){const C=x?e:l(e,N.encoder,m,"key",p);return[(_==null?void 0:_(C))+"="+(_==null?void 0:_(l(g,N.encoder,m,"value",p)))]}return[(_==null?void 0:_(e))+"="+(_==null?void 0:_(String(g)))]}const T=[];if(typeof g>"u")return T;let I;if(t==="comma"&&ne(g))x&&l&&(g=sn(g,l)),I=[{value:g.length>0?g.join(",")||null:void 0}];else if(ne(c))I=c;else{const C=Object.keys(g);I=h?C.sort(h):C}const R=o?String(e).replace(/\./g,"%2E"):String(e),S=n&&ne(g)&&g.length===1?R+"[]":R;if(s&&ne(g)&&g.length===0)return S+"[]";for(let C=0;C<I.length;++C){const O=I[C],E=typeof O=="object"&&typeof O.value<"u"?O.value:g[O];if(i&&E===null)continue;const K=u&&o?O.replace(/\./g,"%2E"):O,oe=ne(g)?typeof t=="function"?t(S,K):S:S+(u?"."+K:"["+K+"]");$.set(r,F);const q=new WeakMap;q.set(dt,$),Pn(T,En(E,oe,t,n,s,a,i,o,t==="comma"&&x&&ne(g)?null:l,c,h,u,f,p,_,x,m,q))}return T}function us(r=N){if(typeof r.allowEmptyArrays<"u"&&typeof r.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof r.encodeDotInKeys<"u"&&typeof r.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(r.encoder!==null&&typeof r.encoder<"u"&&typeof r.encoder!="function")throw new TypeError("Encoder has to be a function.");const e=r.charset||N.charset;if(typeof r.charset<"u"&&r.charset!=="utf-8"&&r.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=At;if(typeof r.format<"u"){if(!is.call(Ct,r.format))throw new TypeError("Unknown format option provided.");t=r.format}const n=Ct[t];let s=N.filter;(typeof r.filter=="function"||ne(r.filter))&&(s=r.filter);let a;if(r.arrayFormat&&r.arrayFormat in xn?a=r.arrayFormat:"indices"in r?a=r.indices?"indices":"repeat":a=N.arrayFormat,"commaRoundTrip"in r&&typeof r.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const i=typeof r.allowDots>"u"?r.encodeDotInKeys?!0:N.allowDots:!!r.allowDots;return{addQueryPrefix:typeof r.addQueryPrefix=="boolean"?r.addQueryPrefix:N.addQueryPrefix,allowDots:i,allowEmptyArrays:typeof r.allowEmptyArrays=="boolean"?!!r.allowEmptyArrays:N.allowEmptyArrays,arrayFormat:a,charset:e,charsetSentinel:typeof r.charsetSentinel=="boolean"?r.charsetSentinel:N.charsetSentinel,commaRoundTrip:!!r.commaRoundTrip,delimiter:typeof r.delimiter>"u"?N.delimiter:r.delimiter,encode:typeof r.encode=="boolean"?r.encode:N.encode,encodeDotInKeys:typeof r.encodeDotInKeys=="boolean"?r.encodeDotInKeys:N.encodeDotInKeys,encoder:typeof r.encoder=="function"?r.encoder:N.encoder,encodeValuesOnly:typeof r.encodeValuesOnly=="boolean"?r.encodeValuesOnly:N.encodeValuesOnly,filter:s,format:t,formatter:n,serializeDate:typeof r.serializeDate=="function"?r.serializeDate:N.serializeDate,skipNulls:typeof r.skipNulls=="boolean"?r.skipNulls:N.skipNulls,sort:typeof r.sort=="function"?r.sort:null,strictNullHandling:typeof r.strictNullHandling=="boolean"?r.strictNullHandling:N.strictNullHandling}}function hs(r,e={}){let t=r;const n=us(e);let s,a;typeof n.filter=="function"?(a=n.filter,t=a("",t)):ne(n.filter)&&(a=n.filter,s=a);const i=[];if(typeof t!="object"||t===null)return"";const o=xn[n.arrayFormat],l=o==="comma"&&n.commaRoundTrip;s||(s=Object.keys(t)),n.sort&&s.sort(n.sort);const c=new WeakMap;for(let f=0;f<s.length;++f){const p=s[f];n.skipNulls&&t[p]===null||Pn(i,En(t[p],p,o,l,n.allowEmptyArrays,n.strictNullHandling,n.skipNulls,n.encodeDotInKeys,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,c))}const h=i.join(n.delimiter);let u=n.addQueryPrefix===!0?"?":"";return n.charsetSentinel&&(n.charset==="iso-8859-1"?u+="utf8=%26%2310003%3B&":u+="utf8=%E2%9C%93&"),h.length>0?u+h:""}const pe="4.85.3";let an=!1,Oe,vn,Rn,xt,In,Tn,On,$n,kn;function fs(r,e={auto:!1}){if(an)throw new Error(`you must \`import 'openai/shims/${r.kind}'\` before importing anything else from openai`);if(Oe)throw new Error(`can't \`import 'openai/shims/${r.kind}'\` after \`import 'openai/shims/${Oe}'\``);an=e.auto,Oe=r.kind,vn=r.fetch,r.Request,r.Response,r.Headers,Rn=r.FormData,r.Blob,xt=r.File,In=r.ReadableStream,Tn=r.getMultipartRequestOptions,On=r.getDefaultAgent,$n=r.fileFromPath,kn=r.isFsReadStream}class ds{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}function ms({manuallyImported:r}={}){const e=r?"You may need to use polyfills":"Add one of these imports before your first `import \u2026 from 'openai'`:\n- `import 'openai/shims/node'` (if you're running on Node)\n- `import 'openai/shims/web'` (otherwise)\n";let t,n,s,a;try{t=fetch,n=Request,s=Response,a=Headers}catch(i){throw new Error(`this environment is missing the following Web Fetch API type: ${i.message}. ${e}`)}return{kind:"web",fetch:t,Request:n,Response:s,Headers:a,FormData:typeof FormData<"u"?FormData:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'FormData' is undefined. ${e}`)}},Blob:typeof Blob<"u"?Blob:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'Blob' is undefined. ${e}`)}},File:typeof File<"u"?File:class{constructor(){throw new Error(`file uploads aren't supported in this environment yet as 'File' is undefined. ${e}`)}},ReadableStream:typeof ReadableStream<"u"?ReadableStream:class{constructor(){throw new Error(`streaming isn't supported in this environment yet as 'ReadableStream' is undefined. ${e}`)}},getMultipartRequestOptions:async(i,o)=>({...o,body:new ds(i)}),getDefaultAgent:i=>{},fileFromPath:()=>{throw new Error("The `fileFromPath` function is only supported in Node. See the README for more details: https://www.github.com/openai/openai-node#file-uploads")},isFsReadStream:i=>!1}}Oe||fs(ms(),{auto:!0});class y extends Error{}class L extends y{constructor(e,t,n,s){super(`${L.makeMessage(e,t,n)}`),this.status=e,this.headers=s,this.request_id=s==null?void 0:s["x-request-id"],this.error=t;const a=t;this.code=a==null?void 0:a.code,this.param=a==null?void 0:a.param,this.type=a==null?void 0:a.type}static makeMessage(e,t,n){const s=t!=null&&t.message?typeof t.message=="string"?t.message:JSON.stringify(t.message):t?JSON.stringify(t):n;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,t,n,s){if(!e||!s)return new st({message:n,cause:Et(t)});const a=t==null?void 0:t.error;return e===400?new Fn(e,a,n,s):e===401?new Mn(e,a,n,s):e===403?new Nn(e,a,n,s):e===404?new Bn(e,a,n,s):e===409?new Dn(e,a,n,s):e===422?new Ln(e,a,n,s):e===429?new Un(e,a,n,s):e>=500?new jn(e,a,n,s):new L(e,a,n,s)}}class Y extends L{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class st extends L{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),t&&(this.cause=t)}}class Mt extends st{constructor({message:e}={}){super({message:e!=null?e:"Request timed out."})}}class Fn extends L{}class Mn extends L{}class Nn extends L{}class Bn extends L{}class Dn extends L{}class Ln extends L{}class Un extends L{}class jn extends L{}class Wn extends y{constructor(){super("Could not parse response content as the length limit was reached")}}class Jn extends y{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}var Ue=globalThis&&globalThis.__classPrivateFieldSet||function(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t},ue=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},H;class at{constructor(){H.set(this,void 0),this.buffer=new Uint8Array,Ue(this,H,null,"f")}decode(e){if(e==null)return[];const t=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?new TextEncoder().encode(e):e;let n=new Uint8Array(this.buffer.length+t.length);n.set(this.buffer),n.set(t,this.buffer.length),this.buffer=n;const s=[];let a;for(;(a=ps(this.buffer,ue(this,H,"f")))!=null;){if(a.carriage&&ue(this,H,"f")==null){Ue(this,H,a.index,"f");continue}if(ue(this,H,"f")!=null&&(a.index!==ue(this,H,"f")+1||a.carriage)){s.push(this.decodeText(this.buffer.slice(0,ue(this,H,"f")-1))),this.buffer=this.buffer.slice(ue(this,H,"f")),Ue(this,H,null,"f");continue}const i=ue(this,H,"f")!==null?a.preceding-1:a.preceding,o=this.decodeText(this.buffer.slice(0,i));s.push(o),this.buffer=this.buffer.slice(a.index),Ue(this,H,null,"f")}return s}decodeText(e){var t;if(e==null)return"";if(typeof e=="string")return e;if(typeof Buffer<"u"){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new y(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if(typeof TextDecoder<"u"){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return(t=this.textDecoder)!=null||(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new y(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new y("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){return this.buffer.length?this.decode(`
`):[]}}H=new WeakMap;at.NEWLINE_CHARS=new Set([`
`,"\r"]);at.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function ps(r,e){for(let s=e!=null?e:0;s<r.length;s++){if(r[s]===10)return{preceding:s,index:s+1,carriage:!1};if(r[s]===13)return{preceding:s,index:s+1,carriage:!0}}return null}function gs(r){for(let n=0;n<r.length-1;n++){if(r[n]===10&&r[n+1]===10||r[n]===13&&r[n+1]===13)return n+2;if(r[n]===13&&r[n+1]===10&&n+3<r.length&&r[n+2]===13&&r[n+3]===10)return n+4}return-1}function qn(r){if(r[Symbol.asyncIterator])return r;const e=r.getReader();return{async next(){try{const t=await e.read();return t!=null&&t.done&&e.releaseLock(),t}catch(t){throw e.releaseLock(),t}},async return(){const t=e.cancel();return e.releaseLock(),await t,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}class se{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let n=!1;async function*s(){if(n)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let a=!1;try{for await(const i of _s(e,t))if(!a){if(i.data.startsWith("[DONE]")){a=!0;continue}if(i.event===null){let o;try{o=JSON.parse(i.data)}catch(l){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),l}if(o&&o.error)throw new L(void 0,o.error,void 0,void 0);yield o}else{let o;try{o=JSON.parse(i.data)}catch(l){throw console.error("Could not parse message into JSON:",i.data),console.error("From chunk:",i.raw),l}if(i.event=="error")throw new L(void 0,o.error,o.message,void 0);yield{event:i.event,data:o}}}a=!0}catch(i){if(i instanceof Error&&i.name==="AbortError")return;throw i}finally{a||t.abort()}}return new se(s,t)}static fromReadableStream(e,t){let n=!1;async function*s(){const i=new at,o=qn(e);for await(const l of o)for(const c of i.decode(l))yield c;for(const l of i.flush())yield l}async function*a(){if(n)throw new Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");n=!0;let i=!1;try{for await(const o of s())i||o&&(yield JSON.parse(o));i=!0}catch(o){if(o instanceof Error&&o.name==="AbortError")return;throw o}finally{i||t.abort()}}return new se(a,t)}[Symbol.asyncIterator](){return this.iterator()}tee(){const e=[],t=[],n=this.iterator(),s=a=>({next:()=>{if(a.length===0){const i=n.next();e.push(i),t.push(i)}return a.shift()}});return[new se(()=>s(e),this.controller),new se(()=>s(t),this.controller)]}toReadableStream(){const e=this;let t;const n=new TextEncoder;return new In({async start(){t=e[Symbol.asyncIterator]()},async pull(s){try{const{value:a,done:i}=await t.next();if(i)return s.close();const o=n.encode(JSON.stringify(a)+`
`);s.enqueue(o)}catch(a){s.error(a)}},async cancel(){var s;await((s=t.return)==null?void 0:s.call(t))}})}}async function*_s(r,e){if(!r.body)throw e.abort(),new y("Attempted to iterate over a response with no body");const t=new ys,n=new at,s=qn(r.body);for await(const a of ws(s))for(const i of n.decode(a)){const o=t.decode(i);o&&(yield o)}for(const a of n.flush()){const i=t.decode(a);i&&(yield i)}}async function*ws(r){let e=new Uint8Array;for await(const t of r){if(t==null)continue;const n=t instanceof ArrayBuffer?new Uint8Array(t):typeof t=="string"?new TextEncoder().encode(t):t;let s=new Uint8Array(e.length+n.length);s.set(e),s.set(n,e.length),e=s;let a;for(;(a=gs(e))!==-1;)yield e.slice(0,a),e=e.slice(a)}e.length>0&&(yield e)}class ys{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const a={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,n,s]=bs(e,":");return s.startsWith(" ")&&(s=s.substring(1)),t==="event"?this.event=s:t==="data"&&this.data.push(s),null}}function bs(r,e){const t=r.indexOf(e);return t!==-1?[r.substring(0,t),e,r.substring(t+e.length)]:[r,"",""]}const Xn=r=>r!=null&&typeof r=="object"&&typeof r.url=="string"&&typeof r.blob=="function",Hn=r=>r!=null&&typeof r=="object"&&typeof r.name=="string"&&typeof r.lastModified=="number"&&it(r),it=r=>r!=null&&typeof r=="object"&&typeof r.size=="number"&&typeof r.type=="string"&&typeof r.text=="function"&&typeof r.slice=="function"&&typeof r.arrayBuffer=="function",Ss=r=>Hn(r)||Xn(r)||kn(r);async function Vn(r,e,t){var s,a,i;if(r=await r,Hn(r))return r;if(Xn(r)){const o=await r.blob();e||(e=(s=new URL(r.url).pathname.split(/[\\/]/).pop())!=null?s:"unknown_file");const l=it(o)?[await o.arrayBuffer()]:[o];return new xt(l,e,t)}const n=await As(r);if(e||(e=(a=xs(r))!=null?a:"unknown_file"),!(t!=null&&t.type)){const o=(i=n[0])==null?void 0:i.type;typeof o=="string"&&(t={...t,type:o})}return new xt(n,e,t)}async function As(r){var t;let e=[];if(typeof r=="string"||ArrayBuffer.isView(r)||r instanceof ArrayBuffer)e.push(r);else if(it(r))e.push(await r.arrayBuffer());else if(Ps(r))for await(const n of r)e.push(n);else throw new Error(`Unexpected data type: ${typeof r}; constructor: ${(t=r==null?void 0:r.constructor)==null?void 0:t.name}; props: ${Cs(r)}`);return e}function Cs(r){return`[${Object.getOwnPropertyNames(r).map(t=>`"${t}"`).join(", ")}]`}function xs(r){var e;return mt(r.name)||mt(r.filename)||((e=mt(r.path))==null?void 0:e.split(/[\\/]/).pop())}const mt=r=>{if(typeof r=="string")return r;if(typeof Buffer<"u"&&r instanceof Buffer)return String(r)},Ps=r=>r!=null&&typeof r=="object"&&typeof r[Symbol.asyncIterator]=="function",on=r=>r&&typeof r=="object"&&r.body&&r[Symbol.toStringTag]==="MultipartBody",Se=async r=>{const e=await Es(r.body);return Tn(e,r)},Es=async r=>{const e=new Rn;return await Promise.all(Object.entries(r||{}).map(([t,n])=>Pt(e,t,n))),e},Pt=async(r,e,t)=>{if(t!==void 0){if(t==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof t=="string"||typeof t=="number"||typeof t=="boolean")r.append(e,String(t));else if(Ss(t)){const n=await Vn(t);r.append(e,n)}else if(Array.isArray(t))await Promise.all(t.map(n=>Pt(r,e+"[]",n)));else if(typeof t=="object")await Promise.all(Object.entries(t).map(([n,s])=>Pt(r,`${e}[${n}]`,s)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${t} instead`)}};var vs=globalThis&&globalThis.__classPrivateFieldSet||function(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t},Rs=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},je;async function Gn(r){const{response:e}=r;if(r.options.stream)return we("response",e.status,e.url,e.headers,e.body),r.options.__streamClass?r.options.__streamClass.fromSSEResponse(e,r.controller):se.fromSSEResponse(e,r.controller);if(e.status===204)return null;if(r.options.__binaryResponse)return e;const t=e.headers.get("content-type");if((t==null?void 0:t.includes("application/json"))||(t==null?void 0:t.includes("application/vnd.api+json"))){const a=await e.json();return we("response",e.status,e.url,e.headers,a),Kn(a,e)}const s=await e.text();return we("response",e.status,e.url,e.headers,s),s}function Kn(r,e){return!r||typeof r!="object"||Array.isArray(r)?r:Object.defineProperty(r,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}class ot extends Promise{constructor(e,t=Gn){super(n=>{n(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new ot(this.responsePromise,async t=>Kn(e(await this.parseResponse(t),t),t.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t,request_id:t.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class Is{constructor({baseURL:e,maxRetries:t=2,timeout:n=6e5,httpAgent:s,fetch:a}){this.baseURL=e,this.maxRetries=pt("maxRetries",t),this.timeout=pt("timeout",n),this.httpAgent=s,this.fetch=a!=null?a:vn}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...Ms(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${Ls()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,n){return this.request(Promise.resolve(n).then(async s=>{const a=s&&it(s==null?void 0:s.body)?new DataView(await s.body.arrayBuffer()):(s==null?void 0:s.body)instanceof DataView?s.body:(s==null?void 0:s.body)instanceof ArrayBuffer?new DataView(s.body):s&&ArrayBuffer.isView(s==null?void 0:s.body)?new DataView(s.body.buffer):s==null?void 0:s.body;return{method:e,path:t,...s,body:a}}))}getAPIList(e,t,n){return this.requestAPIList(t,{method:"get",path:e,...n})}calculateContentLength(e){if(typeof e=="string"){if(typeof Buffer<"u")return Buffer.byteLength(e,"utf8").toString();if(typeof TextEncoder<"u")return new TextEncoder().encode(e).length.toString()}else if(ArrayBuffer.isView(e))return e.byteLength.toString();return null}buildRequest(e,{retryCount:t=0}={}){var _,x,m,$,g,P;e={...e};const{method:n,path:s,query:a,headers:i={}}=e,o=ArrayBuffer.isView(e.body)||e.__binaryRequest&&typeof e.body=="string"?e.body:on(e.body)?e.body.body:e.body?JSON.stringify(e.body,null,2):null,l=this.calculateContentLength(o),c=this.buildURL(s,a);"timeout"in e&&pt("timeout",e.timeout),e.timeout=(_=e.timeout)!=null?_:this.timeout;const h=(m=(x=e.httpAgent)!=null?x:this.httpAgent)!=null?m:On(c),u=e.timeout+1e3;typeof(($=h==null?void 0:h.options)==null?void 0:$.timeout)=="number"&&u>((g=h.options.timeout)!=null?g:0)&&(h.options.timeout=u),this.idempotencyHeader&&n!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),i[this.idempotencyHeader]=e.idempotencyKey);const f=this.buildHeaders({options:e,headers:i,contentLength:l,retryCount:t});return{req:{method:n,...o&&{body:o},headers:f,...h&&{agent:h},signal:(P=e.signal)!=null?P:null},url:c,timeout:e.timeout}}buildHeaders({options:e,headers:t,contentLength:n,retryCount:s}){const a={};n&&(a["content-length"]=n);const i=this.defaultHeaders(e);return un(a,i),un(a,t),on(e.body)&&Oe!=="node"&&delete a["content-type"],qe(i,"x-stainless-retry-count")===void 0&&qe(t,"x-stainless-retry-count")===void 0&&(a["x-stainless-retry-count"]=String(s)),qe(i,"x-stainless-timeout")===void 0&&qe(t,"x-stainless-timeout")===void 0&&e.timeout&&(a["x-stainless-timeout"]=String(e.timeout)),this.validateHeaders(a,t),a}async prepareOptions(e){}async prepareRequest(e,{url:t,options:n}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(t=>[...t])):{...e}:{}}makeStatusError(e,t,n,s){return L.generate(e,t,n,s)}request(e,t=null){return new ot(this.makeRequest(e,t))}async makeRequest(e,t){var u,f,p;const n=await e,s=(u=n.maxRetries)!=null?u:this.maxRetries;t==null&&(t=s),await this.prepareOptions(n);const{req:a,url:i,timeout:o}=this.buildRequest(n,{retryCount:s-t});if(await this.prepareRequest(a,{url:i,options:n}),we("request",i,n,a.headers),(f=n.signal)!=null&&f.aborted)throw new Y;const l=new AbortController,c=await this.fetchWithTimeout(i,a,o,l).catch(Et);if(c instanceof Error){if((p=n.signal)!=null&&p.aborted)throw new Y;if(t)return this.retryRequest(n,t);throw c.name==="AbortError"?new Mt:new st({cause:c})}const h=Os(c.headers);if(!c.ok){if(t&&this.shouldRetry(c)){const P=`retrying, ${t} attempts remaining`;return we(`response (error; ${P})`,c.status,i,h),this.retryRequest(n,t,h)}const _=await c.text().catch(P=>Et(P).message),x=Ns(_),m=x?void 0:_;throw we(`response (error; ${t?"(error; no more retries left)":"(error; not retryable)"})`,c.status,i,h,m),this.makeStatusError(c.status,x,m,h)}return{response:c,options:n,controller:l}}requestAPIList(e,t){const n=this.makeRequest(t,null);return new Ts(this,n,e)}buildURL(e,t){const n=Ds(e)?new URL(e):new URL(this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),s=this.defaultQuery();return zn(s)||(t={...s,...t}),typeof t=="object"&&t&&!Array.isArray(t)&&(n.search=this.stringifyQuery(t)),n.toString()}stringifyQuery(e){return Object.entries(e).filter(([t,n])=>typeof n<"u").map(([t,n])=>{if(typeof n=="string"||typeof n=="number"||typeof n=="boolean")return`${encodeURIComponent(t)}=${encodeURIComponent(n)}`;if(n===null)return`${encodeURIComponent(t)}=`;throw new y(`Cannot stringify type ${typeof n}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,n,s){const{signal:a,...i}=t||{};a&&a.addEventListener("abort",()=>s.abort());const o=setTimeout(()=>s.abort(),n),l={signal:s.signal,...i};return l.method&&(l.method=l.method.toUpperCase()),this.fetch.call(void 0,e,l).finally(()=>{clearTimeout(o)})}shouldRetry(e){const t=e.headers.get("x-should-retry");return t==="true"?!0:t==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,t,n){var o;let s;const a=n==null?void 0:n["retry-after-ms"];if(a){const l=parseFloat(a);Number.isNaN(l)||(s=l)}const i=n==null?void 0:n["retry-after"];if(i&&!s){const l=parseFloat(i);Number.isNaN(l)?s=Date.parse(i)-Date.now():s=l*1e3}if(!(s&&0<=s&&s<60*1e3)){const l=(o=e.maxRetries)!=null?o:this.maxRetries;s=this.calculateDefaultRetryTimeoutMillis(t,l)}return await Me(s),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){const a=t-e,i=Math.min(.5*Math.pow(2,a),8),o=1-Math.random()*.25;return i*o*1e3}getUserAgent(){return`${this.constructor.name}/JS ${pe}`}}class Qn{constructor(e,t,n,s){je.set(this,void 0),vs(this,je,e,"f"),this.options=s,this.response=t,this.body=n}hasNextPage(){return this.getPaginatedItems().length?this.nextPageInfo()!=null:!1}async getNextPage(){const e=this.nextPageInfo();if(!e)throw new y("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");const t={...this.options};if("params"in e&&typeof t.query=="object")t.query={...t.query,...e.params};else if("url"in e){const n=[...Object.entries(t.query||{}),...e.url.searchParams.entries()];for(const[s,a]of n)e.url.searchParams.set(s,a);t.query=void 0,t.path=e.url.toString()}return await Rs(this,je,"f").requestAPIList(this.constructor,t)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(je=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const t of e.getPaginatedItems())yield t}}class Ts extends ot{constructor(e,t,n){super(t,async s=>new n(e,s.response,await Gn(s),s.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const t of e)yield t}}const Os=r=>new Proxy(Object.fromEntries(r.entries()),{get(e,t){const n=t.toString();return e[n.toLowerCase()]||e[n]}}),$s={method:!0,path:!0,query:!0,body:!0,headers:!0,maxRetries:!0,stream:!0,timeout:!0,httpAgent:!0,signal:!0,idempotencyKey:!0,__metadata:!0,__binaryRequest:!0,__binaryResponse:!0,__streamClass:!0},W=r=>typeof r=="object"&&r!==null&&!zn(r)&&Object.keys(r).every(e=>Yn($s,e)),ks=()=>{var e,t;if(typeof Deno<"u"&&Deno.build!=null)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":pe,"X-Stainless-OS":cn(Deno.build.os),"X-Stainless-Arch":ln(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:(t=(e=Deno.version)==null?void 0:e.deno)!=null?t:"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":pe,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if(Object.prototype.toString.call(typeof process<"u"?process:0)==="[object process]")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":pe,"X-Stainless-OS":cn(process.platform),"X-Stainless-Arch":ln(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};const r=Fs();return r?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":pe,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${r.browser}`,"X-Stainless-Runtime-Version":r.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":pe,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function Fs(){if(typeof navigator>"u"||!navigator)return null;const r=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:t}of r){const n=t.exec(navigator.userAgent);if(n){const s=n[1]||0,a=n[2]||0,i=n[3]||0;return{browser:e,version:`${s}.${a}.${i}`}}}return null}const ln=r=>r==="x32"?"x32":r==="x86_64"||r==="x64"?"x64":r==="arm"?"arm":r==="aarch64"||r==="arm64"?"arm64":r?`other:${r}`:"unknown",cn=r=>(r=r.toLowerCase(),r.includes("ios")?"iOS":r==="android"?"Android":r==="darwin"?"MacOS":r==="win32"?"Windows":r==="freebsd"?"FreeBSD":r==="openbsd"?"OpenBSD":r==="linux"?"Linux":r?`Other:${r}`:"Unknown");let We;const Ms=()=>We!=null?We:We=ks(),Ns=r=>{try{return JSON.parse(r)}catch{return}},Bs=/^[a-z][a-z0-9+.-]*:/i,Ds=r=>Bs.test(r),Me=r=>new Promise(e=>setTimeout(e,r)),pt=(r,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new y(`${r} must be an integer`);if(e<0)throw new y(`${r} must be a positive integer`);return e},Et=r=>{if(r instanceof Error)return r;if(typeof r=="object"&&r!==null)try{return new Error(JSON.stringify(r))}catch{}return new Error(r)},Je=r=>{var e,t,n,s,a;if(typeof process<"u")return(t=(e={VERSION:"2.7.15.1",APP_NAME:"connect",MANIFEST_NAME:"e\u5EFA\u8054EasyLink TikTok\u8FBE\u4EBA\u5EFA\u8054\u667A\u80FD\u63D2\u4EF6",VITE_APP_ORIGIN_URL:"https://www.easylinktiktok.com"}[r])==null?void 0:e.trim())!=null?t:void 0;if(typeof Deno<"u")return(a=(s=(n=Deno.env)==null?void 0:n.get)==null?void 0:s.call(n,r))==null?void 0:a.trim()};function zn(r){if(!r)return!0;for(const e in r)return!1;return!0}function Yn(r,e){return Object.prototype.hasOwnProperty.call(r,e)}function un(r,e){for(const t in e){if(!Yn(e,t))continue;const n=t.toLowerCase();if(!n)continue;const s=e[t];s===null?delete r[n]:s!==void 0&&(r[n]=s)}}const hn=new Set(["authorization","api-key"]);function we(r,...e){var t;if(typeof process<"u"&&((t=process==null?void 0:process.env)==null?void 0:t.DEBUG)==="true"){const n=e.map(s=>{if(!s)return s;if(s.headers){const i={...s,headers:{...s.headers}};for(const o in s.headers)hn.has(o.toLowerCase())&&(i.headers[o]="REDACTED");return i}let a=null;for(const i in s)hn.has(i.toLowerCase())&&(a!=null||(a={...s}),a[i]="REDACTED");return a!=null?a:s});console.log(`OpenAI:DEBUG:${r}`,...n)}}const Ls=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{const e=Math.random()*16|0;return(r==="x"?e:e&3|8).toString(16)}),Us=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u",js=r=>typeof(r==null?void 0:r.get)=="function",qe=(r,e)=>{var n;const t=e.toLowerCase();if(js(r)){const s=((n=e[0])==null?void 0:n.toUpperCase())+e.substring(1).replace(/([^\w])(\w)/g,(a,i,o)=>i+o.toUpperCase());for(const a of[e,t,e.toUpperCase(),s]){const i=r.get(a);if(i)return i}}for(const[s,a]of Object.entries(r))if(s.toLowerCase()===t)return Array.isArray(a)?(a.length<=1||console.warn(`Received ${a.length} entries for the ${e} header, using the first entry.`),a[0]):a};function gt(r){return r!=null&&typeof r=="object"&&!Array.isArray(r)}class Ws extends Qn{constructor(e,t,n,s){super(e,t,n,s),this.data=n.data||[],this.object=n.object}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}nextPageParams(){return null}nextPageInfo(){return null}}class G extends Qn{constructor(e,t,n,s){super(e,t,n,s),this.data=n.data||[],this.has_more=n.has_more||!1}getPaginatedItems(){var e;return(e=this.data)!=null?e:[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageParams(){const e=this.nextPageInfo();if(!e)return null;if("params"in e)return e.params;const t=Object.fromEntries(e.url.searchParams);return Object.keys(t).length?t:null}nextPageInfo(){var n;const e=this.getPaginatedItems();if(!e.length)return null;const t=(n=e[e.length-1])==null?void 0:n.id;return t?{params:{after:t}}:null}}class A{constructor(e){this._client=e}}class Zn extends A{list(e,t={},n){return W(t)?this.list(e,{},t):this._client.getAPIList(`/chat/completions/${e}/messages`,Js,{query:t,...n})}}class lt extends A{constructor(){super(...arguments),this.messages=new Zn(this._client)}create(e,t){var n;return this._client.post("/chat/completions",{body:e,...t,stream:(n=e.stream)!=null?n:!1})}retrieve(e,t){return this._client.get(`/chat/completions/${e}`,t)}update(e,t,n){return this._client.post(`/chat/completions/${e}`,{body:t,...n})}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/chat/completions",ct,{query:e,...t})}del(e,t){return this._client.delete(`/chat/completions/${e}`,t)}}class ct extends G{}class Js extends G{}lt.ChatCompletionsPage=ct;lt.Messages=Zn;class ut extends A{constructor(){super(...arguments),this.completions=new lt(this._client)}}ut.Completions=lt;ut.ChatCompletionsPage=ct;class er extends A{create(e,t){return this._client.post("/audio/speech",{body:e,...t,headers:{Accept:"application/octet-stream",...t==null?void 0:t.headers},__binaryResponse:!0})}}class tr extends A{create(e,t){return this._client.post("/audio/transcriptions",Se({body:e,...t,__metadata:{model:e.model}}))}}class nr extends A{create(e,t){return this._client.post("/audio/translations",Se({body:e,...t,__metadata:{model:e.model}}))}}class Ne extends A{constructor(){super(...arguments),this.transcriptions=new tr(this._client),this.translations=new nr(this._client),this.speech=new er(this._client)}}Ne.Transcriptions=tr;Ne.Translations=nr;Ne.Speech=er;class Nt extends A{create(e,t){return this._client.post("/batches",{body:e,...t})}retrieve(e,t){return this._client.get(`/batches/${e}`,t)}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/batches",Bt,{query:e,...t})}cancel(e,t){return this._client.post(`/batches/${e}/cancel`,t)}}class Bt extends G{}Nt.BatchesPage=Bt;class Dt extends A{create(e,t){return this._client.post("/assistants",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,n){return this._client.post(`/assistants/${e}`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/assistants",Lt,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}del(e,t){return this._client.delete(`/assistants/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}}class Lt extends G{}Dt.AssistantsPage=Lt;function fn(r){return typeof r.parse=="function"}const ye=r=>(r==null?void 0:r.role)==="assistant",rr=r=>(r==null?void 0:r.role)==="function",sr=r=>(r==null?void 0:r.role)==="tool";var Q=globalThis&&globalThis.__classPrivateFieldSet||function(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t},k=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},vt,Ge,Ke,Pe,Ee,Qe,ve,ie,Re,et,tt,ge,ar;class ir{constructor(){vt.add(this),this.controller=new AbortController,Ge.set(this,void 0),Ke.set(this,()=>{}),Pe.set(this,()=>{}),Ee.set(this,void 0),Qe.set(this,()=>{}),ve.set(this,()=>{}),ie.set(this,{}),Re.set(this,!1),et.set(this,!1),tt.set(this,!1),ge.set(this,!1),Q(this,Ge,new Promise((e,t)=>{Q(this,Ke,e,"f"),Q(this,Pe,t,"f")}),"f"),Q(this,Ee,new Promise((e,t)=>{Q(this,Qe,e,"f"),Q(this,ve,t,"f")}),"f"),k(this,Ge,"f").catch(()=>{}),k(this,Ee,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},k(this,vt,"m",ar).bind(this))},0)}_connected(){this.ended||(k(this,Ke,"f").call(this),this._emit("connect"))}get ended(){return k(this,Re,"f")}get errored(){return k(this,et,"f")}get aborted(){return k(this,tt,"f")}abort(){this.controller.abort()}on(e,t){return(k(this,ie,"f")[e]||(k(this,ie,"f")[e]=[])).push({listener:t}),this}off(e,t){const n=k(this,ie,"f")[e];if(!n)return this;const s=n.findIndex(a=>a.listener===t);return s>=0&&n.splice(s,1),this}once(e,t){return(k(this,ie,"f")[e]||(k(this,ie,"f")[e]=[])).push({listener:t,once:!0}),this}emitted(e){return new Promise((t,n)=>{Q(this,ge,!0,"f"),e!=="error"&&this.once("error",n),this.once(e,t)})}async done(){Q(this,ge,!0,"f"),await k(this,Ee,"f")}_emit(e,...t){if(k(this,Re,"f"))return;e==="end"&&(Q(this,Re,!0,"f"),k(this,Qe,"f").call(this));const n=k(this,ie,"f")[e];if(n&&(k(this,ie,"f")[e]=n.filter(s=>!s.once),n.forEach(({listener:s})=>s(...t))),e==="abort"){const s=t[0];!k(this,ge,"f")&&!(n!=null&&n.length)&&Promise.reject(s),k(this,Pe,"f").call(this,s),k(this,ve,"f").call(this,s),this._emit("end");return}if(e==="error"){const s=t[0];!k(this,ge,"f")&&!(n!=null&&n.length)&&Promise.reject(s),k(this,Pe,"f").call(this,s),k(this,ve,"f").call(this,s),this._emit("end")}}_emitFinal(){}}Ge=new WeakMap,Ke=new WeakMap,Pe=new WeakMap,Ee=new WeakMap,Qe=new WeakMap,ve=new WeakMap,ie=new WeakMap,Re=new WeakMap,et=new WeakMap,tt=new WeakMap,ge=new WeakMap,vt=new WeakSet,ar=function(e){if(Q(this,et,!0,"f"),e instanceof Error&&e.name==="AbortError"&&(e=new Y),e instanceof Y)return Q(this,tt,!0,"f"),this._emit("abort",e);if(e instanceof y)return this._emit("error",e);if(e instanceof Error){const t=new y(e.message);return t.cause=e,this._emit("error",t)}return this._emit("error",new y(String(e)))};function or(r){return(r==null?void 0:r.$brand)==="auto-parseable-response-format"}function Be(r){return(r==null?void 0:r.$brand)==="auto-parseable-tool"}function qs(r,e){return!e||!lr(e)?{...r,choices:r.choices.map(t=>({...t,message:{...t.message,parsed:null,...t.message.tool_calls?{tool_calls:t.message.tool_calls}:void 0}}))}:Ut(r,e)}function Ut(r,e){const t=r.choices.map(n=>{var s,a;if(n.finish_reason==="length")throw new Wn;if(n.finish_reason==="content_filter")throw new Jn;return{...n,message:{...n.message,...n.message.tool_calls?{tool_calls:(a=(s=n.message.tool_calls)==null?void 0:s.map(i=>Hs(e,i)))!=null?a:void 0}:void 0,parsed:n.message.content&&!n.message.refusal?Xs(e,n.message.content):null}}});return{...r,choices:t}}function Xs(r,e){var t,n;return((t=r.response_format)==null?void 0:t.type)!=="json_schema"?null:((n=r.response_format)==null?void 0:n.type)==="json_schema"?"$parseRaw"in r.response_format?r.response_format.$parseRaw(e):JSON.parse(e):null}function Hs(r,e){var n;const t=(n=r.tools)==null?void 0:n.find(s=>{var a;return((a=s.function)==null?void 0:a.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:Be(t)?t.$parseRaw(e.function.arguments):t!=null&&t.function.strict?JSON.parse(e.function.arguments):null}}}function Vs(r,e){var n;if(!r)return!1;const t=(n=r.tools)==null?void 0:n.find(s=>{var a;return((a=s.function)==null?void 0:a.name)===e.function.name});return Be(t)||(t==null?void 0:t.function.strict)||!1}function lr(r){var e,t;return or(r.response_format)?!0:(t=(e=r.tools)==null?void 0:e.some(n=>Be(n)||n.type==="function"&&n.function.strict===!0))!=null?t:!1}function Gs(r){for(const e of r!=null?r:[]){if(e.type!=="function")throw new y(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new y(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var J=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},j,Rt,nt,It,Tt,Ot,cr,$t;const dn=10;class ur extends ir{constructor(){super(...arguments),j.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var n;this._chatCompletions.push(e),this._emit("chatCompletion",e);const t=(n=e.choices[0])==null?void 0:n.message;return t&&this._addMessage(t),e}_addMessage(e,t=!0){if("content"in e||(e.content=null),this.messages.push(e),t){if(this._emit("message",e),(rr(e)||sr(e))&&e.content)this._emit("functionCallResult",e.content);else if(ye(e)&&e.function_call)this._emit("functionCall",e.function_call);else if(ye(e)&&e.tool_calls)for(const n of e.tool_calls)n.type==="function"&&this._emit("functionCall",n.function)}}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new y("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),J(this,j,"m",Rt).call(this)}async finalMessage(){return await this.done(),J(this,j,"m",nt).call(this)}async finalFunctionCall(){return await this.done(),J(this,j,"m",It).call(this)}async finalFunctionCallResult(){return await this.done(),J(this,j,"m",Tt).call(this)}async totalUsage(){return await this.done(),J(this,j,"m",Ot).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const t=J(this,j,"m",nt).call(this);t&&this._emit("finalMessage",t);const n=J(this,j,"m",Rt).call(this);n&&this._emit("finalContent",n);const s=J(this,j,"m",It).call(this);s&&this._emit("finalFunctionCall",s);const a=J(this,j,"m",Tt).call(this);a!=null&&this._emit("finalFunctionCallResult",a),this._chatCompletions.some(i=>i.usage)&&this._emit("totalUsage",J(this,j,"m",Ot).call(this))}async _createChatCompletion(e,t,n){const s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),J(this,j,"m",cr).call(this,t);const a=await e.chat.completions.create({...t,stream:!1},{...n,signal:this.controller.signal});return this._connected(),this._addChatCompletion(Ut(a,t))}async _runChatCompletion(e,t,n){for(const s of t.messages)this._addMessage(s,!1);return await this._createChatCompletion(e,t,n)}async _runFunctions(e,t,n){var f;const s="function",{function_call:a="auto",stream:i,...o}=t,l=typeof a!="string"&&(a==null?void 0:a.name),{maxChatCompletions:c=dn}=n||{},h={};for(const p of t.functions)h[p.name||p.function.name]=p;const u=t.functions.map(p=>({name:p.name||p.function.name,parameters:p.parameters,description:p.description}));for(const p of t.messages)this._addMessage(p,!1);for(let p=0;p<c;++p){const x=(f=(await this._createChatCompletion(e,{...o,function_call:a,functions:u,messages:[...this.messages]},n)).choices[0])==null?void 0:f.message;if(!x)throw new y("missing message in ChatCompletion response");if(!x.function_call)return;const{name:m,arguments:$}=x.function_call,g=h[m];if(g){if(l&&l!==m){const T=`Invalid function_call: ${JSON.stringify(m)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:s,name:m,content:T});continue}}else{const T=`Invalid function_call: ${JSON.stringify(m)}. Available options are: ${u.map(I=>JSON.stringify(I.name)).join(", ")}. Please try again`;this._addMessage({role:s,name:m,content:T});continue}let P;try{P=fn(g)?await g.parse($):$}catch(T){this._addMessage({role:s,name:m,content:T instanceof Error?T.message:String(T)});continue}const F=await g.function(P,this),w=J(this,j,"m",$t).call(this,F);if(this._addMessage({role:s,name:m,content:w}),l)return}}async _runTools(e,t,n){var p,_,x;const s="tool",{tool_choice:a="auto",stream:i,...o}=t,l=typeof a!="string"&&((p=a==null?void 0:a.function)==null?void 0:p.name),{maxChatCompletions:c=dn}=n||{},h=t.tools.map(m=>{if(Be(m)){if(!m.$callback)throw new y("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:m.$callback,name:m.function.name,description:m.function.description||"",parameters:m.function.parameters,parse:m.$parseRaw,strict:!0}}}return m}),u={};for(const m of h)m.type==="function"&&(u[m.function.name||m.function.function.name]=m.function);const f="tools"in t?h.map(m=>m.type==="function"?{type:"function",function:{name:m.function.name||m.function.function.name,parameters:m.function.parameters,description:m.function.description,strict:m.function.strict}}:m):void 0;for(const m of t.messages)this._addMessage(m,!1);for(let m=0;m<c;++m){const g=(_=(await this._createChatCompletion(e,{...o,tool_choice:a,tools:f,messages:[...this.messages]},n)).choices[0])==null?void 0:_.message;if(!g)throw new y("missing message in ChatCompletion response");if(!((x=g.tool_calls)!=null&&x.length))return;for(const P of g.tool_calls){if(P.type!=="function")continue;const F=P.id,{name:w,arguments:T}=P.function,I=u[w];if(I){if(l&&l!==w){const O=`Invalid tool_call: ${JSON.stringify(w)}. ${JSON.stringify(l)} requested. Please try again`;this._addMessage({role:s,tool_call_id:F,content:O});continue}}else{const O=`Invalid tool_call: ${JSON.stringify(w)}. Available options are: ${Object.keys(u).map(E=>JSON.stringify(E)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:F,content:O});continue}let R;try{R=fn(I)?await I.parse(T):T}catch(O){const E=O instanceof Error?O.message:String(O);this._addMessage({role:s,tool_call_id:F,content:E});continue}const S=await I.function(R,this),C=J(this,j,"m",$t).call(this,S);if(this._addMessage({role:s,tool_call_id:F,content:C}),l)return}}}}j=new WeakSet,Rt=function(){var e;return(e=J(this,j,"m",nt).call(this).content)!=null?e:null},nt=function(){var t,n;let e=this.messages.length;for(;e-- >0;){const s=this.messages[e];if(ye(s)){const{function_call:a,...i}=s,o={...i,content:(t=s.content)!=null?t:null,refusal:(n=s.refusal)!=null?n:null};return a&&(o.function_call=a),o}}throw new y("stream ended without producing a ChatCompletionMessage with role=assistant")},It=function(){var e,t;for(let n=this.messages.length-1;n>=0;n--){const s=this.messages[n];if(ye(s)&&(s==null?void 0:s.function_call))return s.function_call;if(ye(s)&&((e=s==null?void 0:s.tool_calls)==null?void 0:e.length))return(t=s.tool_calls.at(-1))==null?void 0:t.function}},Tt=function(){for(let e=this.messages.length-1;e>=0;e--){const t=this.messages[e];if(rr(t)&&t.content!=null||sr(t)&&t.content!=null&&typeof t.content=="string"&&this.messages.some(n=>{var s;return n.role==="assistant"&&((s=n.tool_calls)==null?void 0:s.some(a=>a.type==="function"&&a.id===t.tool_call_id))}))return t.content}},Ot=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:t}of this._chatCompletions)t&&(e.completion_tokens+=t.completion_tokens,e.prompt_tokens+=t.prompt_tokens,e.total_tokens+=t.total_tokens);return e},cr=function(e){if(e.n!=null&&e.n>1)throw new y("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},$t=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};class ke extends ur{static runFunctions(e,t,n){const s=new ke,a={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runFunctions"}};return s._run(()=>s._runFunctions(e,t,a)),s}static runTools(e,t,n){const s=new ke,a={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,a)),s}_addMessage(e,t=!0){super._addMessage(e,t),ye(e)&&e.content&&this._emit("content",e.content)}}const hr=1,fr=2,dr=4,mr=8,pr=16,gr=32,_r=64,wr=128,yr=256,br=wr|yr,Sr=pr|gr|br|_r,Ar=hr|fr|Sr,Cr=dr|mr,Ks=Ar|Cr,B={STR:hr,NUM:fr,ARR:dr,OBJ:mr,NULL:pr,BOOL:gr,NAN:_r,INFINITY:wr,MINUS_INFINITY:yr,INF:br,SPECIAL:Sr,ATOM:Ar,COLLECTION:Cr,ALL:Ks};class Qs extends Error{}class zs extends Error{}function Ys(r,e=B.ALL){if(typeof r!="string")throw new TypeError(`expecting str, got ${typeof r}`);if(!r.trim())throw new Error(`${r} is empty`);return Zs(r.trim(),e)}const Zs=(r,e)=>{const t=r.length;let n=0;const s=f=>{throw new Qs(`${f} at position ${n}`)},a=f=>{throw new zs(`${f} at position ${n}`)},i=()=>(u(),n>=t&&s("Unexpected end of input"),r[n]==='"'?o():r[n]==="{"?l():r[n]==="["?c():r.substring(n,n+4)==="null"||B.NULL&e&&t-n<4&&"null".startsWith(r.substring(n))?(n+=4,null):r.substring(n,n+4)==="true"||B.BOOL&e&&t-n<4&&"true".startsWith(r.substring(n))?(n+=4,!0):r.substring(n,n+5)==="false"||B.BOOL&e&&t-n<5&&"false".startsWith(r.substring(n))?(n+=5,!1):r.substring(n,n+8)==="Infinity"||B.INFINITY&e&&t-n<8&&"Infinity".startsWith(r.substring(n))?(n+=8,1/0):r.substring(n,n+9)==="-Infinity"||B.MINUS_INFINITY&e&&1<t-n&&t-n<9&&"-Infinity".startsWith(r.substring(n))?(n+=9,-1/0):r.substring(n,n+3)==="NaN"||B.NAN&e&&t-n<3&&"NaN".startsWith(r.substring(n))?(n+=3,NaN):h()),o=()=>{const f=n;let p=!1;for(n++;n<t&&(r[n]!=='"'||p&&r[n-1]==="\\");)p=r[n]==="\\"?!p:!1,n++;if(r.charAt(n)=='"')try{return JSON.parse(r.substring(f,++n-Number(p)))}catch(_){a(String(_))}else if(B.STR&e)try{return JSON.parse(r.substring(f,n-Number(p))+'"')}catch{return JSON.parse(r.substring(f,r.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},l=()=>{n++,u();const f={};try{for(;r[n]!=="}";){if(u(),n>=t&&B.OBJ&e)return f;const p=o();u(),n++;try{const _=i();Object.defineProperty(f,p,{value:_,writable:!0,enumerable:!0,configurable:!0})}catch(_){if(B.OBJ&e)return f;throw _}u(),r[n]===","&&n++}}catch{if(B.OBJ&e)return f;s("Expected '}' at end of object")}return n++,f},c=()=>{n++;const f=[];try{for(;r[n]!=="]";)f.push(i()),u(),r[n]===","&&n++}catch{if(B.ARR&e)return f;s("Expected ']' at end of array")}return n++,f},h=()=>{if(n===0){r==="-"&&B.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(r)}catch(p){if(B.NUM&e)try{return r[r.length-1]==="."?JSON.parse(r.substring(0,r.lastIndexOf("."))):JSON.parse(r.substring(0,r.lastIndexOf("e")))}catch{}a(String(p))}}const f=n;for(r[n]==="-"&&n++;r[n]&&!",]}".includes(r[n]);)n++;n==t&&!(B.NUM&e)&&s("Unterminated number literal");try{return JSON.parse(r.substring(f,n))}catch{r.substring(f,n)==="-"&&B.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(r.substring(f,r.lastIndexOf("e")))}catch(_){a(String(_))}}},u=()=>{for(;n<t&&` 
\r	`.includes(r[n]);)n++};return i()},mn=r=>Ys(r,B.ALL^B.NUM);var de=globalThis&&globalThis.__classPrivateFieldSet||function(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t},v=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},M,ae,me,le,_t,Xe,wt,yt,bt,He,St,pn;class Fe extends ur{constructor(e){super(),M.add(this),ae.set(this,void 0),me.set(this,void 0),le.set(this,void 0),de(this,ae,e,"f"),de(this,me,[],"f")}get currentChatCompletionSnapshot(){return v(this,le,"f")}static fromReadableStream(e){const t=new Fe(null);return t._run(()=>t._fromReadableStream(e)),t}static createChatCompletion(e,t,n){const s=new Fe(t);return s._run(()=>s._runChatCompletion(e,{...t,stream:!0},{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,t,n){var i;super._createChatCompletion;const s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),v(this,M,"m",_t).call(this);const a=await e.chat.completions.create({...t,stream:!0},{...n,signal:this.controller.signal});this._connected();for await(const o of a)v(this,M,"m",wt).call(this,o);if((i=a.controller.signal)!=null&&i.aborted)throw new Y;return this._addChatCompletion(v(this,M,"m",He).call(this))}async _fromReadableStream(e,t){var i;const n=t==null?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),v(this,M,"m",_t).call(this),this._connected();const s=se.fromReadableStream(e,this.controller);let a;for await(const o of s)a&&a!==o.id&&this._addChatCompletion(v(this,M,"m",He).call(this)),v(this,M,"m",wt).call(this,o),a=o.id;if((i=s.controller.signal)!=null&&i.aborted)throw new Y;return this._addChatCompletion(v(this,M,"m",He).call(this))}[(ae=new WeakMap,me=new WeakMap,le=new WeakMap,M=new WeakSet,_t=function(){this.ended||de(this,le,void 0,"f")},Xe=function(t){let n=v(this,me,"f")[t.index];return n||(n={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},v(this,me,"f")[t.index]=n,n)},wt=function(t){var s,a,i,o,l,c,h,u,f,p,_,x,m,$,g,P,F,w,T,I;if(this.ended)return;const n=v(this,M,"m",pn).call(this,t);this._emit("chunk",t,n);for(const R of t.choices){const S=n.choices[R.index];R.delta.content!=null&&((s=S.message)==null?void 0:s.role)==="assistant"&&((a=S.message)==null?void 0:a.content)&&(this._emit("content",R.delta.content,S.message.content),this._emit("content.delta",{delta:R.delta.content,snapshot:S.message.content,parsed:S.message.parsed})),R.delta.refusal!=null&&((i=S.message)==null?void 0:i.role)==="assistant"&&((o=S.message)==null?void 0:o.refusal)&&this._emit("refusal.delta",{delta:R.delta.refusal,snapshot:S.message.refusal}),((l=R.logprobs)==null?void 0:l.content)!=null&&((c=S.message)==null?void 0:c.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(h=R.logprobs)==null?void 0:h.content,snapshot:(f=(u=S.logprobs)==null?void 0:u.content)!=null?f:[]}),((p=R.logprobs)==null?void 0:p.refusal)!=null&&((_=S.message)==null?void 0:_.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(x=R.logprobs)==null?void 0:x.refusal,snapshot:($=(m=S.logprobs)==null?void 0:m.refusal)!=null?$:[]});const C=v(this,M,"m",Xe).call(this,S);S.finish_reason&&(v(this,M,"m",bt).call(this,S),C.current_tool_call_index!=null&&v(this,M,"m",yt).call(this,S,C.current_tool_call_index));for(const O of(g=R.delta.tool_calls)!=null?g:[])C.current_tool_call_index!==O.index&&(v(this,M,"m",bt).call(this,S),C.current_tool_call_index!=null&&v(this,M,"m",yt).call(this,S,C.current_tool_call_index)),C.current_tool_call_index=O.index;for(const O of(P=R.delta.tool_calls)!=null?P:[]){const E=(F=S.message.tool_calls)==null?void 0:F[O.index];!(E!=null&&E.type)||((E==null?void 0:E.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(w=E.function)==null?void 0:w.name,index:O.index,arguments:E.function.arguments,parsed_arguments:E.function.parsed_arguments,arguments_delta:(I=(T=O.function)==null?void 0:T.arguments)!=null?I:""}):(E==null||E.type,void 0))}}},yt=function(t,n){var i,o,l;if(v(this,M,"m",Xe).call(this,t).done_tool_calls.has(n))return;const a=(i=t.message.tool_calls)==null?void 0:i[n];if(!a)throw new Error("no tool call snapshot");if(!a.type)throw new Error("tool call snapshot missing `type`");if(a.type==="function"){const c=(l=(o=v(this,ae,"f"))==null?void 0:o.tools)==null?void 0:l.find(h=>h.type==="function"&&h.function.name===a.function.name);this._emit("tool_calls.function.arguments.done",{name:a.function.name,index:n,arguments:a.function.arguments,parsed_arguments:Be(c)?c.$parseRaw(a.function.arguments):c!=null&&c.function.strict?JSON.parse(a.function.arguments):null})}else a.type},bt=function(t){var s,a;const n=v(this,M,"m",Xe).call(this,t);if(t.message.content&&!n.content_done){n.content_done=!0;const i=v(this,M,"m",St).call(this);this._emit("content.done",{content:t.message.content,parsed:i?i.$parseRaw(t.message.content):null})}t.message.refusal&&!n.refusal_done&&(n.refusal_done=!0,this._emit("refusal.done",{refusal:t.message.refusal})),((s=t.logprobs)==null?void 0:s.content)&&!n.logprobs_content_done&&(n.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:t.logprobs.content})),((a=t.logprobs)==null?void 0:a.refusal)&&!n.logprobs_refusal_done&&(n.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:t.logprobs.refusal}))},He=function(){if(this.ended)throw new y("stream has ended, this shouldn't happen");const t=v(this,le,"f");if(!t)throw new y("request ended without sending any chunks");return de(this,le,void 0,"f"),de(this,me,[],"f"),ea(t,v(this,ae,"f"))},St=function(){var n;const t=(n=v(this,ae,"f"))==null?void 0:n.response_format;return or(t)?t:null},pn=function(t){var h,u,f,p,_,x;var n,s,a,i;let o=v(this,le,"f");const{choices:l,...c}=t;o?Object.assign(o,c):o=de(this,le,{...c,choices:[]},"f");for(const{delta:m,finish_reason:$,index:g,logprobs:P=null,...F}of t.choices){let w=o.choices[g];if(w||(w=o.choices[g]={finish_reason:$,index:g,message:{},logprobs:P,...F}),P)if(!w.logprobs)w.logprobs=Object.assign({},P);else{const{content:E,refusal:K,...oe}=P;Object.assign(w.logprobs,oe),E&&((h=(n=w.logprobs).content)!=null||(n.content=[]),w.logprobs.content.push(...E)),K&&((u=(s=w.logprobs).refusal)!=null||(s.refusal=[]),w.logprobs.refusal.push(...K))}if($&&(w.finish_reason=$,v(this,ae,"f")&&lr(v(this,ae,"f")))){if($==="length")throw new Wn;if($==="content_filter")throw new Jn}if(Object.assign(w,F),!m)continue;const{content:T,refusal:I,function_call:R,role:S,tool_calls:C,...O}=m;if(Object.assign(w.message,O),I&&(w.message.refusal=(w.message.refusal||"")+I),S&&(w.message.role=S),R&&(w.message.function_call?(R.name&&(w.message.function_call.name=R.name),R.arguments&&((f=(a=w.message.function_call).arguments)!=null||(a.arguments=""),w.message.function_call.arguments+=R.arguments)):w.message.function_call=R),T&&(w.message.content=(w.message.content||"")+T,!w.message.refusal&&v(this,M,"m",St).call(this)&&(w.message.parsed=mn(w.message.content))),C){w.message.tool_calls||(w.message.tool_calls=[]);for(const{index:E,id:K,type:oe,function:q,...kr}of C){const ee=(p=(i=w.message.tool_calls)[E])!=null?p:i[E]={};Object.assign(ee,kr),K&&(ee.id=K),oe&&(ee.type=oe),q&&((x=ee.function)!=null||(ee.function={name:(_=q.name)!=null?_:"",arguments:""})),q!=null&&q.name&&(ee.function.name=q.name),q!=null&&q.arguments&&(ee.function.arguments+=q.arguments,Vs(v(this,ae,"f"),ee)&&(ee.function.parsed_arguments=mn(ee.function.arguments)))}}}return o},Symbol.asyncIterator)](){const e=[],t=[];let n=!1;return this.on("chunk",s=>{const a=t.shift();a?a.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(const s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(const a of t)a.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(const a of t)a.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((a,i)=>t.push({resolve:a,reject:i})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new se(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function ea(r,e){const{id:t,choices:n,created:s,model:a,system_fingerprint:i,...o}=r,l={...o,id:t,choices:n.map(({message:c,finish_reason:h,index:u,logprobs:f,...p})=>{var P,F,w;if(!h)throw new y(`missing finish_reason for choice ${u}`);const{content:_=null,function_call:x,tool_calls:m,...$}=c,g=c.role;if(!g)throw new y(`missing role for choice ${u}`);if(x){const{arguments:T,name:I}=x;if(T==null)throw new y(`missing function_call.arguments for choice ${u}`);if(!I)throw new y(`missing function_call.name for choice ${u}`);return{...p,message:{content:_,function_call:{arguments:T,name:I},role:g,refusal:(P=c.refusal)!=null?P:null},finish_reason:h,index:u,logprobs:f}}return m?{...p,index:u,finish_reason:h,logprobs:f,message:{...$,role:g,content:_,refusal:(F=c.refusal)!=null?F:null,tool_calls:m.map((T,I)=>{const{function:R,type:S,id:C,...O}=T,{arguments:E,name:K,...oe}=R||{};if(C==null)throw new y(`missing choices[${u}].tool_calls[${I}].id
${Ve(r)}`);if(S==null)throw new y(`missing choices[${u}].tool_calls[${I}].type
${Ve(r)}`);if(K==null)throw new y(`missing choices[${u}].tool_calls[${I}].function.name
${Ve(r)}`);if(E==null)throw new y(`missing choices[${u}].tool_calls[${I}].function.arguments
${Ve(r)}`);return{...O,id:C,type:S,function:{...oe,name:K,arguments:E}}})}}:{...p,message:{...$,content:_,role:g,refusal:(w=c.refusal)!=null?w:null},finish_reason:h,index:u,logprobs:f}}),created:s,model:a,object:"chat.completion",...i?{system_fingerprint:i}:{}};return qs(l,e)}function Ve(r){return JSON.stringify(r)}class be extends Fe{static fromReadableStream(e){const t=new be(null);return t._run(()=>t._fromReadableStream(e)),t}static runFunctions(e,t,n){const s=new be(null),a={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runFunctions"}};return s._run(()=>s._runFunctions(e,t,a)),s}static runTools(e,t,n){const s=new be(t),a={...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,t,a)),s}}class xr extends A{parse(e,t){return Gs(e.tools),this._client.chat.completions.create(e,{...t,headers:{...t==null?void 0:t.headers,"X-Stainless-Helper-Method":"beta.chat.completions.parse"}})._thenUnwrap(n=>Ut(n,e))}runFunctions(e,t){return e.stream?be.runFunctions(this._client,e,t):ke.runFunctions(this._client,e,t)}runTools(e,t){return e.stream?be.runTools(this._client,e,t):ke.runTools(this._client,e,t)}stream(e,t){return Fe.createChatCompletion(this._client,e,t)}}class kt extends A{constructor(){super(...arguments),this.completions=new xr(this._client)}}(function(r){r.Completions=xr})(kt||(kt={}));class Pr extends A{create(e,t){return this._client.post("/realtime/sessions",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}}class jt extends A{constructor(){super(...arguments),this.sessions=new Pr(this._client)}}jt.Sessions=Pr;var d=globalThis&&globalThis.__classPrivateFieldGet||function(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)},X=globalThis&&globalThis.__classPrivateFieldSet||function(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t},D,Ft,re,ze,z,fe,_e,he,rt,V,Ye,Ze,$e,Ie,Te,gn,_n,wn,yn,bn,Sn,An;class Z extends ir{constructor(){super(...arguments),D.add(this),Ft.set(this,[]),re.set(this,{}),ze.set(this,{}),z.set(this,void 0),fe.set(this,void 0),_e.set(this,void 0),he.set(this,void 0),rt.set(this,void 0),V.set(this,void 0),Ye.set(this,void 0),Ze.set(this,void 0),$e.set(this,void 0)}[(Ft=new WeakMap,re=new WeakMap,ze=new WeakMap,z=new WeakMap,fe=new WeakMap,_e=new WeakMap,he=new WeakMap,rt=new WeakMap,V=new WeakMap,Ye=new WeakMap,Ze=new WeakMap,$e=new WeakMap,D=new WeakSet,Symbol.asyncIterator)](){const e=[],t=[];let n=!1;return this.on("event",s=>{const a=t.shift();a?a.resolve(s):e.push(s)}),this.on("end",()=>{n=!0;for(const s of t)s.resolve(void 0);t.length=0}),this.on("abort",s=>{n=!0;for(const a of t)a.reject(s);t.length=0}),this.on("error",s=>{n=!0;for(const a of t)a.reject(s);t.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:n?{value:void 0,done:!0}:new Promise((a,i)=>t.push({resolve:a,reject:i})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const t=new Z;return t._run(()=>t._fromReadableStream(e)),t}async _fromReadableStream(e,t){var a;const n=t==null?void 0:t.signal;n&&(n.aborted&&this.controller.abort(),n.addEventListener("abort",()=>this.controller.abort())),this._connected();const s=se.fromReadableStream(e,this.controller);for await(const i of s)d(this,D,"m",Ie).call(this,i);if((a=s.controller.signal)!=null&&a.aborted)throw new Y;return this._addRun(d(this,D,"m",Te).call(this))}toReadableStream(){return new se(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,t,n,s,a){const i=new Z;return i._run(()=>i._runToolAssistantStream(e,t,n,s,{...a,headers:{...a==null?void 0:a.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createToolAssistantStream(e,t,n,s,a){var c;const i=a==null?void 0:a.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));const o={...s,stream:!0},l=await e.submitToolOutputs(t,n,o,{...a,signal:this.controller.signal});this._connected();for await(const h of l)d(this,D,"m",Ie).call(this,h);if((c=l.controller.signal)!=null&&c.aborted)throw new Y;return this._addRun(d(this,D,"m",Te).call(this))}static createThreadAssistantStream(e,t,n){const s=new Z;return s._run(()=>s._threadAssistantStream(e,t,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,t,n,s){const a=new Z;return a._run(()=>a._runAssistantStream(e,t,n,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),a}currentEvent(){return d(this,Ye,"f")}currentRun(){return d(this,Ze,"f")}currentMessageSnapshot(){return d(this,z,"f")}currentRunStepSnapshot(){return d(this,$e,"f")}async finalRunSteps(){return await this.done(),Object.values(d(this,re,"f"))}async finalMessages(){return await this.done(),Object.values(d(this,ze,"f"))}async finalRun(){if(await this.done(),!d(this,fe,"f"))throw Error("Final run was not received.");return d(this,fe,"f")}async _createThreadAssistantStream(e,t,n){var o;const s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));const a={...t,stream:!0},i=await e.createAndRun(a,{...n,signal:this.controller.signal});this._connected();for await(const l of i)d(this,D,"m",Ie).call(this,l);if((o=i.controller.signal)!=null&&o.aborted)throw new Y;return this._addRun(d(this,D,"m",Te).call(this))}async _createAssistantStream(e,t,n,s){var l;const a=s==null?void 0:s.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const i={...n,stream:!0},o=await e.create(t,i,{...s,signal:this.controller.signal});this._connected();for await(const c of o)d(this,D,"m",Ie).call(this,c);if((l=o.controller.signal)!=null&&l.aborted)throw new Y;return this._addRun(d(this,D,"m",Te).call(this))}static accumulateDelta(e,t){for(const[n,s]of Object.entries(t)){if(!e.hasOwnProperty(n)){e[n]=s;continue}let a=e[n];if(a==null){e[n]=s;continue}if(n==="index"||n==="type"){e[n]=s;continue}if(typeof a=="string"&&typeof s=="string")a+=s;else if(typeof a=="number"&&typeof s=="number")a+=s;else if(gt(a)&&gt(s))a=this.accumulateDelta(a,s);else if(Array.isArray(a)&&Array.isArray(s)){if(a.every(i=>typeof i=="string"||typeof i=="number")){a.push(...s);continue}for(const i of s){if(!gt(i))throw new Error(`Expected array delta entry to be an object but got: ${i}`);const o=i.index;if(o==null)throw console.error(i),new Error("Expected array delta entry to have an `index` property");if(typeof o!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${o}`);const l=a[o];l==null?a.push(i):a[o]=this.accumulateDelta(l,i)}continue}else throw Error(`Unhandled record type: ${n}, deltaValue: ${s}, accValue: ${a}`);e[n]=a}return e}_addRun(e){return e}async _threadAssistantStream(e,t,n){return await this._createThreadAssistantStream(t,e,n)}async _runAssistantStream(e,t,n,s){return await this._createAssistantStream(t,e,n,s)}async _runToolAssistantStream(e,t,n,s,a){return await this._createToolAssistantStream(n,e,t,s,a)}}Ie=function(e){if(!this.ended)switch(X(this,Ye,e,"f"),d(this,D,"m",wn).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":d(this,D,"m",An).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":d(this,D,"m",_n).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":d(this,D,"m",gn).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},Te=function(){if(this.ended)throw new y("stream has ended, this shouldn't happen");if(!d(this,fe,"f"))throw Error("Final run has not been received");return d(this,fe,"f")},gn=function(e){const[t,n]=d(this,D,"m",bn).call(this,e,d(this,z,"f"));X(this,z,t,"f"),d(this,ze,"f")[t.id]=t;for(const s of n){const a=t.content[s.index];(a==null?void 0:a.type)=="text"&&this._emit("textCreated",a.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,t),e.data.delta.content)for(const s of e.data.delta.content){if(s.type=="text"&&s.text){let a=s.text,i=t.content[s.index];if(i&&i.type=="text")this._emit("textDelta",a,i.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=d(this,_e,"f")){if(d(this,he,"f"))switch(d(this,he,"f").type){case"text":this._emit("textDone",d(this,he,"f").text,d(this,z,"f"));break;case"image_file":this._emit("imageFileDone",d(this,he,"f").image_file,d(this,z,"f"));break}X(this,_e,s.index,"f")}X(this,he,t.content[s.index],"f")}break;case"thread.message.completed":case"thread.message.incomplete":if(d(this,_e,"f")!==void 0){const s=e.data.content[d(this,_e,"f")];if(s)switch(s.type){case"image_file":this._emit("imageFileDone",s.image_file,d(this,z,"f"));break;case"text":this._emit("textDone",s.text,d(this,z,"f"));break}}d(this,z,"f")&&this._emit("messageDone",e.data),X(this,z,void 0,"f")}},_n=function(e){const t=d(this,D,"m",yn).call(this,e);switch(X(this,$e,t,"f"),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const n=e.data.delta;if(n.step_details&&n.step_details.type=="tool_calls"&&n.step_details.tool_calls&&t.step_details.type=="tool_calls")for(const a of n.step_details.tool_calls)a.index==d(this,rt,"f")?this._emit("toolCallDelta",a,t.step_details.tool_calls[a.index]):(d(this,V,"f")&&this._emit("toolCallDone",d(this,V,"f")),X(this,rt,a.index,"f"),X(this,V,t.step_details.tool_calls[a.index],"f"),d(this,V,"f")&&this._emit("toolCallCreated",d(this,V,"f")));this._emit("runStepDelta",e.data.delta,t);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":X(this,$e,void 0,"f"),e.data.step_details.type=="tool_calls"&&d(this,V,"f")&&(this._emit("toolCallDone",d(this,V,"f")),X(this,V,void 0,"f")),this._emit("runStepDone",e.data,t);break}},wn=function(e){d(this,Ft,"f").push(e),this._emit("event",e)},yn=function(e){switch(e.event){case"thread.run.step.created":return d(this,re,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let t=d(this,re,"f")[e.data.id];if(!t)throw Error("Received a RunStepDelta before creation of a snapshot");let n=e.data;if(n.delta){const s=Z.accumulateDelta(t,n.delta);d(this,re,"f")[e.data.id]=s}return d(this,re,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":d(this,re,"f")[e.data.id]=e.data;break}if(d(this,re,"f")[e.data.id])return d(this,re,"f")[e.data.id];throw new Error("No snapshot available")},bn=function(e,t){let n=[];switch(e.event){case"thread.message.created":return[e.data,n];case"thread.message.delta":if(!t)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(const a of s.delta.content)if(a.index in t.content){let i=t.content[a.index];t.content[a.index]=d(this,D,"m",Sn).call(this,a,i)}else t.content[a.index]=a,n.push(a);return[t,n];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(t)return[t,n];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},Sn=function(e,t){return Z.accumulateDelta(t,e)},An=function(e){switch(X(this,Ze,e.data,"f"),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":X(this,fe,e.data,"f"),d(this,V,"f")&&(this._emit("toolCallDone",d(this,V,"f")),X(this,V,void 0,"f"));break}};class Wt extends A{create(e,t,n){return this._client.post(`/threads/${e}/messages`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,t,n){return this._client.get(`/threads/${e}/messages/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}update(e,t,n,s){return this._client.post(`/threads/${e}/messages/${t}`,{body:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}list(e,t={},n){return W(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/messages`,Jt,{query:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}del(e,t,n){return this._client.delete(`/threads/${e}/messages/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}}class Jt extends G{}Wt.MessagesPage=Jt;class qt extends A{retrieve(e,t,n,s={},a){return W(s)?this.retrieve(e,t,n,{},s):this._client.get(`/threads/${e}/runs/${t}/steps/${n}`,{query:s,...a,headers:{"OpenAI-Beta":"assistants=v2",...a==null?void 0:a.headers}})}list(e,t,n={},s){return W(n)?this.list(e,t,{},n):this._client.getAPIList(`/threads/${e}/runs/${t}/steps`,Xt,{query:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}}class Xt extends G{}qt.RunStepsPage=Xt;class De extends A{constructor(){super(...arguments),this.steps=new qt(this._client)}create(e,t,n){var i;const{include:s,...a}=t;return this._client.post(`/threads/${e}/runs`,{query:{include:s},body:a,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers},stream:(i=t.stream)!=null?i:!1})}retrieve(e,t,n){return this._client.get(`/threads/${e}/runs/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}update(e,t,n,s){return this._client.post(`/threads/${e}/runs/${t}`,{body:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}list(e,t={},n){return W(t)?this.list(e,{},t):this._client.getAPIList(`/threads/${e}/runs`,Ht,{query:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}cancel(e,t,n){return this._client.post(`/threads/${e}/runs/${t}/cancel`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}async createAndPoll(e,t,n){const s=await this.create(e,t,n);return await this.poll(e,s.id,n)}createAndStream(e,t,n){return Z.createAssistantStream(e,this._client.beta.threads.runs,t,n)}async poll(e,t,n){const s={...n==null?void 0:n.headers,"X-Stainless-Poll-Helper":"true"};for(n!=null&&n.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=n.pollIntervalMs.toString());;){const{data:a,response:i}=await this.retrieve(e,t,{...n,headers:{...n==null?void 0:n.headers,...s}}).withResponse();switch(a.status){case"queued":case"in_progress":case"cancelling":let o=5e3;if(n!=null&&n.pollIntervalMs)o=n.pollIntervalMs;else{const l=i.headers.get("openai-poll-after-ms");if(l){const c=parseInt(l);isNaN(c)||(o=c)}}await Me(o);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return a}}}stream(e,t,n){return Z.createAssistantStream(e,this._client.beta.threads.runs,t,n)}submitToolOutputs(e,t,n,s){var a;return this._client.post(`/threads/${e}/runs/${t}/submit_tool_outputs`,{body:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers},stream:(a=n.stream)!=null?a:!1})}async submitToolOutputsAndPoll(e,t,n,s){const a=await this.submitToolOutputs(e,t,n,s);return await this.poll(e,a.id,s)}submitToolOutputsStream(e,t,n,s){return Z.createToolAssistantStream(e,t,this._client.beta.threads.runs,n,s)}}class Ht extends G{}De.RunsPage=Ht;De.Steps=qt;De.RunStepsPage=Xt;class Ae extends A{constructor(){super(...arguments),this.runs=new De(this._client),this.messages=new Wt(this._client)}create(e={},t){return W(e)?this.create({},e):this._client.post("/threads",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,n){return this._client.post(`/threads/${e}`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}del(e,t){return this._client.delete(`/threads/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}createAndRun(e,t){var n;return this._client.post("/threads/runs",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers},stream:(n=e.stream)!=null?n:!1})}async createAndRunPoll(e,t){const n=await this.createAndRun(e,t);return await this.runs.poll(n.thread_id,n.id,t)}createAndRunStream(e,t){return Z.createThreadAssistantStream(e,this._client.beta.threads,t)}}Ae.Runs=De;Ae.RunsPage=Ht;Ae.Messages=Wt;Ae.MessagesPage=Jt;const ta=async r=>{const e=await Promise.allSettled(r),t=e.filter(s=>s.status==="rejected");if(t.length){for(const s of t)console.error(s.reason);throw new Error(`${t.length} promise(s) failed - see the above errors`)}const n=[];for(const s of e)s.status==="fulfilled"&&n.push(s.value);return n};class Vt extends A{create(e,t,n){return this._client.post(`/vector_stores/${e}/files`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,t,n){return this._client.get(`/vector_stores/${e}/files/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}list(e,t={},n){return W(t)?this.list(e,{},t):this._client.getAPIList(`/vector_stores/${e}/files`,ht,{query:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}del(e,t,n){return this._client.delete(`/vector_stores/${e}/files/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}async createAndPoll(e,t,n){const s=await this.create(e,t,n);return await this.poll(e,s.id,n)}async poll(e,t,n){const s={...n==null?void 0:n.headers,"X-Stainless-Poll-Helper":"true"};for(n!=null&&n.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=n.pollIntervalMs.toString());;){const a=await this.retrieve(e,t,{...n,headers:s}).withResponse(),i=a.data;switch(i.status){case"in_progress":let o=5e3;if(n!=null&&n.pollIntervalMs)o=n.pollIntervalMs;else{const l=a.response.headers.get("openai-poll-after-ms");if(l){const c=parseInt(l);isNaN(c)||(o=c)}}await Me(o);break;case"failed":case"completed":return i}}}async upload(e,t,n){const s=await this._client.files.create({file:t,purpose:"assistants"},n);return this.create(e,{file_id:s.id},n)}async uploadAndPoll(e,t,n){const s=await this.upload(e,t,n);return await this.poll(e,s.id,n)}}class ht extends G{}Vt.VectorStoreFilesPage=ht;class Er extends A{create(e,t,n){return this._client.post(`/vector_stores/${e}/file_batches`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}retrieve(e,t,n){return this._client.get(`/vector_stores/${e}/file_batches/${t}`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}cancel(e,t,n){return this._client.post(`/vector_stores/${e}/file_batches/${t}/cancel`,{...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}async createAndPoll(e,t,n){const s=await this.create(e,t);return await this.poll(e,s.id,n)}listFiles(e,t,n={},s){return W(n)?this.listFiles(e,t,{},n):this._client.getAPIList(`/vector_stores/${e}/file_batches/${t}/files`,ht,{query:n,...s,headers:{"OpenAI-Beta":"assistants=v2",...s==null?void 0:s.headers}})}async poll(e,t,n){const s={...n==null?void 0:n.headers,"X-Stainless-Poll-Helper":"true"};for(n!=null&&n.pollIntervalMs&&(s["X-Stainless-Custom-Poll-Interval"]=n.pollIntervalMs.toString());;){const{data:a,response:i}=await this.retrieve(e,t,{...n,headers:s}).withResponse();switch(a.status){case"in_progress":let o=5e3;if(n!=null&&n.pollIntervalMs)o=n.pollIntervalMs;else{const l=i.headers.get("openai-poll-after-ms");if(l){const c=parseInt(l);isNaN(c)||(o=c)}}await Me(o);break;case"failed":case"cancelled":case"completed":return a}}}async uploadAndPoll(e,{files:t,fileIds:n=[]},s){var f;if(t==null||t.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const a=(f=s==null?void 0:s.maxConcurrency)!=null?f:5,i=Math.min(a,t.length),o=this._client,l=t.values(),c=[...n];async function h(p){for(let _ of p){const x=await o.files.create({file:_,purpose:"assistants"},s);c.push(x.id)}}const u=Array(i).fill(l).map(h);return await ta(u),await this.createAndPoll(e,{file_ids:c})}}class Ce extends A{constructor(){super(...arguments),this.files=new Vt(this._client),this.fileBatches=new Er(this._client)}create(e,t){return this._client.post("/vector_stores",{body:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}retrieve(e,t){return this._client.get(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}update(e,t,n){return this._client.post(`/vector_stores/${e}`,{body:t,...n,headers:{"OpenAI-Beta":"assistants=v2",...n==null?void 0:n.headers}})}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/vector_stores",Gt,{query:e,...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}del(e,t){return this._client.delete(`/vector_stores/${e}`,{...t,headers:{"OpenAI-Beta":"assistants=v2",...t==null?void 0:t.headers}})}}class Gt extends G{}Ce.VectorStoresPage=Gt;Ce.Files=Vt;Ce.VectorStoreFilesPage=ht;Ce.FileBatches=Er;class ce extends A{constructor(){super(...arguments),this.realtime=new jt(this._client),this.vectorStores=new Ce(this._client),this.chat=new kt(this._client),this.assistants=new Dt(this._client),this.threads=new Ae(this._client)}}ce.Realtime=jt;ce.VectorStores=Ce;ce.VectorStoresPage=Gt;ce.Assistants=Dt;ce.AssistantsPage=Lt;ce.Threads=Ae;class vr extends A{create(e,t){var n;return this._client.post("/completions",{body:e,...t,stream:(n=e.stream)!=null?n:!1})}}class Rr extends A{create(e,t){return this._client.post("/embeddings",{body:e,...t})}}class Kt extends A{create(e,t){return this._client.post("/files",Se({body:e,...t}))}retrieve(e,t){return this._client.get(`/files/${e}`,t)}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/files",Qt,{query:e,...t})}del(e,t){return this._client.delete(`/files/${e}`,t)}content(e,t){return this._client.get(`/files/${e}/content`,{...t,headers:{Accept:"application/binary",...t==null?void 0:t.headers},__binaryResponse:!0})}retrieveContent(e,t){return this._client.get(`/files/${e}/content`,t)}async waitForProcessing(e,{pollInterval:t=5e3,maxWait:n=30*60*1e3}={}){const s=new Set(["processed","error","deleted"]),a=Date.now();let i=await this.retrieve(e);for(;!i.status||!s.has(i.status);)if(await Me(t),i=await this.retrieve(e),Date.now()-a>n)throw new Mt({message:`Giving up on waiting for file ${e} to finish processing after ${n} milliseconds.`});return i}}class Qt extends G{}Kt.FileObjectsPage=Qt;class zt extends A{list(e,t={},n){return W(t)?this.list(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/checkpoints`,Yt,{query:t,...n})}}class Yt extends G{}zt.FineTuningJobCheckpointsPage=Yt;class xe extends A{constructor(){super(...arguments),this.checkpoints=new zt(this._client)}create(e,t){return this._client.post("/fine_tuning/jobs",{body:e,...t})}retrieve(e,t){return this._client.get(`/fine_tuning/jobs/${e}`,t)}list(e={},t){return W(e)?this.list({},e):this._client.getAPIList("/fine_tuning/jobs",Zt,{query:e,...t})}cancel(e,t){return this._client.post(`/fine_tuning/jobs/${e}/cancel`,t)}listEvents(e,t={},n){return W(t)?this.listEvents(e,{},t):this._client.getAPIList(`/fine_tuning/jobs/${e}/events`,en,{query:t,...n})}}class Zt extends G{}class en extends G{}xe.FineTuningJobsPage=Zt;xe.FineTuningJobEventsPage=en;xe.Checkpoints=zt;xe.FineTuningJobCheckpointsPage=Yt;class Le extends A{constructor(){super(...arguments),this.jobs=new xe(this._client)}}Le.Jobs=xe;Le.FineTuningJobsPage=Zt;Le.FineTuningJobEventsPage=en;class Ir extends A{createVariation(e,t){return this._client.post("/images/variations",Se({body:e,...t}))}edit(e,t){return this._client.post("/images/edits",Se({body:e,...t}))}generate(e,t){return this._client.post("/images/generations",{body:e,...t})}}class tn extends A{retrieve(e,t){return this._client.get(`/models/${e}`,t)}list(e){return this._client.getAPIList("/models",nn,e)}del(e,t){return this._client.delete(`/models/${e}`,t)}}class nn extends Ws{}tn.ModelsPage=nn;class Tr extends A{create(e,t){return this._client.post("/moderations",{body:e,...t})}}class Or extends A{create(e,t,n){return this._client.post(`/uploads/${e}/parts`,Se({body:t,...n}))}}class rn extends A{constructor(){super(...arguments),this.parts=new Or(this._client)}create(e,t){return this._client.post("/uploads",{body:e,...t})}cancel(e,t){return this._client.post(`/uploads/${e}/cancel`,t)}complete(e,t,n){return this._client.post(`/uploads/${e}/complete`,{body:t,...n})}}rn.Parts=Or;var $r;class b extends Is{constructor({baseURL:e=Je("OPENAI_BASE_URL"),apiKey:t=Je("OPENAI_API_KEY"),organization:n=(i=>(i=Je("OPENAI_ORG_ID"))!=null?i:null)(),project:s=(o=>(o=Je("OPENAI_PROJECT_ID"))!=null?o:null)(),...a}={}){var c;if(t===void 0)throw new y("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const l={apiKey:t,organization:n,project:s,...a,baseURL:e||"https://api.openai.com/v1"};if(!l.dangerouslyAllowBrowser&&Us())throw new y(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);super({baseURL:l.baseURL,timeout:(c=l.timeout)!=null?c:6e5,httpAgent:l.httpAgent,maxRetries:l.maxRetries,fetch:l.fetch}),this.completions=new vr(this),this.chat=new ut(this),this.embeddings=new Rr(this),this.files=new Kt(this),this.images=new Ir(this),this.audio=new Ne(this),this.moderations=new Tr(this),this.models=new tn(this),this.fineTuning=new Le(this),this.beta=new ce(this),this.batches=new Nt(this),this.uploads=new rn(this),this._options=l,this.apiKey=t,this.organization=n,this.project=s}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project,...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}stringifyQuery(e){return hs(e,{arrayFormat:"brackets"})}}$r=b;b.OpenAI=$r;b.DEFAULT_TIMEOUT=6e5;b.OpenAIError=y;b.APIError=L;b.APIConnectionError=st;b.APIConnectionTimeoutError=Mt;b.APIUserAbortError=Y;b.NotFoundError=Bn;b.ConflictError=Dn;b.RateLimitError=Un;b.BadRequestError=Fn;b.AuthenticationError=Mn;b.InternalServerError=jn;b.PermissionDeniedError=Nn;b.UnprocessableEntityError=Ln;b.toFile=Vn;b.fileFromPath=$n;b.Completions=vr;b.Chat=ut;b.ChatCompletionsPage=ct;b.Embeddings=Rr;b.Files=Kt;b.FileObjectsPage=Qt;b.Images=Ir;b.Audio=Ne;b.Moderations=Tr;b.Models=tn;b.ModelsPage=nn;b.FineTuning=Le;b.Beta=ce;b.Batches=Nt;b.BatchesPage=Bt;b.Uploads=rn;const na=b;let Cn=null;chrome.runtime.onMessage.addListener(function(r,e,t){if(console.log("event",r),r.action&&r.action.type==="event"){if(r.action.name==="loginByPhoneAndPassword")Br(r.data).then(n=>{console.log("loginByPhoneAndPassword",n),t(n)});else if(r.action.name==="userRegister")Dr(r.data).then(n=>{console.log("userRegister",n),t(n)});else if(r.action.name==="invitationTemplateAll")Lr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="invitationTemplateNew")Ur(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="invitationTemplateDelete")jr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="messageTemplateAll")Wr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="messageTemplateNew")Jr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="messageTemplateDelete")qr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="userBindingsShopNew")Xr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="userBindingsShopAll")Hr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="getInvitationCodeInvitees")Vr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="invitationCodeUpdate")Gr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="getRelationshipChildUsers")Kr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="relationshipUpdatePassword")Qr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="relationshipDelete")zr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="shopBindingNew")Zr(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="shopBindingAll")es(r.data).then(n=>{console.log("data",n),t(n)});else if(r.action.name==="verificationDetectSlideImg"){const n=r.data;ts(n).then(s=>{const a=new FormData;a.append("file",s),Yr(a).then(i=>{console.log("data",i),t(i)})})}else if(r.action.name==="deepseekInit"){Cn=new na({apiKey:"***********************************",baseURL:"https://dashscope.aliyuncs.com/compatible-mode/v1"});async function n(){var s,a;try{const i=await Cn.chat.completions.create({model:"deepseek-r1",messages:((s=r==null?void 0:r.data)==null?void 0:s.messages)||[],stream:!0}).catch(o=>{chrome.tabs.query({},l=>{var c;for(let h of l)h.id&&chrome.tabs.sendMessage(h.id,{command:"easyLinkDeepSeekContentError"+((c=r==null?void 0:r.data)==null?void 0:c.asin),value:o.message})})});for await(const o of i){if(!((a=o.choices)!=null&&a.length)){console.log(`
Usage:`),console.log(o.usage);continue}const l=o.choices[0].delta;console.log("delta",o,l),l.reasoning_content?chrome.tabs.query({},c=>{var h;for(let u of c)u.id&&chrome.tabs.sendMessage(u.id,{command:"updateReasoningContent"+((h=r==null?void 0:r.data)==null?void 0:h.asin),value:l.reasoning_content})}):l.content&&chrome.tabs.query({},c=>{var h;for(let u of c)u.id&&chrome.tabs.sendMessage(u.id,{command:"updateAnswerContent"+((h=r==null?void 0:r.data)==null?void 0:h.asin),value:l.content})})}chrome.tabs.query({},o=>{var l;for(let c of o)c.id&&chrome.tabs.sendMessage(c.id,{command:"easyLinkDeepSeekContentDone"+((l=r==null?void 0:r.data)==null?void 0:l.asin)})})}catch(i){console.error("Error:",i)}}n()}}return!0});
