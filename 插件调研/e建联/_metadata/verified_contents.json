[{"description": "treehash per file", "signed_content": {"payload": "************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************", "signatures": [{"header": {"kid": "publisher"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "Jt2v5GkrQx-6udAz0tNVwC3_4ItYkbkm-llAZswBHNiohXBLfu5HoKDlcmFTI-zaHLGadSnKqA38k9HzxGrqNiU5MONywkpSxvnu5ckWUC9fNbQmEgmIV17IdcZ3kmkJZsOd2E_p2-PbQiMCywNE103V1ywZa3cltjTQBmxvA7zgZomozN4pin-8yJrD0-8kGI-CaDP1QP2_lvcrmC1LpNRM-kHLYYAmMyICQTTLZggabObgYPL3BavfVx0m-DEB75eEG_ua5H3ZWua-ruwyhUDNaJfJXw0jpsk1VFeBJske4Z_sEmx-kgO5VX5qXYMKODTB2quhVkmQQqyS-P2A9w"}, {"header": {"kid": "webstore"}, "protected": "eyJhbGciOiJSUzI1NiJ9", "signature": "M_GcgS2LdOsIUQw6Bw1_fv5owmcdJUwypDGyJoBDb7jOPIxklNtW6LR16JCxzSLFL9xuS9fhwGnrzNah-hgCGcZFPn9RLQgwwVZNCJ-uXnlNo8Ft-IOM5VFtsiewQhSR6-R_hs0QZ8B4lKGYal-OH-dXH6XvLF1Jl1v6yPHMy2Nj2SdaPcm9aWEGR4Sq5dUVVe2LsuxZyEhzq5D1weB1pxc_qwPI0ECVpucC8oBUn4vYlNPaFZCAKv_9Y95LCE0DtAEJTx3lmFeQdf8dzx1N9GuXAKJBA58l690hX5YxXGefqxMp6yKbrMupFSuBX-92IhrnGoC249kk52sOvU9nLg"}]}}]