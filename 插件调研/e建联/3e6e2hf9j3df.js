
window.addEventListener("message", (e) => {
  if (e.data.type === 'easyLinkMessage') {
    if (e.data.name === 'getAccountInfoParam') {
      window.postMessage({
        type: 'easyLinkMessage',
        name: 'accountInfoParam',
        data: window._accountInfoParam
      }, '*');
    }
  }
});

let tiktokListInfo = {
  url: '/api/v1/oec/affiliate/creator/marketplace/find',
  query: '',
  headers: {},
};

let globalShopData = null;

let creatorList = [];

function sleep(time) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, time);
  });
}

function isCreatorListUrl(url) {
  return url.indexOf('/api/v1/oec/affiliate/creator/marketplace/find') === 0;
}

const localCreatorListMap = {};

const sendInvitationLog = (log, extra) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendInvitationLog',
    data: {
      log,
      time: new Date().valueOf(),
      ...extra,
    },
  }, '*');
}

const sendTagInvitationLog = (log, extra) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendTagInvitationLog',
    data: {
      log,
      time: new Date().valueOf(),
      ...extra,
    },
  }, '*');
}

const sendMessageLog = (log, extra) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendMessageLog',
    data: {
      log,
      time: new Date().valueOf(),
      ...extra,
    },
  }, '*');
}

const sendTagMessageLog = (log, extra) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendTagMessageLog',
    data: {
      log,
      time: new Date().valueOf(),
      ...extra,
    },
  }, '*');
}

(function (xhr) {
  var XHR = XMLHttpRequest.prototype;
  var originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
  var open = XHR.open;
  var send = XHR.send;
  var currentUrl = '';
  var currentHeaders = {};
  XHR.open = function (method, url) {
    this._method = method;
    this._url = url;
    currentUrl = url;
    return open.apply(this, arguments);
  };

  XHR.setRequestHeader = function (name, value) {
    if (isCreatorListUrl(currentUrl)) {
      currentHeaders[name] = value;
    }
    originalSetRequestHeader.call(this, name, value);
  };

  XHR.send = function (data) {
    if(isCreatorListUrl(currentUrl)) {
      this.addEventListener("load", function() {
        if (!localStorage.getItem('easyLinkInviteRobotConfig') && !localStorage.getItem('easyLinkMessageRobotConfig')) {
          try {
            const res = JSON.parse(this.responseText);

            if (res?.creator_profile_list?.length) {
              for (const creator_profile of res.creator_profile_list) {
                localCreatorListMap[creator_profile.handle.value] = {
                  avatar: creator_profile.avatar?.value?.thumb_url_list?.[0],
                  username: creator_profile.handle?.value,
                  id: creator_profile.creator_oecuid?.value,
                }
              }
            }
            console.log('localCreatorListMap', localCreatorListMap);

            window.postMessage({
              type: 'easyLinkMessage',
              name: 'updateCurrentPageCreatorListListMap',
              data: localCreatorListMap,
            }, '*');
          } catch (error) {
            console.error(error);
          }
        }
      });
    }
    if(isCreatorListUrl(currentUrl)) {
      try {
        const parseData = JSON.parse(data);
        if (parseData.filter_params) {
          window.postMessage({
            type: 'easyLinkMessage',
            name: 'updateCreatorListFilters',
            data: {
              filterParams: parseData.filter_params,
              filtersText: document.querySelector('div[class^="index-module__selected"] div div[data-tid="m4b_space"]').innerText.replaceAll('\n', '; '),
            }
          }, '*');
        }
      } catch (error) {}
      localStorage.setItem('localConnectCreatorListBody', data)
    }

    if (window.location.pathname === '/seller/im' && currentUrl.indexOf('/api/v1/affiliate/account/info') === 0) {
      const url = new URL(`${origin}${currentUrl}`);
      url.searchParams.delete('_signature');
      url.searchParams.delete('X-Bogus');
      url.searchParams.delete('msToken');
      const searchQuery = url.searchParams.toString() || localStorage.getItem('easyLinkTiktokInfoListQuery');
      localStorage.setItem('easyLinkChatPageSearchQuery', searchQuery);
    }
    if (isCreatorListUrl(currentUrl) && !tiktokListInfo.query) {
      const url = new URL(`${origin}${currentUrl}`);
      url.searchParams.delete('_signature');
      url.searchParams.delete('X-Bogus');
      url.searchParams.delete('msToken');
      tiktokListInfo.query = url.searchParams.toString() || localStorage.getItem('easyLinkTiktokInfoListQuery');
      tiktokListInfo.headers = currentHeaders;
      if (tiktokListInfo.query) {
        localStorage.setItem('easyLinkTiktokInfoListQuery', tiktokListInfo.query);
      }
      console.log('currentUrl', currentUrl,  tiktokListInfo)
    }
    return send.apply(this, arguments);
  };
})(XMLHttpRequest);

const sendTipAndReloadPage = (type) => {
 if (type === 'invitation') {
    sendInvitationLog(`请求达人列表网络异常，10秒后刷新页面重启邀约机器人`);
    setTimeout(() => {
      window.location.reload();
    }, 10000);
  } else if (type === 'message') {
    sendMessageLog(`请求达人列表网络异常，10秒后刷新页面重启私信机器人`);
    setTimeout(() => {
      window.location.reload();
    }, 10000);
  }
}

async function simulateMouseMove(startX, startY, endX, endY, easyLinkSlideIframeDocument, stepCount) {
  // console.log('simulateMouseMove', startX, startY, endX, endY,)

  return new Promise((resolve) => {
    // 计算贝塞尔曲线的控制点（使移动轨迹更自然）
    const controlX = startX + (endX - startX) * 0.5;
    const controlY = Math.min(startY, endY) - 50; // 控制点稍微向上，形成弧形轨迹

    // 生成贝塞尔曲线上的点
    const points = [];
    for (let i = 0; i <= stepCount; i++) {
      const t = i / stepCount;
      // 二次贝塞尔曲线公式
      const x = Math.round(
        Math.pow(1 - t, 2) * startX +
        2 * (1 - t) * t * controlX +
        Math.pow(t, 2) * endX
      );
      const y = Math.round(
        Math.pow(1 - t, 2) * startY +
        2 * (1 - t) * t * controlY +
        Math.pow(t, 2) * endY
      );
      points.push({ x, y });
    }

    // 按顺序触发鼠标移动事件
    let index = 0;
    const interval = 20;

    function moveToNextPoint() {
      if (index < points.length) {
        const { x, y } = points[index];

        // 创建并触发鼠标移动事件
        const event = new MouseEvent('mousemove', {
          bubbles: true,
          cancelable: true,
          clientX: x,
          clientY: y,
          view: window
        });
        easyLinkSlideIframeDocument.elementFromPoint(x, y)?.dispatchEvent(event);
        index++;
        setTimeout(moveToNextPoint, interval);
      } else {
        console.log('simulateMouseMove done')
        resolve(true);
      }
    }

    moveToNextPoint();
  });
}

function getRandomPosition() {
  return {
    x: Math.floor(Math.random() * window.innerWidth * 0.8) + window.innerWidth * 0.1,
    y: Math.floor(Math.random() * window.innerHeight * 0.8) + window.innerHeight * 0.1
  };
}

// 从随机位置移动到固定点
async function simulateMouseFromRandom(stepCount) {
  const start = getRandomPosition();
  const end = getRandomPosition();
  await simulateMouseMove(start.x, start.y, end.x, end.y, document, stepCount);
}

const sendTipAndDragPicturePuzzle = (type) => {
  return new Promise((resolve) => {
    if (type === 'invitation') {
      sendInvitationLog(`e建联正在拖动验证码，请稍候...`);
    } else if (type === 'message') {
      sendMessageLog(`e建联正在拖动验证码，请稍候...`);
    }

    window.postMessage({
      type: 'easyLinkMessage',
      name: 'dragSlidePicturePuzzle',
      data: [],
    }, '*');

    const handleSuccessDragSlidePicturePuzzle = (e) => {
      if (e.data.type === 'easyLinkMessage') {
        if (e.data.name === 'successDragSlidePicturePuzzle') {
          console.log('successDragSlidePicturePuzzle',  e.data.data);
          if (type === 'invitation') {
            sendInvitationLog(e.data.data.success ? '验证码已拖动完成，继续邀约' : e.data.data.msg);
          } else if (type === 'message') {
            sendMessageLog(e.data.data.success ? '验证码已拖动完成，继续邀约' : e.data.data.msg);
          }
          window.removeEventListener('message', handleSuccessDragSlidePicturePuzzle)
          resolve(e.data.data);
        }
      }
    }

    window.addEventListener("message", handleSuccessDragSlidePicturePuzzle);
  });
}

const scrollCreatorListContainer = async () => {
  let scrollContainer = document.querySelector('#content-container');
  if (scrollContainer) {
    const stepCount = Math.floor(Math.random() * 300 + 300);
    await simulateMouseFromRandom(stepCount);
    let d = document.createElement('div');
    d.id = 'cc-container-bottom';
    scrollContainer.appendChild(d);
    document.querySelector('#cc-container-bottom').scrollIntoView({
      behavior: "smooth",
      block: "end"
    });
  }
}

const fetchNotFirstPageTkCreatorList = (body, pagination, type) => {
  return new Promise((resolve) => {
    var xhr = new XMLHttpRequest();

    xhr.open("POST", `${tiktokListInfo.url}?${tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery')}`);

    for (const key in (tiktokListInfo.headers || {})) {
      xhr.setRequestHeader(key, tiktokListInfo.headers[key]);
    }
    xhr.onload = async function () {
      if (xhr.status === 200) {
        if (!xhr.responseText) {
          await scrollCreatorListContainer();
          await scrollCreatorListContainer();
          resolve({
            list: [],
            next: { search_key: 'no more',has_more: false },
          });
        } else {
          const res = JSON.parse(xhr.responseText);
          if (!res.creator_profile_list) {
            if (res.code === 0) {
              resolve({
                list: [],
                next: { search_key: 'no more',has_more: false },
              });
            } else {
              await sendTipAndDragPicturePuzzle(type);
              resolve({
                list: [],
                next: null,
              });
            }
          } else {
            resolve({
              list: (res.creator_profile_list || []).filter((o) => (o.is_creator_blocked_by_shop?.status === 0 && o.is_creator_blocked_by_shop?.value === false)),
              next: res.next_pagination,
            });
          }
        }
      } else {
        sendTipAndReloadPage(type);
      }
    };

    xhr.ontimeout = async function () {
      console.error('请求发生错误');
      sendTipAndReloadPage(type);
    };

    xhr.onerror = async function () {
      console.error('请求发生错误');
      sendTipAndReloadPage(type);
    };

    xhr.send(JSON.stringify({
      ...body,
      pagination,
    }));
  });
}

const fetchFirstPageTkCreatorList = (body, type) => {
  return new Promise((resolve) => {
    var xhr = new XMLHttpRequest();

    xhr.open("POST", `${tiktokListInfo.url}?${tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery')}`); // 第三个参数为 true 表示异步请求

    for (const key in (tiktokListInfo.headers || {})) {
      xhr.setRequestHeader(key, tiktokListInfo.headers[key]);
    }
    xhr.onload = async function () {
      if (xhr.status === 200) { // 请求成功
        if (!xhr.responseText) {
          await scrollCreatorListContainer();
          await scrollCreatorListContainer();
          resolve({
            list: [],
            next: { search_key: 'no more',has_more: false },
          });
        } else {
          const res = JSON.parse(xhr.responseText);
          if (!res.creator_profile_list) {
            if (res.code === 0) {
              resolve({
                list: [],
                next: { search_key: 'no more',has_more: false },
              });
            } else {
              await sendTipAndDragPicturePuzzle(type);
              resolve({
                list: [],
                next: null,
              });
            }
          } else {
            resolve({
              list: (res.creator_profile_list || []).filter((o) => (o.is_creator_blocked_by_shop?.status === 0 && o.is_creator_blocked_by_shop?.value === false)),
              next: res.next_pagination,
            });
          }
        }
      } else {
        sendTipAndReloadPage(type);
      }
    };

    xhr.ontimeout = async function () {
      console.error('请求发生错误');
      sendTipAndReloadPage(type);
    };

    xhr.onerror = async function () {
      console.error('请求发生错误');
      sendTipAndReloadPage(type);
    };

    xhr.send(JSON.stringify(body));
  });
}

let fetchFillInvitationPagination = { size: 12, page: 0 };
let fetchDiggingCreatorNextPage = { size: 12, page: 0 };

let fetchInvitationPagination = { size: 12, page: 0 };
// const localFetchInvitationPagination = localStorage.getItem('localFetchInvitationPagination');
// if (localFetchInvitationPagination) {
//   try {
//     fetchInvitationPagination = JSON.parse(localFetchInvitationPagination);
//   } catch (error) {
//   }
// }

let fetchMessagePagination = { size: 12, page: 0 };
// const localFetchMessagePagination = localStorage.getItem('localFetchMessagePagination');
// if (localFetchMessagePagination) {
//   try {
//     fetchMessagePagination = JSON.parse(localFetchMessagePagination);
//   } catch (error) {
//   }
// }

let inviteCreatorList = [];
let messageCreatorList = [];
const invitationDoneResMap = {};

const getCleanInvitationCreatorList = (creatorList, productList, type) => {
  return new Promise(async (resolve) => {
    const filterCreators = (filterCreatorList) => {
      if (!filterCreatorList.length) {
        resolve([]);
        return;
      }
      const formatBody = {
        invitation: {
          creator_id_list: filterCreatorList.map((o) => ({
            base_info: {
              creator_id: null,
              creator_oec_id: o?.creator_oecuid?.value || o?.id,
              nick_name: o?.nickname?.value,
              user_name: o?.handle?.value || o?.username,
            },
            status: 2,
          })),
          product_list: productList,
        },
      };

      fetch(`${origin}/api/v1/oec/affiliate/seller/invitation_group/conflict_check?${tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery')}`, {
        "headers": {
          "accept": "*/*",
          "accept-language": "zh-CN,zh;q=0.9,en;q=0.8",
          "cache-control": "no-cache",
          "content-type": "application/json",
          "pragma": "no-cache",
          "priority": "u=1, i",
          "sec-ch-ua": "\"Google Chrome\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "\"macOS\"",
          "sec-fetch-dest": "empty",
          "sec-fetch-mode": "cors",
          "sec-fetch-site": "same-origin"
        },
        "referrerPolicy": "strict-origin-when-cross-origin",
        "method": "POST",
        "mode": "cors",
        "credentials": "include",
        body: JSON.stringify(formatBody),
      }).then((response) => response.json())
        .then(async (result) => {
          if (result.code === 0) {
            if (result?.data?.conflict_list?.length) {
              const conflictCreatorIdList = [];
              for (const item of result?.data?.conflict_list) {
                conflictCreatorIdList.push(...(item?.creator_id_list?.map((o => o.base_info.creator_oec_id)) || []))
              }
              await sleep(Math.random() * 3000 + 3000);
              const filterList = filterCreatorList.filter((o) => !conflictCreatorIdList.includes(o?.creator_oecuid?.value || o?.id));
              if (filterList.length) {
                if (type === 'tagCreator') {
                  sendTagInvitationLog(`${conflictCreatorIdList.length}位达人已存在邀约记录已去重`);
                } else {
                  sendInvitationLog(`${conflictCreatorIdList.length}位达人已存在邀约记录已去重`);
                }
                filterCreators(filterList);
              } else {
                resolve([]);
              }
            } else {
              resolve(filterCreatorList)
            }
          } else {
            console.log('filterCreators nothing found')
            if (type === 'tagCreator') {
              sendTagInvitationLog(`去重达人异常，可尝试手动发起邀约看是否有拖动验证码，10秒后重试去重`);
              setTimeout(() => {
                resolve([]);
              }, 10000);
            } else {
              sendInvitationLog(`去重达人异常，可尝试手动发起邀约看是否有拖动验证码，10秒后重试去重`);
              setTimeout(() => {
                resolve([]);
              }, 10000);
            }
          }
        })
        .catch(() => {
          console.log('filterCreators error')
          if (type === 'tagCreator') {
            sendTagInvitationLog(`去重达人异常，可尝试手动发起邀约看是否有拖动验证码，10秒后重试去重`);
            setTimeout(() => {
              resolve([]);
            }, 10000);
          } else {
            sendInvitationLog(`去重达人异常，可尝试手动发起邀约看是否有拖动验证码，10秒后重试去重`);
            setTimeout(() => {
              resolve([]);
            }, 10000);
          }
        });
    }

    filterCreators(creatorList);
  });
};


const getNextPageFillInvitationCreatorList = async ({filters, query, invitation_group}) => {
  if (fetchFillInvitationPagination.search_key && !fetchFillInvitationPagination?.has_more) {
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'sendNextPageFillInvitationCreatorList',
      data: [],
    }, '*');
    return;
  }

  const fetchBody = {
    query: query || "",
    pagination: fetchFillInvitationPagination,
    filter_params: filters.filterParams,
    algorithm: 1,
  }

  let fetchCreatorList;
  let nextPage;

  if (fetchFillInvitationPagination.page === 0) {
    let {
      list,
      next
    } = await fetchFirstPageTkCreatorList(fetchBody);
    fetchCreatorList = list;
    nextPage = next;
  } else {
    let {
      list,
      next
    } = await fetchNotFirstPageTkCreatorList(fetchBody, fetchFillInvitationPagination);
    fetchCreatorList = list;
    nextPage = next;
  }

  console.log('fetchCreatorList', fetchCreatorList, nextPage);

  const cleanCreatorList = await getCleanInvitationCreatorList(fetchCreatorList, invitation_group.product_list);

  if (nextPage) {
    fetchFillInvitationPagination = {
      next_item_cursor: nextPage.next_item_cursor,
      page: nextPage.next_page,
      search_key: nextPage.search_key,
      has_more: nextPage.has_more,
      size: 12,
    }
  }

  if (cleanCreatorList.length) {
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'sendNextPageFillInvitationCreatorList',
      data: cleanCreatorList || [],
    }, '*');
  } else {
    await sleep(6000);
    getNextPageFillInvitationCreatorList({ filters, query, invitation_group });
  }
}

const getDiggingCreatorNextPageCreatorList = async ({ filters, query }) => {
  if (fetchDiggingCreatorNextPage?.search_key && !fetchDiggingCreatorNextPage.has_more) {
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'sendDiggingCreatorNextPageCreatorList',
      data: [],
    }, '*');
    return;
  }

  const fetchBody = {
    query: query || "",
    pagination: fetchDiggingCreatorNextPage,
    filter_params: filters.filterParams,
    algorithm: 1,
  }

  let fetchCreatorList;
  let nextPage;

  if (fetchDiggingCreatorNextPage.page === 0) {
    let {
      list,
      next
    } = await fetchFirstPageTkCreatorList(fetchBody);
    fetchCreatorList = list;
    nextPage = next;
  } else {
    let {
      list,
      next
    } = await fetchNotFirstPageTkCreatorList(fetchBody, fetchDiggingCreatorNextPage);
    fetchCreatorList = list;
    nextPage = next;
  }

  console.log('fetchCreatorList', fetchCreatorList, nextPage);

  if (nextPage) {
    fetchDiggingCreatorNextPage = {
      next_item_cursor: nextPage.next_item_cursor,
      page: nextPage.next_page,
      search_key: nextPage.search_key,
      has_more: nextPage.has_more,
      size: 12,
    }
  }

  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendDiggingCreatorNextPageCreatorList',
    data: fetchCreatorList || [],
  }, '*');
}

const getSimilarCreatorListById = async ({ id }) => {
  const fetchBody = {
    query: id,
    pagination: { size: 20, page: 0 },
    query_type: 1000,
  }

  let { list } = await fetchFirstPageTkCreatorList(fetchBody);

  console.log('fetchFirstPageTkCreatorList', list);

  window.postMessage({
    type: 'easyLinkMessage',
    name: 'sendSimilarCreatorListById',
    data: list || [],
  }, '*');
}

const getGroupFilterCreatorList = async (maxCount, creatorListFilters, invitation_group, query, algorithm) => {
  if (fetchInvitationPagination?.search_key && !fetchInvitationPagination.has_more) {
    return [];
  }

  const fetchBody = {
    query: query || "",
    pagination: fetchInvitationPagination,
    filter_params: creatorListFilters.filterParams,
    algorithm,
  }

  console.log('fetchBody', fetchBody, fetchInvitationPagination, maxCount);

  return new Promise(async (resolve) => {
    while (inviteCreatorList.length < maxCount) {
      if (fetchInvitationPagination?.search_key && !fetchInvitationPagination.has_more) {
        break;
      }
      let fetchCreatorList;
      let nextPage;
      if (fetchInvitationPagination.page === 0) {
        let {
          list,
          next
        } = await fetchFirstPageTkCreatorList(fetchBody, 'invitation');
        fetchCreatorList = list;
        nextPage = next;
      } else {
        let {
          list,
          next
        } = await fetchNotFirstPageTkCreatorList(fetchBody, fetchInvitationPagination, 'invitation');
        fetchCreatorList = list;
        nextPage = next;
      }

      console.log(fetchInvitationPagination, fetchCreatorList, nextPage);
      sendInvitationLog(`获取${fetchCreatorList.length}位达人`);

      if (nextPage) {
        fetchInvitationPagination = {
          next_item_cursor: nextPage.next_item_cursor,
          page: nextPage.next_page,
          search_key: nextPage.search_key,
          has_more: nextPage.has_more,
          size: 12,
        }
      }

      if (!fetchCreatorList.length) {
        const stepCount = Math.floor(Math.random() * 150 + 150);
        await simulateMouseFromRandom(stepCount);
        continue;
      }

      // localStorage.setItem('localFetchInvitationPagination', JSON.stringify(fetchInvitationPagination));
      sendInvitationLog(`去重达人中...`);
      let cleanCreatorList = []
      if (fetchCreatorList.length) {
        cleanCreatorList = await getCleanInvitationCreatorList(fetchCreatorList, invitation_group.product_list);
      }
      sendInvitationLog(`去重后挖掘${cleanCreatorList.length}位达人`, { creatorList: cleanCreatorList.map((o) => ({avatar: o.avatar?.value?.thumb_url_list?.[0], username: o.handle?.value, id: o.creator_oecuid?.value }))});
      console.log('cleanCreatorList', cleanCreatorList);
      inviteCreatorList = [...inviteCreatorList, ...cleanCreatorList];

      const stepCount = Math.floor(Math.random() * 150 + 150);
      await simulateMouseFromRandom(stepCount);
    }

    let cleanInviteCreatorList = [];
    if (inviteCreatorList.length) {
      cleanInviteCreatorList = await getCleanInvitationCreatorList(inviteCreatorList.splice(0, 50), invitation_group.product_list)
    }
    resolve(cleanInviteCreatorList);
  });
}

const getGroupPickCreatorList = async (creatorList, invitation_group) => {

  let cleanGroupCreatorList = [];

  while (creatorList.length && cleanGroupCreatorList.length < 50) {
    const needCreatorCount = 50 - cleanGroupCreatorList.length;
    const needCreatorList = creatorList.splice(0, needCreatorCount);
    const groupCreatorList = [...cleanGroupCreatorList, ...needCreatorList];
    sendInvitationLog(`获取${needCreatorList.length}位达人，去重中...`);
    cleanGroupCreatorList = await getCleanInvitationCreatorList(groupCreatorList, invitation_group.product_list);
    sendInvitationLog(`去重后剩余${cleanGroupCreatorList.length}位达人`);
  }

  sendInvitationLog(`去重后共获取${cleanGroupCreatorList.length}位达人`);

  return cleanGroupCreatorList;
}

const sendInvitation2Creators = (invitation_group, currentInviteCreatorList) => {
  return new Promise((resolve) => {
    const time = new Date().valueOf().toString();
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'easyLinkInviteCreator',
      data: {
        invitation_group: {
          ...invitation_group,
          name: `e-${invitation_group.name}`,
          creator_id_list: [...new Map(currentInviteCreatorList.map(item => [item?.creator_oecuid?.value || item?.id, item])).values()].map((o) => ({
            base_info: {
              creator_id: "",
              creator_oec_id: o?.creator_oecuid?.value || o?.id,
              nick_name: "",
            },
          }))
        },
        time,
      }
    }, '*');

    let timer = setInterval(() => {
      const res = invitationDoneResMap[time];
      if (res) {
        clearInterval(timer);
        resolve(res);
      }
    }, 1000);
  });
}

const algorithmList = [1, 18, 20, 26, 24, 22];

const turnOnEasyLinkInviteFilterListRobot = async (easyLinkInviteRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateInviteRobotConfig',
    data: easyLinkInviteRobotConfig,
  }, '*');

  const {
    currentTemplate: { 
      invitation_group,
    },
    creatorListFilters,
    query,
    deepSeek,
  } = easyLinkInviteRobotConfig;

  let currentQuery = query.shift() || '';
  let algorithm = algorithmList[Math.floor(Math.random() * algorithmList.length)];
  localStorage.setItem('easyLinkInviteRobotConfig', JSON.stringify(easyLinkInviteRobotConfig));

  sendInvitationLog(`开始邀约...`);
  if (deepSeek) {
    sendInvitationLog(`深度挖掘邀约关键词【${currentQuery}】达人`);
  }
  while (easyLinkInviteRobotConfig.progressNumber < easyLinkInviteRobotConfig.inviteNumber) {
    if (deepSeek && fetchInvitationPagination.page >= 100) {
      currentQuery = query.shift() || '';
      sendInvitationLog(`切换下一个关键词【${currentQuery}】邀约`);
      fetchInvitationPagination = { size: 12, page: 0 }
      algorithm = algorithmList[Math.floor(Math.random() * algorithmList.length)];
      continue;
    }
    const restCount = easyLinkInviteRobotConfig.inviteNumber - easyLinkInviteRobotConfig.progressNumber;
    const currentGetCreatorGroupCount = restCount >= 50 ? 50 : restCount;
    const creatorList = await getGroupFilterCreatorList(currentGetCreatorGroupCount, creatorListFilters, invitation_group, currentQuery, algorithm);
    console.log('creatorList', creatorList)
    if (creatorList.length) {
      sendInvitationLog(`已挖掘${creatorList.length}位达人`, { creatorList: creatorList.map((o) => ({avatar: o.avatar?.value?.thumb_url_list?.[0], username: o.handle?.value, id: o.creator_oecuid?.value }))});
    } else {
      if (deepSeek && query.length) {
        sendInvitationLog(`当前关键词【${currentQuery}】已过滤出所有达人`);
        currentQuery = query.shift() || '';
        sendInvitationLog(`切换下一个关键词【${currentQuery}】邀约`);
        fetchInvitationPagination = { size: 12, page: 0 }
        algorithm = algorithmList[Math.floor(Math.random() * algorithmList.length)];
        continue;
      } else {
        fetchInvitationPagination = { size: 12, page: 0 }
        algorithm = algorithmList[Math.floor(Math.random() * algorithmList.length)];
        continue;
        // sendInvitationLog(`当前过滤器已过滤出所有达人，请选择其他过滤器重新邀约`);
        // break;
      }
    }

    if (deepSeek && query.length && creatorList.length < currentGetCreatorGroupCount) {
      continue;
    }

    const currentInviteCreatorList = creatorList.splice(0, currentGetCreatorGroupCount);
    sendInvitationLog(`创建邀约中...`);
    const res = await sendInvitation2Creators(invitation_group, currentInviteCreatorList);

    if (res?.data?.invitation?.id) {
      sendInvitationLog(`邀约成功，邀约ID：${res?.data?.invitation?.id}`);
    } else {
      if (res.code === 50001703) {
        sendInvitationLog(`邀约失败，检查邀约信息敏感词汇`);
        break;
      } else if (res.code === 16024019) {
        sendInvitationLog(`邀约失败，邀约触达店铺日上限`);
        break;
      } else {
        sendInvitationLog(`邀约失败：${res?.message}`);
        continue;
      }
    }

    easyLinkInviteRobotConfig.progressNumber = easyLinkInviteRobotConfig.progressNumber + currentInviteCreatorList.length;

    localStorage.setItem('easyLinkInviteRobotConfig', JSON.stringify(easyLinkInviteRobotConfig));
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'updateInviteRobotConfig',
      data: easyLinkInviteRobotConfig,
    }, '*');
    const stepCount = Math.floor(Math.random() * 150 + 150);
    await simulateMouseFromRandom(stepCount);
  }

  localStorage.setItem('easyLinkInviteRobotConfig', '');
  // localStorage.setItem('localFetchInvitationPagination', '');
  fetchInvitationPagination = { size: 12, page: 0 }
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'inviteRobotDoneProcess',
    data: easyLinkInviteRobotConfig,
  }, '*');
}

const turnOnEasyLinkInvitePickListRobot = async (easyLinkInviteRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateInviteRobotConfig',
    data: easyLinkInviteRobotConfig,
  }, '*');

  const {
    currentTemplate: { 
      invitation_group,
    },
    creatorList,
    creatorListFilters,
    type,
  } = easyLinkInviteRobotConfig;

  while (creatorList.length) {
    const groupCreatorList = await getGroupPickCreatorList(creatorList, invitation_group, type);
   
    if (!groupCreatorList.length) {
      sendInvitationLog(`去重后达人个数为0`);
      break;
    }
    sendInvitationLog(`创建邀约中...`);
    const res = await sendInvitation2Creators(invitation_group, groupCreatorList);

    if (res?.data?.invitation?.id) {
      sendInvitationLog(`邀约成功，邀约ID：${res?.data?.invitation?.id}`);
    } else {
      if (res.code === 50001703) {
        sendInvitationLog(`邀约失败，检查邀约信息敏感词汇`);
      } else if (res.code === 16024019) {
        sendInvitationLog(`邀约失败，邀约触达店铺日上限`);
      } else {
        sendInvitationLog(`邀约失败：${res?.message}`);
      }
      continue;
    }

    easyLinkInviteRobotConfig.progressNumber = easyLinkInviteRobotConfig.progressNumber + groupCreatorList.length;

    localStorage.setItem('easyLinkInviteRobotConfig', JSON.stringify(easyLinkInviteRobotConfig));
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'updateInviteRobotConfig',
      data: easyLinkInviteRobotConfig,
    }, '*');
  }

  localStorage.setItem('easyLinkInviteRobotConfig', '');
  // localStorage.setItem('localFetchInvitationPagination', '');
  fetchInvitationPagination = { size: 12, page: 0 }
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'inviteRobotDoneProcess',
    data: easyLinkInviteRobotConfig,
  }, '*');
}

const fetchTagSearchCreatorList = (tags, pagination, type) => {
  return new Promise((resolve) => {
    fetch(
        `${origin}/api/v1/oec/affiliate/crm/creator/list?${tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery')}`,
        {
          method: "POST",
          headers: {
            "content-type": "application/json",
            "user-agent": navigator.userAgent,
            cookie: document.cookie,
          },
          body: JSON.stringify({
            ...pagination,
            sorter: {},
            filter: {
              range_filters: [],
              single_filters: [],
              multi_filters: tags.length ? [{
                filter_type: 1,
                ids: tags.map((o) => o.value),
              }] : [],
            }
          }),
        }
      )
        .then((response) => response.json())
        .then((result) => {
          if (result.code === 0 && result.data?.creators?.length) {
            resolve(result.data?.creators?.filter((o) => o?.im?.status === 1)?.map((o) => ({ avatar: o.base?.avatar?.thumb_url_list?.[0], username: o.base?.handle_name, id: o.base?.oec_id })));
          } else if (result.code === 0) {
            resolve([]);
          } else {
            if (type === 'message') {
              sendTagMessageLog(result.message || `达人列表请求异常`);
            } else if (type === 'invite') {
              sendTagInvitationLog(result.message || `达人列表请求异常`);
            }
            resolve([]);
          }
        })
        .catch(() => {
           if (type === 'message') {
              sendTagMessageLog('达人列表请求失败');
            } else if (type === 'invite') {
              sendTagInvitationLog(`达人列表请求失败`);
            }
            resolve([]);
        });
  })
}

const getGroupTagCreatorList = async (tags, pagination, invitation_group, tagCreatorList) => {
  let cleanGroupCreatorList = [];

  while (cleanGroupCreatorList.length < 50) {
    if (!tagCreatorList.length) {
      const newList = await fetchTagSearchCreatorList(tags, pagination, 'invite');
      pagination.page_no = pagination.page_no + 1;

      if (newList.length) {
        tagCreatorList.push(...newList)
      } else {
        break;
      }
    }

    const needCreatorCount = 50 - cleanGroupCreatorList.length;
    const needCreatorList = tagCreatorList.splice(0, needCreatorCount);
    const groupCreatorList = [...cleanGroupCreatorList, ...needCreatorList];
    sendTagInvitationLog(`获取${needCreatorList.length}位达人，去重中...`);
    cleanGroupCreatorList = await getCleanInvitationCreatorList(groupCreatorList, invitation_group.product_list, 'tagCreator');
    sendTagInvitationLog(`去重后剩余${cleanGroupCreatorList.length}位达人`);
  }

  sendTagInvitationLog(`去重后共获取${cleanGroupCreatorList.length}位达人`);

  return cleanGroupCreatorList;
}

const turnOnEasyLinkInviteTagCreator = async (easyLinkInviteTagCreatorConfig) => {

  const {
    currentTemplate: { 
      invitation_group,
    },
    tags,
  } = easyLinkInviteTagCreatorConfig;

  const pagination = {
    page_no: 1,
    page_size: 100,
  }
  let tagCreatorList = [];

  sendTagInvitationLog(`开始邀约...`);
  while (true) {
    const groupCreatorList = await getGroupTagCreatorList(tags, pagination, invitation_group, tagCreatorList);

    if (!groupCreatorList.length) {
      sendTagInvitationLog(`去重后达人个数为0`);
      break;
    }
    sendTagInvitationLog(`创建邀约中...`);
    const res = await sendInvitation2Creators(invitation_group, groupCreatorList);

    if (res?.data?.invitation?.id) {
      sendTagInvitationLog(`邀约成功，邀约ID：${res?.data?.invitation?.id}`);
    } else {
      if (res.code === 50001703) {
        sendTagInvitationLog(`邀约失败，检查邀约信息敏感词汇`);
      } else if (res.code === 16024019) {
        sendTagInvitationLog(`邀约失败，邀约触达店铺日上限`);
      } else {
        sendTagInvitationLog(`邀约失败：${res?.message}`);
      }
      continue;
    }

    easyLinkInviteTagCreatorConfig.progressNumber = easyLinkInviteTagCreatorConfig.progressNumber + groupCreatorList.length;

    window.postMessage({
      type: 'easyLinkMessage',
      name: 'updateInviteTagCreatorConfig',
      data: easyLinkInviteTagCreatorConfig,
    }, '*');
  }

  window.postMessage({
    type: 'easyLinkMessage',
    name: 'inviteTagCreatorConfigDone',
    data: easyLinkInviteTagCreatorConfig,
  }, '*');
}

const turnOnEasyLinkMessageTagCreator = async (easyLinkMessageTagCreatorConfig) => {

  const querySearch = new URLSearchParams(tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery'));
  const shopId = querySearch.get('oec_seller_id');

  const {
    currentTemplate,
    tags,
  } = easyLinkMessageTagCreatorConfig;

  console.log('easyLinkMessageTagCreatorConfig', easyLinkMessageTagCreatorConfig);
  sendTagMessageLog(`开始私信...`);

  const pagination = {
    page_no: 1,
    page_size: 100,
  }

  while (true) {
    const newList = await fetchTagSearchCreatorList(tags, pagination, 'message');
    pagination.page_no = pagination.page_no + 1;
    if (newList.length) {
      for (const creator of newList) {
        let res = await sendMessage2Creator(creator, shopId, currentTemplate, sendTagMessageLog);
        if (res.code === 'timeout') {
          sendTagMessageLog(`私信达人「${creator?.handle?.value || creator?.username}」超时，重试中...`);
          res = await sendMessage2Creator(creator, shopId, currentTemplate, sendTagMessageLog);
        }
        if (res.success) {
          easyLinkMessageTagCreatorConfig.progressNumber = easyLinkMessageTagCreatorConfig.progressNumber + 1;
          window.postMessage({
            type: 'easyLinkMessage',
            name: 'updateMessageTagCreatorConfig',
            data: easyLinkMessageTagCreatorConfig,
          }, '*');
        } else {
          sendTagMessageLog(res.msg);
        }
      }
    } else {
      break;
    }
  }

  sendTagMessageLog(`私信完成~`);

  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageTagCreatorConfigDone',
    data: easyLinkMessageTagCreatorConfig,
  }, '*');
}

const turnOnEasyLinkFastMessageTagCreator = async (easyLinkMessageTagCreatorConfig) => {
  const {
    currentTemplate,
    tags,
  } = easyLinkMessageTagCreatorConfig;

  console.log('easyLinkMessageTagCreatorConfig', easyLinkMessageTagCreatorConfig);
  sendTagMessageLog(`开始私信...`);

  const pagination = {
    page_no: 1,
    page_size: 100,
  }

  while (true) {
    const newList = await fetchTagSearchCreatorList(tags, pagination, 'message');
    pagination.page_no = pagination.page_no + 1;
    console.log('newList', newList);
    if (newList.length) {
      sendTagMessageLog(`获取 ${newList.length} 位达人，准备批量发信...`);
      const res = await batchSendTTMessage({
        title: currentTemplate.title,
        content: currentTemplate.content,
        type: currentTemplate.type,
        productCards: currentTemplate.products.map((o) => o.product_id),
        creatorList: newList.map((o) => o.id),
        imgUri: currentTemplate.photos?.[0]?.key,
      });

      if (res?.data?.risk_materials?.length) {
        sendTagMessageLog(`您的标题或者信息中包含违反TK政策的单词或短语。请修改后再发送`);
        break;
      }

      if (res?.data?.quota_limit) {
        sendTagMessageLog(`已触达店铺发送站内信上限，请稍后重试`);
        break;
      }

      let errorList = [];

      if (res?.data?.fail_map) {
        for (const creatorId in res?.data?.fail_map) {
          const creatorInfo = newList.find((o) => o?.id === creatorId);
          creatorInfo && errorList.push({
            username: creatorInfo?.username,
            id: creatorInfo?.id,
            errorInfo: getErrorText(res?.data?.fail_map[creatorId]),
          });
        }
      }

      console.log('message res', res);
      console.log('errorList', errorList);

      if (errorList.length) {
        sendTagMessageLog(`批量发送完成，成功私信${newList.length - errorList.length}人，其中${errorList.length}人由于店铺限制无法触达`);
      } else {
        sendTagMessageLog(`批量发送完成，成功私信${newList.length}人`);
      }

      easyLinkMessageTagCreatorConfig.progressNumber = (easyLinkMessageTagCreatorConfig.progressNumber + newList.length - errorList.length);

      window.postMessage({
        type: 'easyLinkMessage',
        name: 'updateMessageTagCreatorConfig',
        data: easyLinkMessageTagCreatorConfig,
      }, '*');
      await sleep((8 + Math.random() * 5) * 1000);
    } else {
      break;
    }
  }

  sendTagMessageLog(`私信完成~`);

  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageTagCreatorConfigDone',
    data: easyLinkMessageTagCreatorConfig,
  }, '*');
}

const sendMessage2Creator = async (creator, shopId, currentTemplate, sendLog) => {
  console.log('creator', creator);
  sendLog(`私信达人「${creator?.handle?.value || creator?.username}」中...`);
  let startTime = new Date().valueOf();
  return new Promise((resolve) => {
    var easyLinkMessageIframe = document.createElement('iframe');
    easyLinkMessageIframe.src = `${origin}/seller/im?shop_id=${shopId}&creator_id=${creator?.creator_oecuid?.value || creator?.id}&username=${creator.handle?.value || creator?.username}&enter_from=affiliate_creator_details`;  // 目标URL
    easyLinkMessageIframe.width = '100%';
    easyLinkMessageIframe.height = '600';
    easyLinkMessageIframe.id = 'easyLinkMessageIframe';
    easyLinkMessageIframe.style.position = 'fixed'; 
    easyLinkMessageIframe.style.zIndex = '100000'; 
    easyLinkMessageIframe.style.top = '0'; 
    easyLinkMessageIframe.style.left = '200%'; 
    document.body.appendChild(easyLinkMessageIframe);

    easyLinkMessageIframe.addEventListener('load', async () => {
      const easyLinkMessageIframeDocument = easyLinkMessageIframe.contentDocument || easyLinkMessageIframe.contentWindow.document;
      console.log('message init');
      const res = await turnOnMessageOneCreatorRobot(currentTemplate, easyLinkMessageIframeDocument, creator, shopId);

      if (res.success) {
        sendLog(`私信达人「${creator?.handle?.value || creator?.username}」成功`)
      } else {
        sendLog(`私信达人「${creator?.handle?.value || creator?.username}」失败：${res.msg}`)
      }
      clearInterval(timer);
      easyLinkMessageIframe.remove();
      resolve(res);
    });

    // const { messageList } = currentTemplate;
    let delayTime = 60;

    // if (messageList.length > 2) {
    //   delayTime += (messageList.length - 2) * 15;
    // }

    let timer = setInterval(() => {
      const currentTime = new Date().valueOf();
      if (currentTime - startTime >= delayTime * 1000) {
        clearInterval(timer);
        easyLinkMessageIframe.remove();
        resolve({success: false, msg: `私信达人「${creator?.handle?.value || creator?.username}」超时，跳过此达人`, code: 'timeout'});
      }
    }, 2000);
  });
}

const turnOnEasyLinkFilterMessageRobot = async (easyLinkMessageRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateMessageRobotConfig',
    data: easyLinkMessageRobotConfig,
  }, '*');

  const querySearch = new URLSearchParams(tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery'));
  const shopId = querySearch.get('oec_seller_id');

  const {
    currentTemplate,
    creatorListFilters,
    query,
  } = easyLinkMessageRobotConfig;
  console.log('easyLinkMessageRobotConfig', easyLinkMessageRobotConfig);
  sendMessageLog(`开始私信...`);

  while (easyLinkMessageRobotConfig.progressNumber < easyLinkMessageRobotConfig.messageNumber) {
    if (fetchMessagePagination.search_key && !fetchMessagePagination.has_more) {
      sendMessageLog(`当前过滤器已过滤出所有达人，请选择其他过滤器重新发私信`);
      break;
    }
    const fetchBody = {
      query: query || "",
      pagination: fetchMessagePagination,
      filter_params: creatorListFilters.filterParams,
      algorithm: 1,
    }

    let fetchCreatorList;
    let nextPage;

    if (fetchMessagePagination.page === 0) {
      let {
        list,
        next
      } = await fetchFirstPageTkCreatorList(fetchBody, 'message');
      fetchCreatorList = list;
      nextPage = next;
    } else {
      let {
        list,
        next
      } = await fetchNotFirstPageTkCreatorList(fetchBody, fetchMessagePagination, 'message');
      fetchCreatorList = list;
      nextPage = next;
    }

    console.log('fetchCreatorList', fetchCreatorList, nextPage);

    sendMessageLog(`已挖掘${fetchCreatorList.length}位达人`, { creatorList: fetchCreatorList.map((o) => ({avatar: o.avatar?.value?.thumb_url_list?.[0], username: o.handle?.value, id: o.creator_oecuid?.value }))});

    if (nextPage) {
      fetchMessagePagination = {
        next_item_cursor: nextPage.next_item_cursor,
        page: nextPage.next_page,
        search_key: nextPage.search_key,
        has_more: nextPage.has_more,
        size: 12,
      }
    }

    if (!fetchCreatorList.length) {
      const stepCount = Math.floor(Math.random() * 150 + 150);
      await simulateMouseFromRandom(stepCount);
      continue;
    }

    // localStorage.setItem('localFetchMessagePagination', JSON.stringify(fetchMessagePagination));  

    sendMessageLog(`私信中...`);

    for (const creator of fetchCreatorList) {
      let res = await sendMessage2Creator(creator, shopId, currentTemplate, sendMessageLog);
      if (res.code === 'timeout') {
        sendMessageLog(`私信达人「${creator?.handle?.value || creator?.username}」超时，重试中...`);
        res = await sendMessage2Creator(creator, shopId, currentTemplate, sendMessageLog);
      }
      if (res.success) {
        easyLinkMessageRobotConfig.progressNumber = easyLinkMessageRobotConfig.progressNumber + 1;
        localStorage.setItem('easyLinkMessageRobotConfig', JSON.stringify(easyLinkMessageRobotConfig));
        window.postMessage({
          type: 'easyLinkMessage',
          name: 'updateMessageRobotConfig',
          data: easyLinkMessageRobotConfig,
        }, '*');
        if (easyLinkMessageRobotConfig.progressNumber >= easyLinkMessageRobotConfig.messageNumber) {
          break;
        }
      } else {
        sendMessageLog(res.msg);
      }
    }
  }
  sendMessageLog(`私信完成~`);

  localStorage.setItem('easyLinkMessageRobotConfig', '');
  // localStorage.setItem('localFetchMessagePagination', '');
  fetchMessagePagination = { size: 12, page: 0 };
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageRobotDoneProcess',
    data: easyLinkMessageRobotConfig,
  }, '*');
}

const getGroupMessageCreatorList = async (maxCount, creatorListFilters, query) => {
  const fetchBody = {
    query: query || "",
    pagination: fetchMessagePagination,
    filter_params: creatorListFilters.filterParams,
    algorithm: 1,
  }

  console.log('fetchBody', fetchBody, fetchMessagePagination, maxCount);

  return new Promise(async (resolve) => {
    while (messageCreatorList.length < maxCount) {
      if (fetchMessagePagination.search_key && !fetchMessagePagination.has_more) {
        sendMessageLog(`当前过滤器已过滤出所有达人，请选择其他过滤器重新邀约`);
        break;
      }
      let fetchCreatorList;
      let nextPage;
      if (fetchMessagePagination.page === 0) {
        let {
          list,
          next
        } = await fetchFirstPageTkCreatorList(fetchBody, 'message');
        fetchCreatorList = list;
        nextPage = next;
      } else {
        let {
          list,
          next
        } = await fetchNotFirstPageTkCreatorList(fetchBody, fetchMessagePagination, 'message');
        fetchCreatorList = list;
        nextPage = next;
      }

      console.log(fetchMessagePagination, fetchCreatorList, nextPage);
      sendMessageLog(`获取${fetchCreatorList.length}位达人`, { creatorList: fetchCreatorList.map((o) => ({avatar: o.avatar?.value?.thumb_url_list?.[0], username: o.handle?.value, id: o.creator_oecuid?.value }))});

      if (nextPage) {
        fetchMessagePagination = {
          next_item_cursor: nextPage.next_item_cursor,
          page: nextPage.next_page,
          search_key: nextPage.search_key,
          has_more: nextPage.has_more,
          size: 12,
        }
      }

      if (!fetchCreatorList.length) {
        const stepCount = Math.floor(Math.random() * 150 + 150);
        await simulateMouseFromRandom(stepCount);
        continue;
      }

      // localStorage.setItem('localFetchMessagePagination', JSON.stringify(fetchMessagePagination));

      messageCreatorList = [...messageCreatorList, ...fetchCreatorList];

      await sleep(Math.random() * 2000 + 2000);
    }

    resolve(messageCreatorList);
  });
}

const batchSendTTMessage = ({
  content,
  title,
  productCards,
  type,
  creatorList,
  imgUri,
}) => {
  return new Promise((resolve) => {
    fetch(
      `${origin}/api/v1/oec/affiliate/crm/im_messages/batch_send?${tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery')}`,
      {
        method: "POST",
        headers: {
          "user-agent": navigator.userAgent,
          "content-type": "application/json",
        },
        body: JSON.stringify({
          msg: {
            content,
            ...(type === 2
              ? { product_ids: productCards }
              : { image_uri: imgUri }),
            title,
          },
          msg_type: type,
          oec_ids: creatorList,
        }),
        redirect: "follow",
      }
    )
      .then((response) => response.json())
      .then((result) => {
        resolve(result);
      });
  });
};

const getErrorText = (info) => {
  if (info.failed_code === 4) {
    return "5封信未回复";
  } else {
    return `店铺限制达人无法触达，错误码：${info.failed_code}`;
  }
};

const turnOnEasyLinkFastMessageRobot = async (easyLinkMessageRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateMessageRobotConfig',
    data: easyLinkMessageRobotConfig,
  }, '*');

  const {
    currentTemplate,
    creatorListFilters,
    query,
  } = easyLinkMessageRobotConfig;
  console.log('easyLinkMessageRobotConfig', easyLinkMessageRobotConfig);
  sendMessageLog(`开始私信...`);

  while (easyLinkMessageRobotConfig.progressNumber < easyLinkMessageRobotConfig.messageNumber) {
    const restCount = easyLinkMessageRobotConfig.messageNumber - easyLinkMessageRobotConfig.progressNumber;
    const currentGetCreatorGroupCount = restCount >= 100 ? 100 : restCount;
    const creatorList = await getGroupMessageCreatorList(currentGetCreatorGroupCount, creatorListFilters, query);
    console.log('creatorList', creatorList)
    if (creatorList.length) {
      sendMessageLog(`已挖掘${creatorList.length}位达人`, { creatorList: creatorList.map((o) => ({avatar: o.avatar?.value?.thumb_url_list?.[0], username: o.handle?.value, id: o.creator_oecuid?.value }))});
    } else {
      sendMessageLog(`当前过滤器已过滤出所有达人，请选择其他过滤器重新邀约`);
      break;
    }

    const currentFastMessageList = creatorList.splice(0, currentGetCreatorGroupCount);

    const res = await batchSendTTMessage({
      title: currentTemplate.title,
      content: currentTemplate.content,
      type: currentTemplate.type,
      productCards: currentTemplate.products.map((o) => o.product_id),
      creatorList: currentFastMessageList.map((o) => o?.creator_oecuid?.value),
      imgUri: currentTemplate.photos?.[0]?.key,
    });

    if (res?.data?.risk_materials?.length) {
      sendMessageLog(`您的标题或者信息中包含违反TK政策的单词或短语。请修改后再发送`);
      break;
    }

    if (res?.data?.quota_limit) {
      sendMessageLog(`已触达店铺发送站内信上限，请稍后重试`);
      break;
    }

    let errorList = [];

    if (res?.data?.fail_map) {
      for (const creatorId in res?.data?.fail_map) {
        const creatorInfo = currentFastMessageList.find((o) => o?.creator_oecuid?.value === creatorId);
        creatorInfo && errorList.push({
          username: creatorInfo?.handle?.value,
          id: creatorInfo?.creator_oecuid?.value,
          errorInfo: getErrorText(res?.data?.fail_map[creatorId]),
        });
      }
    }

    console.log('message res', res);
    console.log('errorList', errorList);

    if (errorList.length) {
      sendMessageLog(`批量发送完成，成功私信${currentFastMessageList.length - errorList.length}人，其中${errorList.length}人由于店铺限制无法触达`);
    } else {
      sendMessageLog(`批量发送完成，成功私信${currentFastMessageList.length}人`);
    }

    easyLinkMessageRobotConfig.progressNumber = (easyLinkMessageRobotConfig.progressNumber + currentFastMessageList.length - errorList.length);
    localStorage.setItem('easyLinkMessageRobotConfig', JSON.stringify(easyLinkMessageRobotConfig));
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'updateMessageRobotConfig',
      data: easyLinkMessageRobotConfig,
    }, '*');

    await sleep((8 + Math.random() * 5) * 1000);
  }

  sendMessageLog(`私信完成`);

  localStorage.setItem('easyLinkMessageRobotConfig', '');
  // localStorage.setItem('localFetchMessagePagination', '');
  fetchMessagePagination = { size: 12, page: 0 };
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageRobotDoneProcess',
    data: easyLinkMessageRobotConfig,
  }, '*');
}

const turnOnEasyLinkFastPickMessageRobot = async (easyLinkMessageRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateMessageRobotConfig',
    data: easyLinkMessageRobotConfig,
  }, '*');

  const {
    creatorList,
    currentTemplate,
  } = easyLinkMessageRobotConfig;
  console.log('easyLinkMessageRobotConfig', creatorList, easyLinkMessageRobotConfig);
  sendMessageLog(`开始私信...`);

  while (creatorList.length) {
    const currentFastMessageList = creatorList.splice(0, 100);

    const res = await batchSendTTMessage({
      title: currentTemplate.title,
      content: currentTemplate.content,
      type: currentTemplate.type,
      productCards: currentTemplate.products.map((o) => o.product_id),
      creatorList: currentFastMessageList.map((o) => o.id),
      imgUri: currentTemplate.photos?.[0]?.key,
    });

    if (res?.data?.risk_materials?.length) {
      sendMessageLog(`您的标题或者信息中包含违反TK政策的单词或短语。请修改后再发送`);
      break;
    }

    if (res?.data?.quota_limit) {
      sendMessageLog(`已触达店铺发送站内信上限，请稍后重试`);
      break;
    }

    let errorList = [];

    if (res?.data?.fail_map) {
      for (const creatorId in res?.data?.fail_map) {
        const creatorInfo = currentFastMessageList.find((o) => o?.id === creatorId);
        creatorInfo && errorList.push({
          username: creatorInfo?.username,
          id: creatorInfo?.id,
          errorInfo: getErrorText(res?.data?.fail_map[creatorId]),
        });
      }
    }

    console.log('message res', res);
    console.log('errorList', errorList);

    if (errorList.length) {
      sendMessageLog(`批量发送完成，成功私信${currentFastMessageList.length - errorList.length}人，其中${errorList.length}人由于店铺限制无法触达`);
    } else {
      sendMessageLog(`批量发送完成，成功私信${currentFastMessageList.length}人`);
    }

    easyLinkMessageRobotConfig.progressNumber = (easyLinkMessageRobotConfig.progressNumber + currentFastMessageList.length - errorList.length);
    localStorage.setItem('easyLinkMessageRobotConfig', JSON.stringify(easyLinkMessageRobotConfig));
    window.postMessage({
      type: 'easyLinkMessage',
      name: 'updateMessageRobotConfig',
      data: easyLinkMessageRobotConfig,
    }, '*');

    await sleep((8 + Math.random() * 5) * 1000);

    if (!creatorList.length) {
      break;
    }
  }

  sendMessageLog(`私信完成~`);

  localStorage.setItem('easyLinkMessageRobotConfig', '');
  // localStorage.setItem('localFetchMessagePagination', '');
  fetchMessagePagination = { size: 12, page: 0 };
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageRobotDoneProcess',
    data: easyLinkMessageRobotConfig,
  }, '*');
}

const turnOnEasyLinkPickMessageRobot = async (easyLinkMessageRobotConfig) => {
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'updateMessageRobotConfig',
    data: easyLinkMessageRobotConfig,
  }, '*');

  const querySearch = new URLSearchParams(tiktokListInfo.query || localStorage.getItem('easyLinkTiktokInfoListQuery'));
  const shopId = querySearch.get('oec_seller_id');

  const {
    creatorList,
    currentTemplate,
    creatorListFilters,
  } = easyLinkMessageRobotConfig;
  console.log('easyLinkMessageRobotConfig', creatorList, easyLinkMessageRobotConfig);
  sendMessageLog(`开始私信...`);

  while (creatorList.length) {
    const creator = creatorList.shift();
    let res =  await sendMessage2Creator(creator, shopId, currentTemplate, sendMessageLog);
    if (res.code === 'timeout') {
      sendMessageLog(`私信达人「${creator?.handle?.value || creator?.username}」超时，重试中...`);
      res = await sendMessage2Creator(creator, shopId, currentTemplate, sendMessageLog);
    }
    if (res.success) {
      easyLinkMessageRobotConfig.progressNumber = easyLinkMessageRobotConfig.progressNumber + 1;
      localStorage.setItem('easyLinkMessageRobotConfig', JSON.stringify(easyLinkMessageRobotConfig));
      window.postMessage({
        type: 'easyLinkMessage',
        name: 'updateMessageRobotConfig',
        data: easyLinkMessageRobotConfig,
      }, '*');
    } else {
      sendMessageLog(res.msg);
    }

    if (!creatorList.length) {
      break;
    }

  }

  sendMessageLog(`私信完成~`);

  localStorage.setItem('easyLinkMessageRobotConfig', '');
  // localStorage.setItem('localFetchMessagePagination', '');
  fetchMessagePagination = { size: 12, page: 0 };
  window.postMessage({
    type: 'easyLinkMessage',
    name: 'messageRobotDoneProcess',
    data: easyLinkMessageRobotConfig,
  }, '*');
}

window.addEventListener("message", (e) => {
  if (e.data.type === 'easyLinkMessage') {
    if (e.data.name === 'startAutoInviteCreators') {
      const easyLinkInviteRobotConfig = {
        ...e.data.data,
        progressNumber: 0,
        startTime: new Date().valueOf(),
      }

      const url = new URL(window.location.href);
      url.searchParams.set('elrt', 'i');    // 添加或修改参数
      history.pushState({}, '', url);

      localStorage.setItem('easyLinkInviteRobotConfig', JSON.stringify(easyLinkInviteRobotConfig));
      console.log('easyLinkInviteRobotConfig', easyLinkInviteRobotConfig)
      if (e.data.data.type === 'filter') {
        turnOnEasyLinkInviteFilterListRobot(easyLinkInviteRobotConfig);
      } else if (e.data.data.type === 'pick') {
        turnOnEasyLinkInvitePickListRobot(easyLinkInviteRobotConfig);
      } else if (e.data.data.type === 'similar') {
        turnOnEasyLinkInvitePickListRobot(easyLinkInviteRobotConfig);
      } else if (e.data.data.type === 'import') {
        turnOnEasyLinkInvitePickListRobot(easyLinkInviteRobotConfig);
      }
    } else if (e.data.name === 'inviteCreatorByTags') {
      turnOnEasyLinkInviteTagCreator(e.data.data);
    } else if (e.data.name === 'messageCreatorByTags') {
      if (e.data.data.fast) {
        turnOnEasyLinkFastMessageTagCreator(e.data.data);
      } else {
        turnOnEasyLinkMessageTagCreator(e.data.data);
      }
    } else if (e.data.name === 'startAutoMessageCreators') {
      const easyLinkMessageRobotConfig = {
        ...e.data.data,
        progressNumber: 0,
        startTime: new Date().valueOf(),
      }

      const url = new URL(window.location.href);
      url.searchParams.set('elrt', 'm');    // 添加或修改参数
      history.pushState({}, '', url); 

      localStorage.setItem('easyLinkMessageRobotConfig', JSON.stringify(easyLinkMessageRobotConfig));

      console.log('easyLinkMessageRobotConfig', easyLinkMessageRobotConfig);
      if (easyLinkMessageRobotConfig.fast && easyLinkMessageRobotConfig.type === 'filter') {
        turnOnEasyLinkFastMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.fast && easyLinkMessageRobotConfig.type === 'pick') {
        turnOnEasyLinkFastPickMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.fast && easyLinkMessageRobotConfig.type === 'similar') {
        turnOnEasyLinkFastPickMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.fast && easyLinkMessageRobotConfig.type === 'import') {
        turnOnEasyLinkFastPickMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.type === 'filter') {
        turnOnEasyLinkFilterMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.type === 'pick') {
        turnOnEasyLinkPickMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.type === 'similar') {
        turnOnEasyLinkPickMessageRobot(easyLinkMessageRobotConfig);
      } else if (easyLinkMessageRobotConfig.type === 'import') {
        turnOnEasyLinkPickMessageRobot(easyLinkMessageRobotConfig);
      }
    } else if (e.data.name === 'sendInvitation2CreatorsDone') {
      invitationDoneResMap[e.data.data.time] = e.data.data.res;
    } else if (e.data.name === 'getNextPageFillInvitationCreatorList') {
      getNextPageFillInvitationCreatorList(e.data.data);
    } else if (e.data.name === 'getDiggingCreatorNextPageCreatorList') {
      getDiggingCreatorNextPageCreatorList(e.data.data);
    } else if (e.data.name === 'getSimilarCreatorListById') {
      getSimilarCreatorListById(e.data.data);
    }else if (e.data.name === 'updateGlobalShopData') {
      console.log('updateGlobalShopData', e.data.data);
      globalShopData = e.data.data;
    } else if (e.data.name === 'cancelInvitationProcess') {
      localStorage.setItem('easyLinkInviteRobotConfig', '');
      // localStorage.setItem('localFetchInvitationPagination', '');
      fetchInvitationPagination = { size: 12, page: 0 }
      window.location.reload();
    } else if (e.data.name === 'cancelMessageProcess') {
      localStorage.setItem('easyLinkMessageRobotConfig', '');
      // localStorage.setItem('localFetchMessagePagination', '');
      fetchMessagePagination = { size: 12, page: 0 };
      window.location.reload();
    } else if (e.data.name === 'mainRootInitDone') {
      const url = new URL(window.location.href);

      try {
        const localEasyLinkInviteRobotConfigString = localStorage.getItem('easyLinkInviteRobotConfig');

        if (localEasyLinkInviteRobotConfigString && url.searchParams.get('elrt') === 'i') {
          const localEasyLinkInviteRobotConfig = JSON.parse(localEasyLinkInviteRobotConfigString);
          console.log('localEasyLinkInviteRobotConfig', localEasyLinkInviteRobotConfig);
          if (localEasyLinkInviteRobotConfig.type === 'filter') {
            turnOnEasyLinkInviteFilterListRobot(localEasyLinkInviteRobotConfig);
          } else if (localEasyLinkInviteRobotConfig.type === 'pick') {
            turnOnEasyLinkInvitePickListRobot(localEasyLinkInviteRobotConfig);
          } else if (localEasyLinkInviteRobotConfig.type === 'similar') {
            turnOnEasyLinkInvitePickListRobot(localEasyLinkInviteRobotConfig);
          } else if (localEasyLinkInviteRobotConfig.type === 'import') {
            turnOnEasyLinkInvitePickListRobot(localEasyLinkInviteRobotConfig);
          }
        }


        const localEasyLinkMessageRobotConfigString = localStorage.getItem('easyLinkMessageRobotConfig');

        if (localEasyLinkMessageRobotConfigString && url.searchParams.get('elrt') === 'm') {
          const localEasyLinkMessageRobotConfig = JSON.parse(localEasyLinkMessageRobotConfigString);
          console.log('localEasyLinkMessageRobotConfig', localEasyLinkMessageRobotConfig)
          if (localEasyLinkMessageRobotConfig.fast && localEasyLinkMessageRobotConfig.type === 'filter') {
            turnOnEasyLinkFastMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.fast && localEasyLinkMessageRobotConfig.type === 'pick') {
            turnOnEasyLinkFastPickMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.fast && localEasyLinkMessageRobotConfig.type === 'similar') {
            turnOnEasyLinkFastPickMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.fast && localEasyLinkMessageRobotConfig.type === 'import') {
            turnOnEasyLinkFastPickMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.type === 'filter') {
            turnOnEasyLinkFilterMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.type === 'pick') {
            turnOnEasyLinkPickMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.type === 'similar') {
            turnOnEasyLinkPickMessageRobot(localEasyLinkMessageRobotConfig);
          } else if (localEasyLinkMessageRobotConfig.type === 'import') {
            turnOnEasyLinkPickMessageRobot(localEasyLinkMessageRobotConfig);
          }
        }
      } catch (error) {

      }
    }
  }
});

const checkElementInit = (selector, currentDoc) => {
  return new Promise((resolve) => {
    let timer = setInterval(() => {
      const im_sdk_chat_input = currentDoc.querySelector(selector);
      if (im_sdk_chat_input) {
        clearInterval(timer);
        resolve(im_sdk_chat_input);
      }
    }, 1000);
  });
}

function clickElement(element) {
  try {
    const fk = Object.keys(element).find(key => 
      key.startsWith('__reactFiber') || 
      key.startsWith('_reactFiber')
    );

    if (fk) {
      const f = element[fk];
      console.log('f', f.memoizedProps);
      if (f && f.memoizedProps && f.memoizedProps.onClick) {
        f.memoizedProps.onClick({});
        return true;
      }
      if (f && f.onClick) {
        f.onClick({});
        return true;
      }
    }

    const pk = Object.keys(element).find(key => 
      key.startsWith('__reactProps') || 
      key.startsWith('_reactProps')
    );

    if (pk) {
      const p = element[pk];
      console.log('p', p);
      if (p && p.onClick) {
        p.onClick({});
        return true;
      }
    }
  } catch (e) {
    console.log('click error:', e);
    return false;
  }
  return false;
}


function onChangeElement(element, v) {
  try {
    const fk = Object.keys(element).find(key => 
      key.startsWith('__reactFiber') || 
      key.startsWith('_reactFiber')
    );

    if (fk) {
      const f = element[fk];
      if (f && f.memoizedProps && f.memoizedProps.onChange) {
        f.memoizedProps.onChange({ target: { value: v, } });
        return true;
      }
    }

    const pk = Object.keys(element).find(key => 
      key.startsWith('__reactProps') || 
      key.startsWith('_reactProps')
    );


    if (pk) {
      const p = element[pk];
      if (p && p.onChange) {
        p.onChange({ target: { value: v, } });
        return true;
      }
    }
  } catch (e) {
    console.log('click error:', e);
    return false;
  }
  return false;
}

const clickProductMessageSendBtn = async (productId, currentDoc) => {
  return new Promise((resolve) => {
    let count = 0;
    let timer = setInterval(() => {
      if (count >= 10) {
        clearInterval(timer);
      }
      count++;
      const productList = Array.from(currentDoc.querySelectorAll('div[aria-labelledby="arco-tabs-0-tab-1"] .text-body-m-regular'));
      const currentProductDom = productList.find((o) => o.innerText.includes(productId));
      if (currentProductDom) {
        clearInterval(timer);
        clickElement(currentProductDom.nextElementSibling);
        resolve();
      }
    }, 2000)
  })
}

async function sendProductMessage(value, productTitle, currentDoc) {
  console.log("check tab Init")
  await checkElementInit('#arco-tabs-0-tab-1', currentDoc);
  await sleep(1000);

  await new Promise((resolve) => {
    let count = 0;
    let timer = setInterval(async () => {
      if (count >= 10) {
        clearInterval(timer);
      }
      count++;
      clickElement(currentDoc.querySelector('#arco-tabs-0-tab-1'));
      await sleep(1000);
      const searchInput = currentDoc.querySelector('div[aria-labelledby="arco-tabs-0-tab-1"] input[data-tid="m4b_input_search"]');
      if (searchInput) {
        onChangeElement(searchInput, productTitle);
        await sleep(1000);
        clickElement(currentDoc.querySelector('div[aria-labelledby="arco-tabs-0-tab-1"] div[data-tid="m4b_input_group"] .arco-icon-search'));
        clearInterval(timer);
        resolve();
      }
    }, 2000);
  })

  await sleep(1000);
  console.log("click product send")
  await clickProductMessageSendBtn(value, currentDoc);

  await sleep(2000);
}

async function fetchImageAsBlob(url) {
  try {
    const response = await fetch(url, {
      mode: "cors",
      cache: "no-cache",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const blob = await response.blob();
    return blob;
  } catch (error) {
    console.error('fetchImageAsBlob error', error);
    return null;
  }
}

async function waitForPastedImageModalAndConfirm(currentDoc) {
  let count = 0;
  return new Promise((resolve) => {
    let timer = setInterval(async () => {
      if (count >= 10) {
        clearInterval(timer);
      }
      count++
      const okBtn = currentDoc.querySelector('.m4b-modal-footer button.arco-btn-primary');
      if (okBtn) {
        clickElement(okBtn);
        clearInterval(timer);
        await sleep(2000);
        let innerCount = 0;
        let innerTimer = setInterval(async () => {
          if (innerCount >= 15) {
            clearInterval(innerTimer);
          }
          innerCount++;
          if (!currentDoc.querySelector('.chatd-message-status-content--pending')) {
            clearInterval(innerTimer);
            await sleep(2000);
            resolve();
          }
        }, 1000);
      }
    }, 1000);
  });
}

async function sendImgMessage(url, currentDoc) {
  const imageBlob = await fetchImageAsBlob(url);
  
  if (imageBlob) {
    const imageFile = new File([imageBlob], "image.png", {
      type: "image/png",
    });

    let chatTextarea = currentDoc.querySelector('#im_sdk_chat_input textarea');

    if (chatTextarea) {
      chatTextarea.dispatchEvent(new Event("input", { bubbles: true }));

      const fileInput = currentDoc.querySelector('input[type="file"]');
      if (fileInput) {
        console.log("handle input file");
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(imageFile);
        fileInput.files = dataTransfer.files;
        fileInput.dispatchEvent(new Event("change", { bubbles: true }));
        await waitForPastedImageModalAndConfirm(currentDoc);
      } else {
        console.log("handle copy Paste");
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(imageFile);

        try {
          const pasteEvent = new ClipboardEvent("paste", {
            bubbles: true,
            cancelable: true,
            clipboardData: dataTransfer,
          });

          chatTextarea.dispatchEvent(pasteEvent);

          await waitForPastedImageModalAndConfirm(currentDoc);
        } catch (e) {
          console.error('past img error', e);
        }
      }
    }
  }
}

async function sendTextMessage(value, currentDoc) {
  onChangeElement(currentDoc.querySelector('#im_sdk_chat_input textarea'), value);
  await sleep(2000);

  return new Promise((resolve) => {
    let count = 0;
    let timer = setInterval(async () => {
      if (count >= 10) {
        clearInterval(timer);
      }
      count++;
      if (currentDoc.querySelector('#im_sdk_chat_input textarea').value) {
        clickElement(currentDoc.querySelector('#im_sdk_chat_input div[class^="send-"] button'));
        await sleep(2000);
      }

      const messageList = Array.from(currentDoc.querySelectorAll('.chatd-bubble--self pre')).map(o => o.innerText);

      if (messageList.find((v) => v === value)) {
        clearInterval(timer);
        resolve();
      }
    }, 2000);
  })
}

async function turnOnMessageOneCreatorRobot(currentTemplate, currentDoc, creator, shopId) {
  return new Promise(async (resolve) => {
    await checkElementInit('#im_sdk_chat_input textarea', currentDoc);
    await sleep(3000);
  
    const { duplicateRule, messageList } = currentTemplate;
    const username = creator?.handle?.value || creator?.username;
    const preMessageList = Array.from(currentDoc.querySelectorAll('.chatd-message--right .chatd-message-body-info-message'));
  
    console.log(duplicateRule, messageList);
  
    if (duplicateRule === 'doNotSend' && preMessageList.length) {
      resolve({ success: false, msg: `达人 ${username} 已沟通过，跳过此达人`, code: '20340528' });
      return;
    }

    clickElement(currentDoc.querySelector('.chatd-scrollView-content'));

    for (const messageItem of messageList) {
      if (messageItem.type === 'text') {
        await sendTextMessage(messageItem.value.replaceAll('{{creators username}}', username), currentDoc);
      } else if (messageItem.type === 'productCard') {
        await sendProductMessage(messageItem.value, messageItem.extra?.title, currentDoc);
      } else if (messageItem.type === 'img') {
        await sendImgMessage(messageItem.value, currentDoc);
      }
    }
  
    resolve({ success: true, msg: '' });
  });
}
