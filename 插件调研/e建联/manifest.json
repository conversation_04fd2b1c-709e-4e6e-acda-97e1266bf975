{"background": {"service_worker": "service-worker-loader.js", "type": "module"}, "content_scripts": [{"css": ["assets/main.84d0bfc4.css"], "js": ["assets/main.tsx-loader.ee4b1253.js"], "matches": ["https://affiliate-us.tiktok.com/*", "https://affiliate.tiktok.com/*", "https://affiliate.tiktokglobalshop.com/*", "https://mail.google.com/*", "https://seller-vn.tiktok.com/*", "https://seller-br.tiktok.com/*", "https://seller-de.tiktok.com/*", "https://seller-fr.tiktok.com/*", "https://seller-it.tiktok.com/*", "https://seller-ie.tiktok.com/*", "https://seller-mx.tiktok.com/*", "https://seller-es.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-us.tiktok.com/*", "https://seller.us.tiktokglobalshop.com/*", "https://seller-id.tokopedia.com/*", "https://seller-sg.tiktok.com/*", "https://affiliate-id.tokopedia.com/*", "https://seller-my.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://accounts.google.com/*", "https://partner.us.tiktokshop.com/*", "https://seller.tiktokglobalshop.com/*"], "media": [], "run_at": "document_start"}], "host_permissions": ["https://www.easylinktiktok.com/*"], "icons": {"128": "logo128.png", "16": "logo16.png", "32": "logo32.png", "48": "logo48.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAk5QUhlnJXHEZLTJ4KG5r1X8LcC9hC7mmd6CSgthEWecumAYgdfICpcROynLq9BpXOv7E6fREnlz+ybdTQa67XblozgVQfdNc8O/uNVX1SKjLwkw0BGGbi2uMhK/pbA4F+hAw4Yl2a5Mk2D7znyetiDY3G7Mgfv2ggZoPiuBKqWx9rzwzqRdUSPonKRizIxINQ/d2PxtbRZFJK8Zm63vorqz3gXFtDZEeKBnBNNxPI+vhFLGdWG90vRaZ8VO/UdufNz9U42xKYL+hDkQIZFXYUIIN3YmRqTHWWGXRW90wzGT+CGlCXD5rpoHXU5ZiLhMdMZqxTaJM8aR7H797eU+UpwIDAQAB", "manifest_version": 3, "name": "e建联EasyLink TikTok达人建联智能插件", "permissions": ["storage"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "********", "web_accessible_resources": [{"matches": ["https://accounts.google.com/*", "https://affiliate-id.tokopedia.com/*", "https://affiliate-us.tiktok.com/*", "https://affiliate.tiktok.com/*", "https://affiliate.tiktokglobalshop.com/*", "https://mail.google.com/*", "https://partner.us.tiktokshop.com/*", "https://seller-br.tiktok.com/*", "https://seller-de.tiktok.com/*", "https://seller-es.tiktok.com/*", "https://seller-fr.tiktok.com/*", "https://seller-id.tokopedia.com/*", "https://seller-ie.tiktok.com/*", "https://seller-it.tiktok.com/*", "https://seller-mx.tiktok.com/*", "https://seller-my.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-sg.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-us.tiktok.com/*", "https://seller-vn.tiktok.com/*", "https://seller.tiktokglobalshop.com/*", "https://seller.us.tiktokglobalshop.com/*", "https://www.kalodata.com/*"], "resources": ["**/*", "*"], "use_dynamic_url": false}, {"matches": ["https://accounts.google.com/*", "https://affiliate-id.tokopedia.com/*", "https://affiliate-us.tiktok.com/*", "https://affiliate.tiktok.com/*", "https://affiliate.tiktokglobalshop.com/*", "https://mail.google.com/*", "https://partner.us.tiktokshop.com/*", "https://seller-br.tiktok.com/*", "https://seller-de.tiktok.com/*", "https://seller-es.tiktok.com/*", "https://seller-fr.tiktok.com/*", "https://seller-id.tokopedia.com/*", "https://seller-ie.tiktok.com/*", "https://seller-it.tiktok.com/*", "https://seller-mx.tiktok.com/*", "https://seller-my.tiktok.com/*", "https://seller-ph.tiktok.com/*", "https://seller-sg.tiktok.com/*", "https://seller-th.tiktok.com/*", "https://seller-uk.tiktok.com/*", "https://seller-us.tiktok.com/*", "https://seller-vn.tiktok.com/*", "https://seller.tiktokglobalshop.com/*", "https://seller.us.tiktokglobalshop.com/*"], "resources": ["assets/jsx-runtime.57502d4a.js", "assets/lodash.a9edce51.js", "assets/_commonjsHelpers.b8add541.js", "assets/main.tsx.27affb5d.js"], "use_dynamic_url": false}]}