<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI侧边栏库 - 使用示例</title>
    
    <!-- 引入必要的CSS -->
    <link rel="stylesheet" href="https://cdn.staticfile.net/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="style-enhanced.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .demo-section {
            margin: 30px 0;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 6px;
        }
        
        .demo-section h2 {
            color: #3b82f6;
            margin-bottom: 15px;
        }
        
        .code-block {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .code-block pre {
            margin: 0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .demo-buttons {
            margin: 20px 0;
        }
        
        .demo-buttons button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .demo-buttons button:hover {
            background: #2563eb;
            transform: translateY(-1px);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            padding-left: 25px;
            position: relative;
        }
        
        .feature-list li:before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }
        
        .api-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .api-table th,
        .api-table td {
            border: 1px solid #e5e7eb;
            padding: 10px;
            text-align: left;
        }
        
        .api-table th {
            background: #f3f4f6;
            font-weight: 600;
        }
        
        .api-table code {
            background: #e5e7eb;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 13px;
        }
        
        .sidebar-demo-container {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            height: 600px;
            overflow: hidden;
            position: relative;
            background: white;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>AI助手侧边栏交互库</h1>
        <p>一个功能丰富、易于使用的侧边栏交互库，支持项目管理、对话管理、拖拽操作等功能。</p>
        
        <!-- 功能特性 -->
        <div class="demo-section">
            <h2>📋 功能特性</h2>
            <ul class="feature-list">
                <li>项目管理：创建、编辑、删除项目</li>
                <li>对话管理：创建、编辑、删除、移动对话</li>
                <li>拖拽操作：支持对话在项目间的拖拽移动</li>
                <li>内联编辑：双击或点击编辑按钮即可编辑名称</li>
                <li>UI反馈：操作提示、工具提示等</li>
                <li>高亮管理：自动管理元素高亮状态</li>
                <li>回调函数：支持各种操作的回调</li>
                <li>国际化：支持自定义文本</li>
                <li>灵活配置：高度可定制的配置选项</li>
            </ul>
        </div>
        
        <!-- 快速开始 -->
        <div class="demo-section">
            <h2>🚀 快速开始</h2>
            
            <h3>1. 引入库文件</h3>
            <div class="code-block">
                <pre>&lt;!-- 引入侧边栏库 --&gt;
&lt;script src="sidebar-library.js"&gt;&lt;/script&gt;</pre>
            </div>
            
            <h3>2. 初始化</h3>
            <div class="code-block">
                <pre>// 创建侧边栏实例
const sidebar = new AISidebar({
    // 自定义配置（可选）
    features: {
        dragAndDrop: true,
        inlineEdit: true
    },
    callbacks: {
        onProjectCreate: function(data) {
            console.log('项目创建:', data);
        }
    }
});

// 初始化
sidebar.init();</pre>
            </div>
        </div>
        
        <!-- 配置选项 -->
        <div class="demo-section">
            <h2>⚙️ 配置选项</h2>
            
            <h3>选择器配置</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>选项</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>selectors.projectsList</code></td>
                        <td>String</td>
                        <td>#projects-list</td>
                        <td>项目列表容器选择器</td>
                    </tr>
                    <tr>
                        <td><code>selectors.uncategorizedArea</code></td>
                        <td>String</td>
                        <td>#uncategorized-conversations</td>
                        <td>未归类对话区域选择器</td>
                    </tr>
                    <tr>
                        <td><code>selectors.newProjectIcon</code></td>
                        <td>String</td>
                        <td>#new-project-icon</td>
                        <td>新建项目按钮选择器</td>
                    </tr>
                    <tr>
                        <td><code>selectors.newConversationIcon</code></td>
                        <td>String</td>
                        <td>#new-conversation-icon</td>
                        <td>新建对话按钮选择器</td>
                    </tr>
                </tbody>
            </table>
            
            <h3>功能开关</h3>
            <table class="api-table">
                <thead>
                    <tr>
                        <th>选项</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>features.dragAndDrop</code></td>
                        <td>Boolean</td>
                        <td>true</td>
                        <td>是否启用拖拽功能</td>
                    </tr>
                    <tr>
                        <td><code>features.inlineEdit</code></td>
                        <td>Boolean</td>
                        <td>true</td>
                        <td>是否启用内联编辑</td>
                    </tr>
                    <tr>
                        <td><code>features.tooltips</code></td>
                        <td>Boolean</td>
                        <td>true</td>
                        <td>是否启用工具提示</td>
                    </tr>
                    <tr>
                        <td><code>features.animations</code></td>
                        <td>Boolean</td>
                        <td>true</td>
                        <td>是否启用动画效果</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- API 方法 -->
        <div class="demo-section">
            <h2>📚 API 方法</h2>
            
            <h3>项目管理</h3>
            <div class="code-block">
                <pre>// 创建新项目
sidebar.projectManager.create('项目名称');

// 编辑项目（需要传入项目DOM元素）
sidebar.projectManager.edit(projectElement);

// 删除项目
sidebar.projectManager.delete(projectElement);

// 切换项目展开/收缩
sidebar.projectManager.toggle(projectElement);</pre>
            </div>
            
            <h3>对话管理</h3>
            <div class="code-block">
                <pre>// 创建新对话
sidebar.conversationManager.create();

// 编辑对话（需要传入对话DOM元素）
sidebar.conversationManager.edit(conversationElement);

// 删除对话
sidebar.conversationManager.delete(conversationElement);

// 高亮对话
sidebar.conversationManager.highlight(conversationElement);

// 更新未归类对话计数
sidebar.conversationManager.updateCount();</pre>
            </div>
            
            <h3>UI反馈</h3>
            <div class="code-block">
                <pre>// 显示操作提示
sidebar.uiFeedback.showTip('操作成功', 2000);

// 显示工具提示
sidebar.uiFeedback.showTooltip(element, '提示文本');

// 隐藏工具提示
sidebar.uiFeedback.hideTooltip();

// 确认对话框
const confirmed = sidebar.uiFeedback.confirm('确定删除吗？');</pre>
            </div>
            
            <h3>高亮管理</h3>
            <div class="code-block">
                <pre>// 高亮元素
sidebar.highlightManager.highlight(element);

// 高亮AI助手菜单
sidebar.highlightManager.highlightAIAssistant('ai-assistant-menu');

// 清除所有高亮
sidebar.highlightManager.clearAll();</pre>
            </div>
        </div>
        
        <!-- 回调函数 -->
        <div class="demo-section">
            <h2>🔔 回调函数</h2>
            
            <div class="code-block">
                <pre>const sidebar = new AISidebar({
    callbacks: {
        // 项目创建回调
        onProjectCreate: function(data) {
            console.log('项目创建:', data);
            // data = { id, name, element }
        },
        
        // 项目删除回调
        onProjectDelete: function(data) {
            console.log('项目删除:', data);
            // data = { id, name }
        },
        
        // 项目重命名回调
        onProjectRename: function(data) {
            console.log('项目重命名:', data);
            // data = { id, oldName, newName, element }
        },
        
        // 对话创建回调
        onConversationCreate: function(data) {
            console.log('对话创建:', data);
            // data = { timestamp }
        },
        
        // 对话删除回调
        onConversationDelete: function(data) {
            console.log('对话删除:', data);
            // data = { id, title }
        },
        
        // 对话重命名回调
        onConversationRename: function(data) {
            console.log('对话重命名:', data);
            // data = { id, oldTitle, newTitle, element }
        },
        
        // 对话移动回调
        onConversationMove: function(data) {
            console.log('对话移动:', data);
            // data = { conversationId, conversationTitle, targetType, targetId, targetName }
        },
        
        // 元素高亮回调
        onHighlight: function(data) {
            console.log('元素高亮:', data);
            // data = { id, title, element }
        }
    }
});</pre>
            </div>
        </div>
        
        <!-- 自定义文本 -->
        <div class="demo-section">
            <h2>🌐 自定义文本（国际化）</h2>
            
            <div class="code-block">
                <pre>const sidebar = new AISidebar({
    texts: {
        newProjectName: 'New Project',
        newConversationTitle: 'New Conversation',
        confirmProjectDelete: 'Are you sure you want to delete project "{name}"?',
        confirmConversationDelete: 'Are you sure you want to delete conversation "{name}"?',
        emptyNameError: 'Name cannot be empty',
        nameTooLongError: 'Name cannot exceed {max} characters',
        invalidCharsError: 'Name cannot contain: / \\ : * ? " < > |',
        projectCreateSuccess: 'Project created successfully',
        projectRenameSuccess: 'Project renamed successfully',
        conversationCreateSuccess: 'Conversation created successfully',
        conversationRenameSuccess: 'Conversation renamed successfully',
        conversationMoveSuccess: 'Conversation moved successfully'
    }
});</pre>
            </div>
        </div>
        
        <!-- 使用示例 -->
        <div class="demo-section">
            <h2>💡 使用示例</h2>
            
            <h3>完整初始化示例</h3>
            <div class="code-block">
                <pre>// 创建并配置侧边栏
const sidebar = new AISidebar({
    // 选择器配置
    selectors: {
        projectsList: '#my-projects',
        uncategorizedArea: '#my-uncategorized'
    },
    
    // 功能配置
    features: {
        dragAndDrop: true,
        inlineEdit: true,
        tooltips: true
    },
    
    // UI配置
    ui: {
        maxNameLength: 30,
        confirmDelete: true,
        animationDuration: 200
    },
    
    // 回调函数
    callbacks: {
        onProjectCreate: function(data) {
            // 发送到服务器
            fetch('/api/projects', {
                method: 'POST',
                body: JSON.stringify({ name: data.name })
            });
        },
        
        onConversationMove: function(data) {
            // 更新数据库
            updateConversationProject(data.conversationId, data.targetId);
        }
    },
    
    // 自定义文本
    texts: {
        newProjectName: '新建文件夹',
        projectCreateSuccess: '文件夹创建成功'
    }
});

// 初始化
sidebar.init();

// 程序化创建项目
setTimeout(() => {
    sidebar.projectManager.create('我的第一个项目');
}, 1000);</pre>
            </div>
        </div>
        
        <!-- 交互演示 -->
        <div class="demo-section">
            <h2>🎮 交互演示</h2>
            <p>下面的按钮展示了如何通过代码控制侧边栏：</p>
            
            <div class="demo-buttons">
                <button onclick="demoCreateProject()">创建新项目</button>
                <button onclick="demoCreateConversation()">创建新对话</button>
                <button onclick="demoShowTip()">显示提示消息</button>
                <button onclick="demoClearHighlights()">清除所有高亮</button>
                <button onclick="demoToggleFeature()">切换拖拽功能</button>
            </div>
            
            <div class="code-block">
                <pre id="demo-output">// 操作结果将显示在这里...</pre>
            </div>
        </div>
        
        <!-- 注意事项 -->
        <div class="demo-section">
            <h2>⚠️ 注意事项</h2>
            <ul>
                <li>确保HTML结构与库的默认选择器匹配，或通过配置自定义选择器</li>
                <li>需要引入Font Awesome图标库以正常显示图标</li>
                <li>某些功能（如拖拽）需要现代浏览器支持</li>
                <li>建议在DOM加载完成后初始化库</li>
                <li>回调函数中的异步操作不会阻塞UI</li>
            </ul>
        </div>
        
        <!-- 版本信息 -->
        <div class="demo-section">
            <h2>📦 版本信息</h2>
            <p>当前版本：<strong id="version-info">-</strong></p>
            <p>更新日期：2024年1月</p>
            <p>作者：AI Assistant</p>
        </div>
    </div>
    
    <!-- 引入侧边栏库 -->
    <script src="sidebar-library.js"></script>
    
    <!-- 演示脚本 -->
    <script>
        // 初始化侧边栏（用于演示）
        let demoSidebar;
        
        // 等待库加载
        if (typeof AISidebar !== 'undefined') {
            initDemo();
        } else {
            window.addEventListener('load', initDemo);
        }
        
        function initDemo() {
            // 显示版本信息
            document.getElementById('version-info').textContent = AISidebar.version;
            
            // 创建演示实例
            demoSidebar = new AISidebar({
                callbacks: {
                    onProjectCreate: logToDemo,
                    onProjectDelete: logToDemo,
                    onProjectRename: logToDemo,
                    onConversationCreate: logToDemo,
                    onConversationDelete: logToDemo,
                    onConversationRename: logToDemo,
                    onConversationMove: logToDemo,
                    onHighlight: logToDemo
                }
            });
            
            // 注意：演示环境可能没有实际的DOM结构
            // 这里只是展示API用法
        }
        
        // 演示函数
        function demoCreateProject() {
            if (!demoSidebar) {
                logToDemo({ error: '库尚未初始化' });
                return;
            }
            
            try {
                // 模拟创建项目
                logToDemo({ 
                    action: '创建新项目',
                    code: 'sidebar.projectManager.create("演示项目")',
                    note: '注意：演示环境可能缺少必要的DOM结构'
                });
            } catch (e) {
                logToDemo({ error: e.message });
            }
        }
        
        function demoCreateConversation() {
            if (!demoSidebar) {
                logToDemo({ error: '库尚未初始化' });
                return;
            }
            
            try {
                logToDemo({ 
                    action: '创建新对话',
                    code: 'sidebar.conversationManager.create()',
                    result: '新对话不会显示在侧边栏中（按设计）'
                });
            } catch (e) {
                logToDemo({ error: e.message });
            }
        }
        
        function demoShowTip() {
            if (!demoSidebar) {
                logToDemo({ error: '库尚未初始化' });
                return;
            }
            
            demoSidebar.uiFeedback.showTip('这是一个演示提示消息！', 3000);
            logToDemo({ 
                action: '显示提示消息',
                code: 'sidebar.uiFeedback.showTip("这是一个演示提示消息！", 3000)'
            });
        }
        
        function demoClearHighlights() {
            if (!demoSidebar) {
                logToDemo({ error: '库尚未初始化' });
                return;
            }
            
            demoSidebar.highlightManager.clearAll();
            logToDemo({ 
                action: '清除所有高亮',
                code: 'sidebar.highlightManager.clearAll()'
            });
        }
        
        function demoToggleFeature() {
            if (!demoSidebar) {
                logToDemo({ error: '库尚未初始化' });
                return;
            }
            
            demoSidebar.config.features.dragAndDrop = !demoSidebar.config.features.dragAndDrop;
            const status = demoSidebar.config.features.dragAndDrop ? '启用' : '禁用';
            
            logToDemo({ 
                action: '切换拖拽功能',
                code: 'sidebar.config.features.dragAndDrop = !sidebar.config.features.dragAndDrop',
                result: `拖拽功能已${status}`
            });
        }
        
        // 日志输出函数
        function logToDemo(data) {
            const output = document.getElementById('demo-output');
            const timestamp = new Date().toLocaleTimeString();
            
            let message = `// [${timestamp}] `;
            
            if (data.error) {
                message += `错误: ${data.error}`;
            } else if (data.action) {
                message += `\n// ${data.action}\n`;
                if (data.code) message += `${data.code}\n`;
                if (data.result) message += `// 结果: ${data.result}\n`;
                if (data.note) message += `// 注意: ${data.note}\n`;
            } else {
                message += `事件: ${JSON.stringify(data, null, 2)}`;
            }
            
            output.textContent = message + '\n' + output.textContent;
            
            // 限制日志长度
            const lines = output.textContent.split('\n');
            if (lines.length > 20) {
                output.textContent = lines.slice(0, 20).join('\n');
            }
        }
    </script>
</body>
</html>