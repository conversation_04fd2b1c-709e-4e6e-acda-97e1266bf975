<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特征提取结果 - 原型</title>
    <!-- 内部CSS链接 -->
    <link rel="stylesheet" href="./style.css">
    <link rel="stylesheet" href="./style-enhanced.css">
    <link rel="stylesheet" href="./performance-enhanced.css">
    <!-- Remi Icon 图标库 -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.2.0/fonts/remixicon.css" rel="stylesheet" />
    <style>
        body {
            background-color: var(--background-color, #f4f7fa);
            padding: 40px;
            font-family: var(--font-family);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
        }
        #prototype-container {
            width: 100%;
            max-width: 850px;
        }
        /* 强制隐藏主题切换UI */
        .theme-switcher {
            display: none !important;
        }
        
        /* 重写营销活动设置布局 */
        .campaign-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }
        
        /* 移动端响应式 */
        @media (max-width: 768px) {
            .campaign-inputs {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
        
        /* 营销活动设置中的筛选样式 */
        .campaign-label {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-primary, #374151);
            margin-bottom: 8px;
            display: block;
        }
        
        /* 平台筛选标签样式 - 横向排列 */
        .platform-filter-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .platform-tag {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            border: 2px solid var(--border-color, #d1d5db);
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
            color: var(--text-secondary, #6b7280);
            user-select: none;
            flex: 1;
            justify-content: center;
            min-height: 40px;
        }
        
        .platform-tag:hover {
            border-color: var(--primary-color, #3b82f6);
            background: var(--primary-light, #eff6ff);
        }
        
        .platform-tag.selected {
            border-color: var(--primary-color, #3b82f6);
            background: var(--primary-color, #3b82f6);
            color: white;
        }
        
        .platform-tag .check-icon {
            font-size: 14px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .platform-tag.selected .check-icon {
            opacity: 1;
        }
        
        /* 粉丝数范围标签样式 */
        .followers-range-tags {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 6px;
        }
        
        .range-tag {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            padding: 8px 12px;
            border: 2px solid var(--border-color, #d1d5db);
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 13px;
            font-weight: 500;
            color: var(--text-secondary, #6b7280);
            user-select: none;
            min-height: 40px;
            text-align: center;
        }
        
        .range-tag:hover {
            border-color: var(--primary-color, #3b82f6);
            background: var(--primary-light, #eff6ff);
        }
        
        .range-tag.selected {
            border-color: var(--primary-color, #3b82f6);
            background: var(--primary-color, #3b82f6);
            color: white;
        }
        
        .custom-toggle .toggle-icon {
            font-size: 14px;
            transition: transform 0.2s ease;
        }
        
        .custom-toggle.active .toggle-icon {
            transform: rotate(180deg);
        }
        
        /* 自定义输入区域 */
        .custom-range-container {
            margin-top: 8px;
            padding: 12px;
            background: var(--surface-light, #f8f9fa);
            border: 1px solid var(--border-light, #e9ecef);
            border-radius: 8px;
        }
        
        .custom-range-inputs {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }
        
        .custom-range-input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid var(--border-color, #d1d5db);
            border-radius: 6px;
            font-size: 13px;
            background: white;
            height: 36px;
            box-sizing: border-box;
        }
        
        .custom-range-input:focus {
            outline: none;
            border-color: var(--primary-color, #3b82f6);
            box-shadow: 0 0 0 2px var(--primary-light, #eff6ff);
        }
        
        .range-separator {
            font-weight: 600;
            color: var(--text-secondary, #6b7280);
            font-size: 14px;
        }
        
        .apply-custom-btn {
            padding: 8px 12px;
            background: var(--primary-color, #3b82f6);
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .apply-custom-btn:hover {
            background: var(--primary-dark, #2563eb);
        }
        
        .custom-range-hint {
            font-size: 12px;
            color: var(--text-secondary, #6b7280);
            font-style: italic;
        }
        
        /* 移动端优化 */
        @media (max-width: 768px) {
            .followers-range-tags {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
        }
    </style>
</head>
<body>

    <div id="prototype-container">
        <!-- 特征提取卡片将由JS动态生成并插入此处 -->
    </div>

    <!-- 内部JS链接 -->
    <script src="./themes/theme-config.js"></script>
    <script src="./themes/theme-manager.js"></script>
    <script src="./feature_extraction_script.js"></script>
</body>
</html> 