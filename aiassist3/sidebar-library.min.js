/**
 * AI助手侧边栏交互库 v1.0.0
 * 一个功能丰富的侧边栏交互库，支持项目管理、对话管理、拖拽操作等
 */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=t():"function"==typeof define&&define.amd?define(t):e.AISidebar=t()}("undefined"!=typeof window?window:this,function(){"use strict";const e={selectors:{projectsList:"#projects-list",uncategorizedArea:"#uncategorized-conversations",newProjectIcon:"#new-project-icon",newConversationIcon:"#new-conversation-icon",aiAssistantMenu:"#ai-assistant-menu",aiAssistantSubmenu:"#ai-assistant-submenu"},features:{dragAndDrop:!0,inlineEdit:!0,projectManagement:!0,conversationManagement:!0,tooltips:!0,animations:!0},ui:{maxNameLength:20,tooltipDelay:0,animationDuration:300,confirmDelete:!0,autoFocusOnEdit:!0},callbacks:{onProjectCreate:null,onProjectDelete:null,onProjectRename:null,onConversationCreate:null,onConversationDelete:null,onConversationRename:null,onConversationMove:null,onHighlight:null},texts:{newProjectName:"新项目",newConversationTitle:"新对话",confirmProjectDelete:'确定要删除项目"{name}"吗？项目内的对话将移回未归类区域。',confirmConversationDelete:'确定要删除对话"{name}"吗？',emptyNameError:"名称不能为空",nameTooLongError:"名称不能超过{max}个字符",invalidCharsError:"名称不能包含以下字符：/ \\ : * ? \" < > |",projectCreateSuccess:"新建项目成功",projectRenameSuccess:"项目重命名成功",conversationCreateSuccess:"新建对话成功",conversationRenameSuccess:"对话重命名成功",conversationMoveSuccess:"移动对话成功"}},t={deepMerge(e,t){const n=Object.assign({},e);return function e(t){return t&&"object"==typeof t&&!Array.isArray(t)}(e)&&e(t)&&Object.keys(t).forEach(s=>{e(t[s])?s in e?n[s]=this.deepMerge(e[s],t[s]):Object.assign(n,{[s]:t[s]}):Object.assign(n,{[s]:t[s]})}),n},generateId:(e="item")=>`${e}-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,debounce:(e,t)=>{let n;return function(...s){clearTimeout(n),n=setTimeout(()=>{clearTimeout(n),e(...s)},t)}},formatText:(e,t)=>e.replace(/{(\w+)}/g,(e,n)=>t.hasOwnProperty(n)?t[n]:e),validateName(e,t){const n=e.trim();return n.length<1?{valid:!1,error:"empty"}:n.length>t?{valid:!1,error:"tooLong"}:/[\/\\:*?"<>|]/.test(n)?{valid:!1,error:"invalidChars"}:{valid:!0}}},n={showTip(e,t=2e3){const n=document.querySelector(".operation-tip");n&&n.remove();const s=document.createElement("div");s.className="operation-tip",s.textContent=e,document.body.appendChild(s),setTimeout(()=>{s.classList.add("fade-out"),setTimeout(()=>s.remove(),300)},t)},showTooltip(e,t){const n=document.querySelector(".custom-tooltip");n&&n.remove();const s=document.createElement("div");s.className="custom-tooltip",s.textContent=t,document.body.appendChild(s);const o=e.getBoundingClientRect(),i=s.getBoundingClientRect();s.style.left=o.left+o.width/2-i.width/2+"px",s.style.top=o.top-i.height-8+"px",requestAnimationFrame(()=>{s.classList.add("show")})},hideTooltip(){const e=document.querySelector(".custom-tooltip");e&&(e.classList.remove("show"),setTimeout(()=>e.remove(),200))},confirm:e=>window.confirm(e)},s={highlight(e){this.clearAll(),e.classList.add("highlighted")},highlightAIAssistant(e){this.clearAll();const t=document.getElementById(e);if(t){const e=t.querySelector(".menu-item-content");e&&e.classList.add("highlighted")}},clearAll(){document.querySelectorAll(".highlighted").forEach(e=>e.classList.remove("highlighted"))}};class o{constructor(e){this.sidebar=e,this.config=e.config}create(e=null){const s=t.generateId("project"),o=e||this.config.texts.newProjectName,i=document.querySelector(this.config.selectors.projectsList);if(!i)return null;const a=document.createElement("div");return a.className="project-item",a.dataset.projectId=s,a.dataset.lastUsed=Date.now(),a.innerHTML=`\n            <div class="project-header-item">\n                <i class="fas fa-chevron-down text-blue-500"></i>\n                <span class="project-name">${o}</span>\n                <div class="project-actions">\n                    <button class="project-edit-btn" title="重命名">\n                        <i class="fas fa-edit"></i>\n                    </button>\n                    <button class="project-delete-btn" title="删除项目">\n                        <i class="fas fa-trash"></i>\n                    </button>\n                </div>\n            </div>\n            <div class="project-conversations"></div>\n        `,i.insertBefore(a,i.firstChild),this.config.features.dragAndDrop&&this.sidebar.dragDropManager.addProjectListeners(a),this.config.callbacks.onProjectCreate&&this.config.callbacks.onProjectCreate({id:s,name:o,element:a}),n.showTip(this.config.texts.projectCreateSuccess),this.config.features.inlineEdit&&setTimeout(()=>{this.edit(a,!0)},150),a}edit(e,s=!1){if(!this.config.features.inlineEdit)return;const o=e.querySelector(".project-name"),i=o.textContent;if(o.querySelector("input"))return;const a=new c(o,i,this.config.texts.newProjectName,this.config.ui.maxNameLength);a.onConfirm=(s=>{const a=t.validateName(s,this.config.ui.maxNameLength);return!!a.valid&&(s!==i&&(o.textContent=s,this.config.callbacks.onProjectRename&&this.config.callbacks.onProjectRename({id:e.dataset.projectId,oldName:i,newName:s,element:e}),n.showTip(this.config.texts.projectRenameSuccess)),!0)}),a.show(s)}delete(e){const s=e.querySelector(".project-name").textContent,o=t.formatText(this.config.texts.confirmProjectDelete,{name:s});if(!this.config.ui.confirmDelete||n.confirm(o)){const t=e.querySelectorAll(".conversation-item"),n=document.querySelector(this.config.selectors.uncategorizedArea);n&&(t.forEach(e=>{e.className="conversation-item",e.draggable=!0,n.appendChild(e)}),this.sidebar.conversationManager.updateCount()),this.config.callbacks.onProjectDelete&&this.config.callbacks.onProjectDelete({id:e.dataset.projectId,name:s}),e.remove()}}toggle(e){const t=e.querySelector(".project-conversations"),n=e.querySelector(".project-header-item > i");"none"===t.style.display?(t.style.display="block",n.className="fas fa-chevron-down"):(t.style.display="none",n.className="fas fa-chevron-right")}handleValidationError(e){let s="";switch(e){case"empty":s=this.config.texts.emptyNameError;break;case"tooLong":s=t.formatText(this.config.texts.nameTooLongError,{max:this.config.ui.maxNameLength});break;case"invalidChars":s=this.config.texts.invalidCharsError}n.showTip(s)}}class i{constructor(e){this.sidebar=e,this.config=e.config}create(){return s.highlightAIAssistant(this.config.selectors.aiAssistantMenu.replace("#","")),this.config.callbacks.onConversationCreate&&this.config.callbacks.onConversationCreate({timestamp:Date.now()}),n.showTip(this.config.texts.conversationCreateSuccess),null}edit(e){if(!this.config.features.inlineEdit)return;const s=e.querySelector(".conversation-title"),o=s.textContent;if(s.querySelector("input"))return;const i=new c(s,o,this.config.texts.newConversationTitle,this.config.ui.maxNameLength);i.onConfirm=(i=>{const a=t.validateName(i,this.config.ui.maxNameLength);return!!a.valid&&(i!==o&&(s.textContent=i,this.config.callbacks.onConversationRename&&this.config.callbacks.onConversationRename({id:e.dataset.chatId,oldTitle:o,newTitle:i,element:e}),n.showTip(this.config.texts.conversationRenameSuccess)),!0)}),i.show()}delete(e){const s=e.querySelector(".conversation-title").textContent,o=t.formatText(this.config.texts.confirmConversationDelete,{name:s});(!this.config.ui.confirmDelete||n.confirm(o))&&(this.config.callbacks.onConversationDelete&&this.config.callbacks.onConversationDelete({id:e.dataset.chatId,title:s}),e.remove(),this.updateCount())}updateCount(){const e=document.querySelectorAll(`${this.config.selectors.uncategorizedArea} .conversation-item`),t=document.querySelector(".conversation-count");t&&(t.textContent=e.length)}highlight(e){s.highlight(e),this.config.callbacks.onHighlight&&this.config.callbacks.onHighlight({id:e.dataset.chatId,title:e.querySelector(".conversation-title").textContent,element:e}),n.showTip("已选中对话："+e.querySelector(".conversation-title").textContent)}}class a{constructor(e){this.sidebar=e,this.config=e.config,this.draggedItem=null}init(){if(!this.config.features.dragAndDrop)return;document.querySelectorAll('.conversation-item[draggable="true"]').forEach(e=>this.addConversationListeners(e)),document.querySelectorAll(".project-item").forEach(e=>this.addProjectListeners(e));const e=document.querySelector(this.config.selectors.uncategorizedArea);e&&this.addUncategorizedListeners(e)}addConversationListeners(e){e.addEventListener("dragstart",t=>{this.draggedItem=e,t.dataTransfer.setData("text/plain",e.dataset.chatId),e.classList.add("dragging")}),e.addEventListener("dragend",t=>{e.classList.remove("dragging"),this.draggedItem=null})}addProjectListeners(e){e.addEventListener("dragover",t=>{t.preventDefault(),e.classList.add("drag-over")}),e.addEventListener("dragleave",t=>{e.contains(t.relatedTarget)||e.classList.remove("drag-over")}),e.addEventListener("drop",t=>{t.preventDefault(),e.classList.remove("drag-over"),this.draggedItem&&this.moveToProject(this.draggedItem,e)})}addUncategorizedListeners(e){e.addEventListener("dragover",t=>{t.preventDefault(),e.classList.add("drag-over")}),e.addEventListener("dragleave",t=>{e.contains(t.relatedTarget)||e.classList.remove("drag-over")}),e.addEventListener("drop",t=>{t.preventDefault(),e.classList.remove("drag-over"),this.draggedItem&&this.moveToUncategorized(this.draggedItem)})}moveToProject(e,t){const s=t.querySelector(".project-conversations"),o=e.querySelector(".conversation-title").textContent,i=t.querySelector(".project-name").textContent;s.appendChild(e),this.sidebar.conversationManager.updateCount(),this.config.callbacks.onConversationMove&&this.config.callbacks.onConversationMove({conversationId:e.dataset.chatId,conversationTitle:o,targetType:"project",targetId:t.dataset.projectId,targetName:i}),n.showTip(this.config.texts.conversationMoveSuccess)}moveToUncategorized(e){const t=document.querySelector(this.config.selectors.uncategorizedArea),s=e.querySelector(".conversation-title").textContent;t.appendChild(e),this.sidebar.conversationManager.updateCount(),this.config.callbacks.onConversationMove&&this.config.callbacks.onConversationMove({conversationId:e.dataset.chatId,conversationTitle:s,targetType:"uncategorized",targetId:null,targetName:"未归类"}),n.showTip(this.config.texts.conversationMoveSuccess)}}class c{constructor(e,t,n,s){this.element=e,this.currentValue=t,this.placeholder=n,this.maxLength=s,this.onConfirm=null,this.onCancel=null}show(e=!0){this.element.innerHTML=`\n            <div class="inline-edit-container">\n                <input type="text" \n                       class="inline-edit-input" \n                       value="${this.currentValue}" \n                       placeholder="${this.placeholder}" \n                       maxlength="${this.maxLength}">\n                <div class="inline-edit-actions">\n                    <button class="inline-edit-confirm" title="确认">\n                        <i class="fas fa-check"></i>\n                    </button>\n                    <button class="inline-edit-cancel" title="取消">\n                        <i class="fas fa-times"></i>\n                    </button>\n                </div>\n            </div>\n        `;const t=this.element.querySelector(".inline-edit-input"),n=this.element.querySelector(".inline-edit-confirm"),s=this.element.querySelector(".inline-edit-cancel");setTimeout(()=>{t.focus(),e&&t.select()},50);const o=e=>{e&&e.stopPropagation();const n=t.value.trim();this.onConfirm&&!1!==this.onConfirm(n)?this.element.textContent=n||this.currentValue:n||(this.element.textContent=this.currentValue)},i=e=>{e&&e.stopPropagation(),this.element.textContent=this.currentValue,this.onCancel&&this.onCancel()};n.addEventListener("click",o),s.addEventListener("click",i),t.addEventListener("keydown",e=>{"Enter"===e.key&&(e.preventDefault(),o()),"Escape"===e.key&&(e.preventDefault(),i())});let a;t.addEventListener("blur",()=>{a=setTimeout(()=>{document.activeElement&&(document.activeElement.closest(".inline-edit-confirm")||document.activeElement.closest(".inline-edit-cancel"))||o()},200)}),n.addEventListener("mousedown",()=>clearTimeout(a)),s.addEventListener("mousedown",()=>clearTimeout(a))}}return class{constructor(c={}){this.config=t.deepMerge(e,c),this.projectManager=new o(this),this.conversationManager=new i(this),this.dragDropManager=new a(this),this.utils=t,this.uiFeedback=n,this.highlightManager=s}init(){return this.bindEvents(),this.dragDropManager.init(),this.config.features.tooltips&&this.initTooltips(),this}bindEvents(){const e=document.querySelector(this.config.selectors.newProjectIcon);e&&e.addEventListener("click",e=>{e.stopPropagation(),this.projectManager.create()});const t=document.querySelector(this.config.selectors.newConversationIcon);t&&t.addEventListener("click",e=>{e.stopPropagation(),this.conversationManager.create()});const n=document.querySelector(this.config.selectors.aiAssistantMenu);if(n){const e=n.querySelector(".menu-item-content");e&&e.addEventListener("click",e=>{e.preventDefault(),e.stopPropagation(),e.target.closest(".ai-assistant-icons")||this.conversationManager.create()})}const s=document.querySelector(this.config.selectors.projectsList);s&&s.addEventListener("click",e=>{const t=e.target,n=t.closest(".project-item");if(!n)return;if(t.closest(".project-header-item")&&!t.closest(".project-actions"))this.projectManager.toggle(n);else if(t.closest(".project-edit-btn"))e.stopPropagation(),this.projectManager.edit(n);else if(t.closest(".project-delete-btn"))e.stopPropagation(),this.projectManager.delete(n);else if(t.closest(".conversation-item")){const n=t.closest(".conversation-item");t.closest(".conversation-edit-btn")?(e.stopPropagation(),this.conversationManager.edit(n)):t.closest(".conversation-delete-btn")?(e.stopPropagation(),this.conversationManager.delete(n)):t.closest(".conversation-actions")||this.conversationManager.highlight(n)}});const o=document.querySelector(this.config.selectors.uncategorizedArea);o&&o.addEventListener("click",e=>{const t=e.target,n=t.closest(".conversation-item");n&&(t.closest(".conversation-edit-btn")?(e.stopPropagation(),this.conversationManager.edit(n)):t.closest(".conversation-delete-btn")?(e.stopPropagation(),this.conversationManager.delete(n)):t.closest(".conversation-actions")||this.conversationManager.highlight(n))}),document.addEventListener("click",e=>{e.target.classList.contains("inline-edit-input")&&e.stopPropagation()})}initTooltips(){const e=document.querySelector(this.config.selectors.newProjectIcon),t=document.querySelector(this.config.selectors.newConversationIcon);e&&(e.addEventListener("mouseenter",()=>{n.showTooltip(e,"新建项目")}),e.addEventListener("mouseleave",()=>{n.hideTooltip()})),t&&(t.addEventListener("mouseenter",()=>{n.showTooltip(t,"新建对话")}),t.addEventListener("mouseleave",()=>{n.hideTooltip()}))}destroy(){}static get version(){return"1.0.0"}}});